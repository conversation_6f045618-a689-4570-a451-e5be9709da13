{"_comment": "Test configuration file - Unit tests use mocked dependencies and do not connect to real databases or APIs", "Migration": {"Environments": {"US": {"Name": "United States Production", "Description": "US Production Environment", "DatabaseConnection": "", "ApiBaseUrl": "https://us-api.xq360.com/", "ApiUsername": "us-migration-user", "ApiPassword": "YOUR_US_API_PASSWORD"}, "UK": {"Name": "United Kingdom Production", "Description": "UK Production Environment", "DatabaseConnection": "", "ApiBaseUrl": "https://uk-api.xq360.com/", "ApiUsername": "uk-migration-user", "ApiPassword": "YOUR_UK_API_PASSWORD"}, "AU": {"Name": "Australia Production", "Description": "AU Production Environment", "DatabaseConnection": "", "ApiBaseUrl": "https://au-api.xq360.com/", "ApiUsername": "au-migration-user", "ApiPassword": "YOUR_AU_API_PASSWORD"}, "Pilot": {"Name": "Pilot Testing Environment", "Description": "Pilot testing and validation environment", "DatabaseConnection": "", "ApiBaseUrl": "https://godev.collectiveintelligence.com.au/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/", "ApiUsername": "Admin", "ApiPassword": "Admin"}, "Development": {"Name": "Test Environment", "Description": "Unit testing environment - uses mocked connections", "DatabaseConnection": "Server=localhost;Database=UnitTestDb;Integrated Security=true;TrustServerCertificate=true", "ApiBaseUrl": "https://localhost:53052/", "ApiUsername": "TestUser", "ApiPassword": "TestPassword"}}, "BatchSize": 100, "MaxRetryAttempts": 3, "BackupEnabled": true, "ValidateBeforeMigration": true, "ContinueOnError": false}, "BulkSeeder": {"DefaultBatchSize": 1000, "MaxBatchSize": 10000, "TempTableBatchSize": 5000, "CommandTimeoutSeconds": 300, "EnableParallelProcessing": true, "MaxDegreeOfParallelism": 4, "EnableMemoryOptimization": true, "MemoryThresholdMB": 1024, "EnableProgressReporting": true, "ProgressReportIntervalSeconds": 5}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "./Logs/dataseeder-tests-{Date}.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}]}}