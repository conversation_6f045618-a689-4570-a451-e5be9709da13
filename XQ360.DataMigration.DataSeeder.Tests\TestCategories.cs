namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Test categories to help organize and run specific types of tests
    /// </summary>
    public static class TestCategories
    {
        /// <summary>
        /// Unit tests - these should run fast, use mocks for all external dependencies,
        /// and not require any external infrastructure (database, APIs, etc.)
        /// </summary>
        public const string Unit = "Unit";

        /// <summary>
        /// Integration tests - these may require external infrastructure and test
        /// the interaction between components. May use real database connections
        /// or external services.
        /// </summary>
        public const string Integration = "Integration";

        /// <summary>
        /// Performance tests - tests that measure performance characteristics
        /// and may take longer to run
        /// </summary>
        public const string Performance = "Performance";
    }
}
