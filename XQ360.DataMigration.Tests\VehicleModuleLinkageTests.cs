using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Data.SqlClient;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BusinessRules;
using XQ360.DataMigration.Web.Services.BusinessRules.Rules;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Unit tests for vehicle-to-module linkage validation functionality
    /// Tests vehicle creation with proper ModuleId1 assignment, module allocation status updates,
    /// prevention of duplicate module assignments, and validation that modules exist before vehicle creation
    /// </summary>
    public class VehicleModuleLinkageTests : IDisposable
    {
        private readonly Mock<ILogger<ComplexEntityCreationService>> _mockLogger;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockConfig;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly Mock<IBusinessRuleValidationService> _mockBusinessRuleService;
        private readonly BulkSeederConfiguration _testConfig;
        private readonly MigrationConfiguration _testMigrationConfig;

        public VehicleModuleLinkageTests()
        {
            _mockLogger = new Mock<ILogger<ComplexEntityCreationService>>();
            _mockConfig = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();
            _mockBusinessRuleService = new Mock<IBusinessRuleValidationService>();

            _testConfig = new BulkSeederConfiguration
            {
                DefaultBatchSize = 50,
                MaxRetryAttempts = 3,
                RetryDelayMs = 1000
            };

            _testMigrationConfig = new MigrationConfiguration
            {
                DatabaseConnection = "Server=test;Database=test;Integrated Security=true;",
                ApiBaseUrl = "https://test-api.example.com",
                ApiUsername = "testuser",
                ApiPassword = "testpass"
            };

            _mockConfig.Setup(x => x.Value).Returns(_testConfig);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(_testMigrationConfig);
        }

        #region Vehicle Creation with Module Assignment Tests

        [Fact]
        public async Task CreateVehicleBatchAsync_WithValidModuleIoTDevice_ShouldLinkVehicleToModule()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var vehicleRequests = CreateTestVehicleRequestsWithModules(1);

            SetupSuccessfulModuleLookup();
            SetupSuccessfulBusinessRuleValidation();

            // Act
            var result = await service.CreateVehicleBatchAsync(sessionId, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(1, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);

            // Verify business rule validation was called for module assignment
            _mockBusinessRuleService.Verify(x => x.ValidateVehicleModuleAssignmentAsync(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<ValidationContext>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateVehicleBatchAsync_WithNonExistentModule_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var vehicleRequests = CreateTestVehicleRequestsWithModules(1);

            SetupNonExistentModuleLookup();

            // Act
            var result = await service.CreateVehicleBatchAsync(sessionId, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(1, result.FailedRequests);
            Assert.Contains("Module", result.Errors.First());
            Assert.Contains("not found", result.Errors.First());
        }

        [Fact]
        public async Task CreateVehicleBatchAsync_WithAlreadyAllocatedModule_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var vehicleRequests = CreateTestVehicleRequestsWithModules(1);

            SetupAllocatedModuleLookup();

            // Act
            var result = await service.CreateVehicleBatchAsync(sessionId, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(1, result.FailedRequests);
            Assert.Contains("already allocated", result.Errors.First());
        }

        [Fact]
        public async Task CreateVehicleBatchAsync_WithMultipleVehiclesSameModule_ShouldPreventDuplicateAssignment()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var vehicleRequests = new List<VehicleCreateRequest>
            {
                CreateVehicleRequestWithModule("VEH_001", "TEST_IOT_001"),
                CreateVehicleRequestWithModule("VEH_002", "TEST_IOT_001") // Same module
            };

            SetupSuccessfulModuleLookup();
            SetupSuccessfulBusinessRuleValidation();

            // Act
            var result = await service.CreateVehicleBatchAsync(sessionId, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.SuccessfulRequests); // First vehicle should succeed
            Assert.Equal(1, result.FailedRequests); // Second vehicle should fail
            Assert.Contains("already allocated", result.Errors.First());
        }

        [Fact]
        public async Task CreateVehicleBatchAsync_WithoutModuleIoTDevice_ShouldCreateVehicleWithoutModule()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var vehicleRequests = new List<VehicleCreateRequest>
            {
                CreateVehicleRequestWithoutModule("VEH_001")
            };

            // Act
            var result = await service.CreateVehicleBatchAsync(sessionId, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(1, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);

            // Verify no module validation was performed
            _mockBusinessRuleService.Verify(x => x.ValidateVehicleModuleAssignmentAsync(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<ValidationContext>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        #endregion

        #region Module Allocation Status Tests

        [Fact]
        public async Task AllocateModuleToVehicleAsync_WithValidRequest_ShouldUpdateAllocationStatus()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var allocationRequests = new List<ModuleAllocationRequest>
            {
                new ModuleAllocationRequest
                {
                    IoTDevice = "TEST_IOT_001",
                    VehicleId = Guid.NewGuid(),
                    ValidateAvailability = true
                }
            };

            SetupSuccessfulModuleLookup();
            SetupModuleNotAllocated();

            // Act
            var result = await service.AllocateModulesToVehiclesBatchAsync(sessionId, allocationRequests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(1, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);
        }

        [Fact]
        public async Task AllocateModuleToVehicleAsync_WithAlreadyAllocatedModule_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var allocationRequests = new List<ModuleAllocationRequest>
            {
                new ModuleAllocationRequest
                {
                    IoTDevice = "TEST_IOT_001",
                    VehicleId = Guid.NewGuid(),
                    ValidateAvailability = true
                }
            };

            SetupSuccessfulModuleLookup();
            SetupModuleAlreadyAllocated();

            // Act
            var result = await service.AllocateModulesToVehiclesBatchAsync(sessionId, allocationRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(1, result.FailedRequests);
            Assert.Contains("already allocated", result.Errors.First());
        }

        [Fact]
        public async Task AllocateModuleToVehicleAsync_WithoutValidation_ShouldSkipAvailabilityCheck()
        {
            // Arrange
            var service = CreateComplexEntityCreationService();
            var sessionId = Guid.NewGuid();
            var allocationRequests = new List<ModuleAllocationRequest>
            {
                new ModuleAllocationRequest
                {
                    IoTDevice = "TEST_IOT_001",
                    VehicleId = Guid.NewGuid(),
                    ValidateAvailability = false // Skip validation
                }
            };

            SetupSuccessfulModuleLookup();
            // Don't setup allocation check since validation is disabled

            // Act
            var result = await service.AllocateModulesToVehiclesBatchAsync(sessionId, allocationRequests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(1, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);
        }

        #endregion

        #region Business Rule Validation Tests

        [Fact]
        public async Task ValidateVehicleModuleAssignment_WithValidAssignment_ShouldReturnValid()
        {
            // Arrange
            var vehicleId = Guid.NewGuid();
            var moduleId = Guid.NewGuid();

            SetupSuccessfulBusinessRuleValidation();

            // Act
            var result = await _mockBusinessRuleService.Object.ValidateVehicleModuleAssignmentAsync(
                vehicleId, moduleId, new ValidationContext { ConnectionString = _testMigrationConfig.DatabaseConnection });

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsValid);
            Assert.Empty(result.Issues);
        }

        [Fact]
        public async Task ValidateVehicleModuleAssignment_WithInvalidAssignment_ShouldReturnInvalid()
        {
            // Arrange
            var vehicleId = Guid.NewGuid();
            var moduleId = Guid.NewGuid();

            SetupFailedBusinessRuleValidation();

            // Act
            var result = await _mockBusinessRuleService.Object.ValidateVehicleModuleAssignmentAsync(
                vehicleId, moduleId, new ValidationContext { ConnectionString = _testMigrationConfig.DatabaseConnection });

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsValid);
            Assert.NotEmpty(result.Issues);
        }

        #endregion

        #region Helper Methods

        private ComplexEntityCreationService CreateComplexEntityCreationService()
        {
            return new ComplexEntityCreationService(
                _mockLogger.Object,
                _mockConfig.Object,
                _mockEnvironmentService.Object,
                _mockBusinessRuleService.Object);
        }

        private List<VehicleCreateRequest> CreateTestVehicleRequestsWithModules(int count)
        {
            var requests = new List<VehicleCreateRequest>();
            for (int i = 1; i <= count; i++)
            {
                requests.Add(CreateVehicleRequestWithModule($"VEH_{i:D3}", $"TEST_IOT_{i:D3}"));
            }
            return requests;
        }

        private VehicleCreateRequest CreateVehicleRequestWithModule(string serialNo, string iotDeviceId)
        {
            return new VehicleCreateRequest
            {
                SerialNo = serialNo,
                HireNo = $"HIRE_{serialNo}",
                IdleTimer = 300,
                OnHire = true,
                ImpactLockout = false,
                TimeoutEnabled = true,
                IsCanbus = false,
                ModelId = Guid.NewGuid(),
                SiteId = Guid.NewGuid(),
                DepartmentId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                ModuleIoTDevice = iotDeviceId,
                ChecklistType = ChecklistType.TimeBased
            };
        }

        private VehicleCreateRequest CreateVehicleRequestWithoutModule(string serialNo)
        {
            return new VehicleCreateRequest
            {
                SerialNo = serialNo,
                HireNo = $"HIRE_{serialNo}",
                IdleTimer = 300,
                OnHire = true,
                ImpactLockout = false,
                TimeoutEnabled = true,
                IsCanbus = false,
                ModelId = Guid.NewGuid(),
                SiteId = Guid.NewGuid(),
                DepartmentId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                ModuleIoTDevice = null, // No module
                ChecklistType = ChecklistType.TimeBased
            };
        }

        private void SetupSuccessfulModuleLookup()
        {
            // This would require mocking database operations
            // For now, we'll assume the service handles this internally
        }

        private void SetupNonExistentModuleLookup()
        {
            // This would require mocking database operations to return null
        }

        private void SetupAllocatedModuleLookup()
        {
            // This would require mocking database operations to return allocated module
        }

        private void SetupModuleNotAllocated()
        {
            // This would require mocking database operations
        }

        private void SetupModuleAlreadyAllocated()
        {
            // This would require mocking database operations
        }

        private void SetupSuccessfulBusinessRuleValidation()
        {
            _mockBusinessRuleService.Setup(x => x.ValidateVehicleModuleAssignmentAsync(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<ValidationContext>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult { IsValid = true, Issues = new List<ValidationIssue>() });
        }

        private void SetupFailedBusinessRuleValidation()
        {
            _mockBusinessRuleService.Setup(x => x.ValidateVehicleModuleAssignmentAsync(
                It.IsAny<Guid>(),
                It.IsAny<Guid>(),
                It.IsAny<ValidationContext>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult
                {
                    IsValid = false,
                    Issues = new List<ValidationIssue>
                    {
                        new ValidationIssue
                        {
                            RuleId = "TEST_RULE",
                            RuleName = "Test Rule",
                            Severity = ValidationSeverity.Error,
                            Message = "Module assignment validation failed",
                            FieldName = "ModuleId"
                        }
                    }
                });
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
