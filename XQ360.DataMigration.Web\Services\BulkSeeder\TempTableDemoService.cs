using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Demonstration service showing temporary table functionality
/// This service shows how to use temp tables instead of permanent staging tables
/// </summary>
public class TempTableDemoService
{
    private readonly ILogger<TempTableDemoService> _logger;
    private readonly ITempStagingService _tempStagingService;
    private readonly BulkSeederConfiguration _config;
    private readonly IEnvironmentConfigurationService _environmentService;

    public TempTableDemoService(
        ILogger<TempTableDemoService> logger,
        ITempStagingService tempStagingService,
        IOptions<BulkSeederConfiguration> config,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tempStagingService = tempStagingService ?? throw new ArgumentNullException(nameof(tempStagingService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    /// <summary>
    /// Demonstrates the complete temporary table workflow with sample data
    /// </summary>
    public async Task<TempTableDemoResult> DemonstrateCompleteWorkflowAsync(
        int samplePersonCount = 10,
        int sampleVehicleCount = 5,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var result = new TempTableDemoResult
        {
            SessionId = sessionId,
            WorkflowSteps = new List<string>()
        };

        _logger.LogInformation("Starting temporary table workflow demonstration with {PersonCount} persons and {VehicleCount} vehicles, Session: {SessionId}",
            samplePersonCount, sampleVehicleCount, sessionId);

        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        try
        {
            // Step 1: Create temporary tables
            result.WorkflowSteps.Add("Step 1: Creating temporary staging tables");
            await _tempStagingService.CreateTempStagingTablesAsync(connection, sessionId, cancellationToken);
            _logger.LogInformation("✅ Created temporary tables (#PersonDriverImport, #VehicleImport, #ImportSession)");

            // Step 2: Generate sample data
            result.WorkflowSteps.Add("Step 2: Generating sample data");
            var samplePersons = GenerateSamplePersonData(samplePersonCount);
            var sampleVehicles = GenerateSampleVehicleData(sampleVehicleCount);
            _logger.LogInformation("✅ Generated {PersonCount} sample persons and {VehicleCount} sample vehicles", 
                samplePersons.Count, sampleVehicles.Count);

            // Step 3: Bulk insert to temp tables using SqlBulkCopy
            result.WorkflowSteps.Add("Step 3: Bulk inserting data using SqlBulkCopy");
            await _tempStagingService.PopulatePersonDriverTempDataAsync(connection, sessionId, samplePersons, cancellationToken);
            await _tempStagingService.PopulateVehicleTempDataAsync(connection, sessionId, sampleVehicles, cancellationToken);
            _logger.LogInformation("✅ Bulk inserted data to temporary tables");

            // Step 4: Validate data in temp tables
            result.WorkflowSteps.Add("Step 4: Validating data in temporary tables");
            result.ValidationResult = await _tempStagingService.ValidateTempDataAsync(connection, sessionId, cancellationToken);
            _logger.LogInformation("✅ Validation completed: {ValidPersons}/{TotalPersons} valid persons, {ValidVehicles}/{TotalVehicles} valid vehicles",
                result.ValidationResult.ValidPersonRows, result.ValidationResult.TotalPersonRows,
                result.ValidationResult.ValidVehicleRows, result.ValidationResult.TotalVehicleRows);

            // Step 5: Dry run processing (simulate production merge)
            result.WorkflowSteps.Add("Step 5: Dry run processing (simulate production merge)");
            result.ProcessingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);
            _logger.LogInformation("✅ Dry run completed: {ProcessedPersons} persons and {ProcessedVehicles} vehicles would be processed",
                result.ProcessingResult.ProcessedPersonRows, result.ProcessingResult.ProcessedVehicleRows);

            // Step 6: Get final summary
            result.WorkflowSteps.Add("Step 6: Getting session summary");
            result.Summary = await _tempStagingService.GetTempStagingSummaryAsync(connection, sessionId, cancellationToken);
            _logger.LogInformation("✅ Session summary generated");

            // Step 7: Demonstrate temp table queries
            result.WorkflowSteps.Add("Step 7: Demonstrating temp table queries");
            result.TempTableData = await GetTempTableDataSampleAsync(connection, sessionId, cancellationToken);
            _logger.LogInformation("✅ Retrieved sample data from temporary tables");

            result.Success = true;
            result.Message = "Temporary table workflow demonstration completed successfully!";
            
            _logger.LogInformation("🎉 Temporary table demonstration completed successfully for session {SessionId}", sessionId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Temporary table demonstration failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Message = $"Demonstration failed: {ex.Message}";
            return result;
        }
        finally
        {
            // Cleanup (automatic when connection closes, but we can log it)
            try
            {
                await _tempStagingService.CleanupTempTablesAsync(connection, sessionId, cancellationToken);
                _logger.LogInformation("🧹 Temporary tables cleaned up (auto-cleanup on connection close)");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ Cleanup warning for session {SessionId}", sessionId);
            }
        }
    }

    private List<PersonCreateRequest> GenerateSamplePersonData(int count)
    {
        var samplePersons = new List<PersonCreateRequest>();
        var random = new Random(42); // Fixed seed for reproducible results

        // Sample GUIDs for testing (in real implementation, these would come from lookups)
        var sampleSiteId = Guid.Parse("12345678-1234-1234-1234-123456789ABC");
        var sampleDepartmentId = Guid.Parse("*************-4321-4321-CBA987654321");
        var sampleCustomerId = Guid.Parse("11111111-**************-************");

        var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily", "James", "Anna" };
        var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez" };

        for (int i = 0; i < count; i++)
        {
            samplePersons.Add(new PersonCreateRequest
            {
                FirstName = firstNames[random.Next(firstNames.Length)],
                LastName = lastNames[random.Next(lastNames.Length)] + i, // Add index to make unique
                SiteId = sampleSiteId,
                DepartmentId = sampleDepartmentId,
                CustomerId = sampleCustomerId,
                IsDriver = random.Next(2) == 1,
                IsSupervisor = random.Next(5) == 1, // 20% chance
                WebsiteAccess = random.Next(3) != 0, // 66% chance
                // Removed SendDenyMessage - not needed for data seeder
                VORActivateDeactivate = random.Next(2) == 1,
                NormalDriverAccess = random.Next(2) == 1,
                CanUnlockVehicle = random.Next(2) == 1
            });
        }

        return samplePersons;
    }

    private List<TempVehicleCreateRequest> GenerateSampleVehicleData(int count)
    {
        var sampleVehicles = new List<TempVehicleCreateRequest>();
        var random = new Random(84); // Different seed for vehicles

        var vehicleModels = new[] { "Toyota Camry", "Ford F-150", "Honda Civic", "Chevrolet Silverado", "Nissan Altima" };
        var manufacturers = new[] { "Toyota", "Ford", "Honda", "Chevrolet", "Nissan" };

        for (int i = 0; i < count; i++)
        {
            var modelIndex = random.Next(vehicleModels.Length);
            
            sampleVehicles.Add(new TempVehicleCreateRequest
            {
                ExternalVehicleId = $"EXT-{i:D4}",
                HireNo = $"HIRE-{1000 + i}",
                SerialNo = $"SN-{2000 + i}",
                Description = $"Sample vehicle {i + 1}",
                OnHire = random.Next(2) == 1,
                ImpactLockout = random.Next(4) == 1,
                IsCanbus = random.Next(2) == 1,
                TimeoutEnabled = random.Next(2) == 1,
                ModuleIsConnected = random.Next(2) == 1,
                IDLETimer = random.Next(300, 1800), // 5-30 minutes
                CustomerName = "Sample Customer Corp",
                SiteName = "Main Site",
                DepartmentName = "Fleet Department",
                ModelName = vehicleModels[modelIndex],
                ManufacturerName = manufacturers[modelIndex],
                ModuleSerialNumber = $"MOD-{3000 + i}",
                AssignedDriverEmail = random.Next(3) == 0 ? $"driver{i}@example.com" : null,
                AssignedPersonEmail = random.Next(4) == 0 ? $"person{i}@example.com" : null
            });
        }

        return sampleVehicles;
    }

    private async Task<TempTableDataSample> GetTempTableDataSampleAsync(
        SqlConnection connection,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        var dataSample = new TempTableDataSample();

        // Get sample person data from temp table
        const string personSql = @"
            SELECT TOP 3 
                FirstName, LastName, ValidationStatus, IsDriver, IsSupervisor
            FROM #PersonDriverImport 
            WHERE ImportSessionId = @SessionId
            ORDER BY RowNumber
        ";

        using var personCommand = new SqlCommand(personSql, connection);
        personCommand.Parameters.AddWithValue("@SessionId", sessionId);

        using var personReader = await personCommand.ExecuteReaderAsync(cancellationToken);
        while (await personReader.ReadAsync())
        {
            dataSample.SamplePersons.Add($"{personReader["FirstName"]} {personReader["LastName"]} " +
                $"(Driver: {personReader["IsDriver"]}, Supervisor: {personReader["IsSupervisor"]}, " +
                $"Status: {personReader["ValidationStatus"]})");
        }

        // Get sample vehicle data from temp table
        const string vehicleSql = @"
            SELECT TOP 3 
                HireNo, ModelName, ValidationStatus, OnHire
            FROM #VehicleImport 
            WHERE ImportSessionId = @SessionId
            ORDER BY RowNumber
        ";

        using var vehicleCommand = new SqlCommand(vehicleSql, connection);
        vehicleCommand.Parameters.AddWithValue("@SessionId", sessionId);

        using var vehicleReader = await vehicleCommand.ExecuteReaderAsync(cancellationToken);
        while (await vehicleReader.ReadAsync())
        {
            dataSample.SampleVehicles.Add($"{vehicleReader["HireNo"]} - {vehicleReader["ModelName"]} " +
                $"(OnHire: {vehicleReader["OnHire"]}, Status: {vehicleReader["ValidationStatus"]})");
        }

        return dataSample;
    }
}

/// <summary>
/// Result of the temporary table demonstration
/// </summary>
public class TempTableDemoResult
{
    public Guid SessionId { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<string> WorkflowSteps { get; set; } = new();
    public TempValidationResult? ValidationResult { get; set; }
    public TempProcessingResult? ProcessingResult { get; set; }
    public TempStagingSummary? Summary { get; set; }
    public TempTableDataSample? TempTableData { get; set; }
}

/// <summary>
/// Sample data from temporary tables
/// </summary>
public class TempTableDataSample
{
    public List<string> SamplePersons { get; set; } = new();
    public List<string> SampleVehicles { get; set; } = new();
}
