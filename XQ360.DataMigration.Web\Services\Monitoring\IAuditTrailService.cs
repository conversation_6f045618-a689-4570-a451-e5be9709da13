namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public interface IAuditTrailService
    {
        Task LogOperationAsync(AuditEntry entry, CancellationToken cancellationToken = default);
        Task<List<AuditEntry>> GetAuditTrailAsync(AuditFilter filter, CancellationToken cancellationToken = default);
        Task<AuditSummary> GetAuditSummaryAsync(Guid sessionId, CancellationToken cancellationToken = default);
        Task CleanupOldAuditEntriesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default);
        Task<List<AuditEntry>> SearchAuditEntriesAsync(AuditSearchCriteria criteria, CancellationToken cancellationToken = default);
        Task ExportAuditTrailAsync(AuditFilter filter, string exportPath, AuditExportFormat format, CancellationToken cancellationToken = default);
    }

    public class AuditEntry
    {
        public Guid AuditId { get; set; }
        public Guid SessionId { get; set; }
        public DateTime Timestamp { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public AuditEventType EventType { get; set; }
        public string OperationType { get; set; } = string.Empty;
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public AuditAction Action { get; set; }
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> BeforeData { get; set; } = new();
        public Dictionary<string, object> AfterData { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan? Duration { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public AuditSeverity Severity { get; set; } = AuditSeverity.Info;
        public string CorrelationId { get; set; } = string.Empty;
    }

    public enum AuditEventType
    {
        Authentication,
        Authorization,
        DataAccess,
        DataModification,
        SystemOperation,
        SecurityEvent,
        ConfigurationChange,
        Migration,
        Validation,
        Transaction,
        Performance,
        Error
    }

    public enum AuditAction
    {
        Create,
        Read,
        Update,
        Delete,
        Execute,
        Login,
        Logout,
        Access,
        Deny,
        Start,
        Complete,
        Fail,
        Cancel,
        Retry,
        Rollback,
        Export,
        Import
    }

    public enum AuditSeverity
    {
        Info,
        Warning,
        Error,
        Critical,
        Success
    }

    public class AuditFilter
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Guid? SessionId { get; set; }
        public string? UserId { get; set; }
        public AuditEventType? EventType { get; set; }
        public AuditAction? Action { get; set; }
        public string? EntityType { get; set; }
        public AuditSeverity? Severity { get; set; }
        public bool? Success { get; set; }
        public int Skip { get; set; } = 0;
        public int Take { get; set; } = 1000;
        public string OrderBy { get; set; } = "Timestamp";
        public bool OrderDescending { get; set; } = true;
    }

    public class AuditSearchCriteria
    {
        public string? SearchTerm { get; set; }
        public List<string> SearchFields { get; set; } = new() { "Description", "EntityId", "UserName" };
        public AuditFilter? Filter { get; set; }
        public bool CaseSensitive { get; set; } = false;
        public bool UseWildcards { get; set; } = true;
    }

    public class AuditSummary
    {
        public Guid SessionId { get; set; }
        public DateTime SessionStart { get; set; }
        public DateTime? SessionEnd { get; set; }
        public TimeSpan? SessionDuration { get; set; }
        public int TotalEntries { get; set; }
        public int SuccessfulOperations { get; set; }
        public int FailedOperations { get; set; }
        public double SuccessRate => TotalEntries > 0 ? (double)SuccessfulOperations / TotalEntries * 100 : 0;
        public Dictionary<AuditEventType, int> EventTypeCounts { get; set; } = new();
        public Dictionary<AuditAction, int> ActionCounts { get; set; } = new();
        public Dictionary<string, int> EntityTypeCounts { get; set; } = new();
        public List<string> UniqueUsers { get; set; } = new();
        public List<AuditEntry> CriticalEvents { get; set; } = new();
        public List<AuditEntry> Errors { get; set; } = new();
    }

    public enum AuditExportFormat
    {
        Json,
        Csv,
        Excel,
        Xml,
        Pdf
    }

    public class AuditConfiguration
    {
        public TimeSpan DefaultRetentionPeriod { get; set; } = TimeSpan.FromDays(90);
        public TimeSpan CriticalEventRetention { get; set; } = TimeSpan.FromDays(365);
        public bool EnableAutomaticCleanup { get; set; } = true;
        public int MaxEntriesPerBatch { get; set; } = 1000;
        public List<AuditEventType> RequiredEventTypes { get; set; } = new();
        public List<string> SensitiveFields { get; set; } = new() { "Password", "ApiKey", "Token" };
        public bool MaskSensitiveData { get; set; } = true;
        public bool EnablePerformanceMetrics { get; set; } = true;
        public string AuditTableName { get; set; } = "AuditTrail";
        public bool UseCompression { get; set; } = true;
        public bool EnableEncryption { get; set; } = false;
    }

    public class AuditStatistics
    {
        public DateTime GeneratedAt { get; set; }
        public TimeSpan Period { get; set; }
        public long TotalEntries { get; set; }
        public long UniqueUsers { get; set; }
        public long UniqueSessions { get; set; }
        public double AverageEntriesPerSession { get; set; }
        public double AverageSessionDuration { get; set; }
        public Dictionary<AuditEventType, long> EventTypeDistribution { get; set; } = new();
        public Dictionary<AuditAction, long> ActionDistribution { get; set; } = new();
        public Dictionary<AuditSeverity, long> SeverityDistribution { get; set; } = new();
        public List<TopUserActivity> TopUsers { get; set; } = new();
        public List<TopEntityActivity> TopEntities { get; set; } = new();
        public List<TimeBasedActivity> ActivityTrends { get; set; } = new();
    }

    public class TopUserActivity
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public long ActivityCount { get; set; }
        public DateTime LastActivity { get; set; }
        public Dictionary<AuditAction, long> ActionBreakdown { get; set; } = new();
    }

    public class TopEntityActivity
    {
        public string EntityType { get; set; } = string.Empty;
        public string EntityId { get; set; } = string.Empty;
        public long ActivityCount { get; set; }
        public DateTime LastModified { get; set; }
        public Dictionary<AuditAction, long> ActionBreakdown { get; set; } = new();
    }

    public class TimeBasedActivity
    {
        public DateTime TimeSlot { get; set; }
        public long ActivityCount { get; set; }
        public long UniqueUsers { get; set; }
        public double AverageResponseTime { get; set; }
        public long ErrorCount { get; set; }
    }
}
