namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Bulk Insert Optimization Service for high-performance data operations
/// Implements Phase 1.2.1: SqlBulkCopy for staging, parameterized MERGE for production
/// Performance targets: 10,000 records/batch for staging, 1,000 records/batch for production MERGE
/// Transaction strategy: Nested transactions with savepoints for granular rollback
/// </summary>
public interface IBulkInsertOptimizationService
{
    /// <summary>
    /// Performs high-performance bulk insert to staging tables using SqlBulkCopy
    /// Optimized for 10,000 record batches with progress tracking and memory management
    /// </summary>
    Task<BulkInsertResult> BulkInsertToStagingAsync<T>(
        Guid sessionId,
        IEnumerable<T> data,
        string stagingTableName,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Performs optimized MERGE operation from staging to production tables
    /// Uses parameterized MERGE with 1,000 record batches for optimal performance
    /// </summary>
    Task<MergeResult> MergeFromStagingToProductionAsync(
        Guid sessionId,
        string stagingTableName,
        string productionTableName,
        MergeConfiguration mergeConfig,
        int batchSize = 1000,
        IProgress<MergeProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk inserts driver data to staging with validation and FK resolution
    /// Integrates with FK cache for sub-millisecond lookup performance
    /// </summary>
    Task<BulkInsertResult> BulkInsertDriversToStagingAsync(
        Guid sessionId,
        IEnumerable<DriverStagingRecord> drivers,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk inserts vehicle data to staging with complex dependency resolution
    /// Pre-validates module allocation and model references
    /// </summary>
    Task<BulkInsertResult> BulkInsertVehiclesToStagingAsync(
        Guid sessionId,
        IEnumerable<VehicleStagingRecord> vehicles,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk inserts card data to staging with Weigand uniqueness validation
    /// Ensures unique Weigand numbers across all cards
    /// </summary>
    Task<BulkInsertResult> BulkInsertCardsToStagingAsync(
        Guid sessionId,
        IEnumerable<CardStagingRecord> cards,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk inserts access permission data for 4-tier access system
    /// Optimized for high-volume access record generation
    /// </summary>
    Task<BulkInsertResult> BulkInsertAccessPermissionsToStagingAsync(
        Guid sessionId,
        IEnumerable<AccessPermissionStagingRecord> accessRecords,
        int batchSize = 10000,
        IProgress<BulkInsertProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes transactional MERGE operations with nested transaction support
    /// Provides savepoints for granular rollback and error recovery
    /// </summary>
    Task<TransactionalMergeResult> ExecuteTransactionalMergeAsync(
        Guid sessionId,
        IEnumerable<MergeOperation> mergeOperations,
        TransactionStrategy strategy = TransactionStrategy.NestedWithSavepoints,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Optimizes bulk operation performance with connection pooling and statistics
    /// Updates table statistics and optimizes execution plans for large datasets
    /// </summary>
    Task OptimizeBulkOperationPerformanceAsync(
        string tableName,
        bool updateStatistics = true,
        bool recompileQueries = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets comprehensive bulk operation metrics for performance monitoring
    /// Includes throughput analysis and optimization recommendations
    /// </summary>
    Task<BulkOperationMetrics> GetBulkOperationMetricsAsync(Guid sessionId);

    /// <summary>
    /// Validates staging data integrity before production MERGE operations
    /// Performs referential integrity and business rule validation
    /// </summary>
    Task<StagingValidationResult> ValidateStagingDataIntegrityAsync(
        Guid sessionId,
        IEnumerable<string> stagingTableNames,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration for MERGE operations between staging and production tables
/// </summary>
public class MergeConfiguration
{
    public List<string> KeyColumns { get; set; } = new();
    public List<string> UpdateColumns { get; set; } = new();
    public List<string> InsertColumns { get; set; } = new();
    public bool AllowDelete { get; set; } = false;
    public string? CustomMergeCondition { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool UseOutputClause { get; set; } = true;
}

/// <summary>
/// Strategy for handling transactions in bulk operations
/// </summary>
public enum TransactionStrategy
{
    SingleTransaction,
    NestedWithSavepoints,
    BatchedTransactions,
    ReadCommittedSnapshot
}

/// <summary>
/// Represents a single MERGE operation in a transactional batch
/// </summary>
public class MergeOperation
{
    public string StagingTableName { get; set; } = string.Empty;
    public string ProductionTableName { get; set; } = string.Empty;
    public MergeConfiguration Configuration { get; set; } = new();
    public int Priority { get; set; } = 0; // For operation ordering
    public string? SavepointName { get; set; }
}

/// <summary>
/// Result of bulk insert operation with performance metrics
/// </summary>
public class BulkInsertResult
{
    public bool Success { get; set; }
    public int TotalRecords { get; set; }
    public int InsertedRecords { get; set; }
    public int SkippedRecords { get; set; }
    public int ErrorRecords { get; set; }
    public TimeSpan Duration { get; set; }
    public decimal RecordsPerSecond => Duration.TotalSeconds > 0 ? (decimal)InsertedRecords / (decimal)Duration.TotalSeconds : 0;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public BulkInsertMetrics Metrics { get; set; } = new();
}

/// <summary>
/// Result of MERGE operation with detailed tracking
/// </summary>
public class MergeResult
{
    public bool Success { get; set; }
    public int ProcessedRecords { get; set; }
    public int InsertedRecords { get; set; }
    public int UpdatedRecords { get; set; }
    public int DeletedRecords { get; set; }
    public int SkippedRecords { get; set; }
    public TimeSpan Duration { get; set; }
    public decimal RecordsPerSecond => Duration.TotalSeconds > 0 ? (decimal)ProcessedRecords / (decimal)Duration.TotalSeconds : 0;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public Dictionary<string, object> OutputData { get; set; } = new(); // From OUTPUT clause
}

/// <summary>
/// Result of transactional MERGE operations with rollback support
/// </summary>
public class TransactionalMergeResult
{
    public bool Success { get; set; }
    public List<MergeResult> OperationResults { get; set; } = new();
    public TimeSpan TotalDuration { get; set; }
    public int TotalProcessedRecords { get; set; }
    public List<string> SavepointsCreated { get; set; } = new();
    public List<string> SavepointsRolledBack { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public TransactionStrategy StrategyUsed { get; set; }
}

/// <summary>
/// Progress tracking for bulk insert operations
/// </summary>
public class BulkInsertProgress
{
    public int TotalRecords { get; set; }
    public int ProcessedRecords { get; set; }
    public int ProgressPercentage => TotalRecords > 0 ? (ProcessedRecords * 100) / TotalRecords : 0;
    public TimeSpan ElapsedTime { get; set; }
    public decimal CurrentRecordsPerSecond { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public string CurrentOperation { get; set; } = string.Empty;
    public long MemoryUsageBytes { get; set; }
}

/// <summary>
/// Progress tracking for MERGE operations
/// </summary>
public class MergeProgress
{
    public int TotalBatches { get; set; }
    public int ProcessedBatches { get; set; }
    public int CurrentBatchSize { get; set; }
    public int TotalRecords { get; set; }
    public int ProcessedRecords { get; set; }
    public int InsertedRecords { get; set; }
    public int UpdatedRecords { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public string CurrentOperation { get; set; } = string.Empty;
}

/// <summary>
/// Detailed metrics for bulk insert operations
/// </summary>
public class BulkInsertMetrics
{
    public TimeSpan DataPreparationTime { get; set; }
    public TimeSpan SqlBulkCopyTime { get; set; }
    public TimeSpan ValidationTime { get; set; }
    public TimeSpan IndexUpdateTime { get; set; }
    public long PeakMemoryUsageBytes { get; set; }
    public int BatchCount { get; set; }
    public decimal AverageBatchSize { get; set; }
    public decimal ThroughputRecordsPerSecond { get; set; }
    public List<string> PerformanceRecommendations { get; set; } = new();
}

/// <summary>
/// Comprehensive metrics for all bulk operations in a session
/// </summary>
public class BulkOperationMetrics
{
    public Guid SessionId { get; set; }
    public DateTime MetricsTimestamp { get; set; } = DateTime.UtcNow;
    
    // Overall Statistics
    public int TotalBulkInserts { get; set; }
    public int TotalMergeOperations { get; set; }
    public int TotalRecordsProcessed { get; set; }
    public TimeSpan TotalOperationTime { get; set; }
    public decimal OverallThroughput { get; set; }
    
    // Performance Breakdown
    public BulkInsertMetrics DriverInsertMetrics { get; set; } = new();
    public BulkInsertMetrics VehicleInsertMetrics { get; set; } = new();
    public BulkInsertMetrics CardInsertMetrics { get; set; } = new();
    public BulkInsertMetrics AccessInsertMetrics { get; set; } = new();
    
    // Resource Usage
    public long PeakMemoryUsageBytes { get; set; }
    public int MaxConcurrentConnections { get; set; }
    public decimal AverageConnectionPoolUsage { get; set; }
    
    // Error Statistics
    public int TotalErrors { get; set; }
    public int TotalWarnings { get; set; }
    public List<string> ErrorSummary { get; set; } = new();
    
    // Optimization Recommendations
    public List<string> PerformanceRecommendations { get; set; } = new();
    public List<string> ScalabilityRecommendations { get; set; } = new();
}

/// <summary>
/// Result of staging data integrity validation
/// </summary>
public class StagingValidationResult
{
    public bool IsValid { get; set; }
    public Dictionary<string, TableValidationResult> TableResults { get; set; } = new();
    public List<string> ReferentialIntegrityErrors { get; set; } = new();
    public List<string> BusinessRuleViolations { get; set; } = new();
    public List<string> DataQualityWarnings { get; set; } = new();
    public int TotalRecordsValidated { get; set; }
    public int ValidRecords { get; set; }
    public int InvalidRecords { get; set; }
    public TimeSpan ValidationDuration { get; set; }
}

/// <summary>
/// Validation result for individual staging table
/// </summary>
public class TableValidationResult
{
    public string TableName { get; set; } = string.Empty;
    public bool IsValid { get; set; }
    public int TotalRecords { get; set; }
    public int ValidRecords { get; set; }
    public int InvalidRecords { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

// Staging record models for bulk operations

/// <summary>
/// Driver staging record for bulk insert operations
/// </summary>
public class DriverStagingRecord
{
    public Guid SessionId { get; set; }
    public int BatchId { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? EmployeeId { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? LicenseNumber { get; set; }
    public DateTime? LicenseExpiryDate { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string SiteName { get; set; } = string.Empty;
    public string DepartmentName { get; set; } = string.Empty;
    public string? CardNumber { get; set; }
    public long? WeigandNumber { get; set; }
    public string AccessLevel { get; set; } = "Department";
}

/// <summary>
/// Vehicle staging record for bulk insert operations
/// </summary>
public class VehicleStagingRecord
{
    public Guid SessionId { get; set; }
    public int BatchId { get; set; }
    public string VehicleName { get; set; } = string.Empty;
    public string DeviceId { get; set; } = string.Empty;
    public string? LicensePlate { get; set; }
    public string? VIN { get; set; }
    public string? Make { get; set; }
    public string? Model { get; set; }
    public int? Year { get; set; }
    public string? Color { get; set; }
    public string? FuelType { get; set; }
    public string? VehicleType { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string SiteName { get; set; } = string.Empty;
    public string DepartmentName { get; set; } = string.Empty;
    public string? ModelName { get; set; }
    public string? DealerName { get; set; }
    public string? ModuleSerialNumber { get; set; }
    public string ChecklistType { get; set; } = "Time";
}

/// <summary>
/// Card staging record for bulk insert operations
/// </summary>
public class CardStagingRecord
{
    public Guid SessionId { get; set; }
    public int BatchId { get; set; }
    public string CardNumber { get; set; } = string.Empty;
    public long WeigandNumber { get; set; }
    public string CardType { get; set; } = "Normal";
    public bool Active { get; set; } = true;
    public DateTime? ExpiryDate { get; set; }
    public string DriverFirstName { get; set; } = string.Empty;
    public string DriverLastName { get; set; } = string.Empty;
    public string DriverEmail { get; set; } = string.Empty;
}

/// <summary>
/// Access permission staging record for bulk insert operations
/// </summary>
public class AccessPermissionStagingRecord
{
    public Guid SessionId { get; set; }
    public int BatchId { get; set; }
    public string AccessLevel { get; set; } = string.Empty; // Site, Department, Model, Vehicle
    public string PermissionType { get; set; } = "Normal";
    public long WeigandNumber { get; set; }
    public string DriverFirstName { get; set; } = string.Empty;
    public string DriverLastName { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string SiteName { get; set; } = string.Empty;
    public string DepartmentName { get; set; } = string.Empty;
    public string? TargetModelName { get; set; }
    public string? TargetVehicleName { get; set; }
}
