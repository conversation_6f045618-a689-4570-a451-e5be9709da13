# IoT Workflow Integration in Data Seeder

## Overview

The data seeder now includes complete IoT device creation functionality that mirrors the main migration system's 3-phase approach. This ensures consistency between the main migration system and the data seeder for IoT-enabled vehicle management.

## Architecture

### Main Migration System Pattern (Reference)
```
Step 1: Spare Modules (API) → IoT hub integration
Step 3: Vehicles (SQL) → Links to IoT devices via ModuleId1
Step 9: Vehicle Sync Settings (API) → IoT device synchronization
```

### Data Seeder Implementation (New)
```
Phase 1: Create IoT devices/modules via API (IIoTDeviceCreationService)
Phase 2: Create vehicles linked to IoT devices (ComplexEntityCreationService)
Phase 3: Sync vehicle IoT settings via API (IIoTDeviceCreationService)
```

## New Services

### 1. IIoTDeviceCreationService
- **Purpose**: Creates IoT devices/modules via API calls before vehicle creation
- **Pattern**: Mirrors `SpareModuleMigration.cs` functionality
- **API Endpoint**: `module` (same as main migration system)
- **Features**:
  - Rate limiting (30 requests/second)
  - Retry policies with exponential backoff
  - Duplicate detection
  - Dealer validation and caching

### 2. IEnhancedApiOrchestrationService
- **Purpose**: Orchestrates complete IoT workflow with temp table integration
- **Pattern**: Extends `TempTableApiOrchestrationService` with IoT capabilities
- **Features**:
  - 3-phase workflow execution
  - Dry run support
  - Progress tracking
  - Error handling and rollback

## API Integration

### IoT Device Creation
```csharp
// Uses same API endpoint as main migration system
var apiResult = await _apiClient.PostFormAsync<IoTDeviceApiResponse>("module", formData);
```

### Vehicle IoT Synchronization
```csharp
// Uses same API endpoint as main migration system
var apiResult = await _apiClient.PostFormAsync<object>("iothubmanager/syncvehiclesettings", formData);
```

## Usage Examples

### 1. Complete IoT Workflow via API

**Endpoint**: `POST /api/bulk-seeder/iot-workflow`

**Request**:
```json
{
  "dealerName": "Test Dealer",
  "moduleCount": 10,
  "vehicleCount": 10,
  "startingNumber": 1,
  "batchSize": 5,
  "dryRun": false,
  "modelId": "12345678-1234-1234-1234-123456789012",
  "siteId": "12345678-1234-1234-1234-123456789012",
  "departmentId": "12345678-1234-1234-1234-123456789012",
  "customerId": "12345678-1234-1234-1234-123456789012"
}
```

**Response**:
```json
{
  "sessionId": "12345678-1234-1234-1234-123456789012",
  "totalModuleRequests": 10,
  "totalVehicleRequests": 10,
  "success": true,
  "summary": "IoT workflow completed: 10 modules, 10 vehicles, 10 synced",
  "moduleCreationResult": {
    "totalRequests": 10,
    "successfulRequests": 10,
    "failedRequests": 0,
    "success": true
  },
  "vehicleCreationSuccess": true,
  "syncResult": {
    "totalDevices": 10,
    "successfulSyncs": 10,
    "failedSyncs": 0,
    "success": true
  }
}
```

### 2. Programmatic Usage

```csharp
// Inject the enhanced service
private readonly IEnhancedApiOrchestrationService _enhancedApiService;

// Generate IoT device requests
var moduleRequests = TempTableApiOrchestrationService.GenerateIoTDeviceRequests(
    "Test Dealer", 10, 1);

// Generate vehicle requests linked to IoT devices
var vehicleRequests = TempTableApiOrchestrationService.GenerateVehicleRequestsWithIoTDevices(
    moduleRequests, modelId, siteId, departmentId, customerId, 1);

// Execute complete workflow
var result = await _enhancedApiService.CreateCompleteIoTWorkflowAsync(
    moduleRequests, vehicleRequests, batchSize: 5, dryRun: false);
```

## Data Flow

### Phase 1: IoT Device Creation
1. Generate IoT device requests with unique device IDs
2. Validate dealer exists in database
3. Check for duplicate IoT devices
4. Create module entities via API
5. Cache results for Phase 2

### Phase 2: Vehicle Creation
1. Use existing `ComplexEntityCreationService.CreateVehicleBatchAsync`
2. Link vehicles to IoT devices via `ModuleId1` foreign key
3. Validate module availability
4. Create vehicle with all dependencies

### Phase 3: IoT Device Synchronization
1. Extract device IDs from successful module creation
2. Call `iothubmanager/syncvehiclesettings` for each device
3. Apply rate limiting and retry policies
4. Report sync results

## Configuration

### Dependency Injection
```csharp
// Register IoT device creation services
builder.Services.AddScoped<IIoTDeviceCreationService, IoTDeviceCreationService>();
builder.Services.AddScoped<IEnhancedApiOrchestrationService, TempTableApiOrchestrationService>();
```

### Rate Limiting
- **IoT Device Creation**: 30 requests/second
- **Vehicle Sync**: 500ms delay between calls
- **Retry Policy**: 3 attempts with exponential backoff

## Error Handling

### Validation Errors
- Missing dealer name
- Invalid module/vehicle counts
- Missing required GUIDs

### API Errors
- Authentication failures
- Rate limit exceeded
- IoT device already exists
- Sync operation failures

### Recovery Strategies
- Automatic retry with exponential backoff
- Graceful degradation (continue with partial success)
- Detailed error reporting for troubleshooting

## Consistency with Main Migration System

### ✅ Maintained Patterns
- Same API endpoints (`module`, `iothubmanager/syncvehiclesettings`)
- Same authentication mechanism (`XQ360ApiClient`)
- Same database schema and relationships
- Same error handling and retry policies
- Same 3-phase workflow order

### ✅ Enhanced Features
- Temp table integration for validation
- Batch processing with rate limiting
- Dry run capability
- Real-time progress tracking
- Comprehensive result reporting

## Testing

### Unit Tests
- `IoTDeviceCreationServiceTests.cs`
- `TempTableApiOrchestrationServiceTests.cs`

### Integration Tests
- End-to-end workflow testing
- API connectivity validation
- Database consistency verification

### Performance Tests
- Batch processing performance
- Rate limiting effectiveness
- Memory usage optimization

## Migration Path

### From Main Migration System
1. Use same CSV templates (`SPARE_MODEL_IMPORT_TEMPLATE.csv`, `VEHICLE_IMPORT.csv`)
2. Same API authentication and configuration
3. Same database schema and constraints

### To Data Seeder
1. Generate requests programmatically
2. Use enhanced batch processing
3. Leverage temp table validation
4. Real-time progress monitoring

This implementation ensures that the data seeder maintains full compatibility with the main migration system's IoT integration patterns while providing enhanced functionality for bulk operations.
