namespace XQ360.DataMigration.Web.Services.BusinessRules
{
    /// <summary>
    /// Business rule validation service for ensuring data integrity and compliance
    /// </summary>
    public interface IBusinessRuleValidationService
    {
        /// <summary>
        /// Validates business rules for a single entity
        /// </summary>
        Task<ValidationResult> ValidateEntityAsync<T>(T entity, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates business rules for a batch of entities
        /// </summary>
        Task<BatchValidationResult> ValidateBatchAsync<T>(IEnumerable<T> entities, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates vehicle module assignment rules
        /// </summary>
        Task<ValidationResult> ValidateVehicleModuleAssignmentAsync(Guid vehicleId, Guid moduleId, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates driver card allocation rules
        /// </summary>
        Task<ValidationResult> ValidateDriverCardAllocationAsync(Guid driverId, Guid cardId, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates access permission rules
        /// </summary>
        Task<ValidationResult> ValidateAccessPermissionAsync(AccessPermissionRequest request, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates uniqueness constraints
        /// </summary>
        Task<ValidationResult> ValidateUniquenessAsync<T>(T entity, string[] uniqueFields, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validates referential integrity
        /// </summary>
        Task<ValidationResult> ValidateReferentialIntegrityAsync<T>(T entity, ValidationContext context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all available business rules for an entity type
        /// </summary>
        Task<IEnumerable<BusinessRule>> GetBusinessRulesAsync<T>(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Validation context containing session and configuration information
    /// </summary>
    public class ValidationContext
    {
        public Guid SessionId { get; set; }
        public string ConnectionString { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public ValidationMode Mode { get; set; } = ValidationMode.Strict;
        public bool EnableCaching { get; set; } = true;
        public TimeSpan CacheTimeout { get; set; } = TimeSpan.FromMinutes(5);
    }

    /// <summary>
    /// Validation mode enumeration
    /// </summary>
    public enum ValidationMode
    {
        Strict,     // All rules must pass
        Lenient,    // Warnings allowed, only errors fail
        Advisory    // All issues are warnings
    }

    /// <summary>
    /// Single entity validation result
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<ValidationIssue> Issues { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public int RulesEvaluated { get; set; }

        public IEnumerable<ValidationIssue> Errors => Issues.Where(i => i.Severity == ValidationSeverity.Error);
        public IEnumerable<ValidationIssue> Warnings => Issues.Where(i => i.Severity == ValidationSeverity.Warning);
        public IEnumerable<ValidationIssue> Information => Issues.Where(i => i.Severity == ValidationSeverity.Information);
    }

    /// <summary>
    /// Batch validation result
    /// </summary>
    public class BatchValidationResult
    {
        public bool IsValid { get; set; }
        public List<EntityValidationResult> EntityResults { get; set; } = new();
        public ValidationSummary Summary { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public int TotalEntitiesValidated { get; set; }
        public int TotalRulesEvaluated { get; set; }
    }

    /// <summary>
    /// Individual entity validation result within a batch
    /// </summary>
    public class EntityValidationResult
    {
        public int EntityIndex { get; set; }
        public string EntityIdentifier { get; set; } = string.Empty;
        public ValidationResult Result { get; set; } = new();
    }

    /// <summary>
    /// Validation summary statistics
    /// </summary>
    public class ValidationSummary
    {
        public int ValidEntities { get; set; }
        public int InvalidEntities { get; set; }
        public int TotalErrors { get; set; }
        public int TotalWarnings { get; set; }
        public int TotalInformation { get; set; }
        public Dictionary<string, int> ErrorsByRule { get; set; } = new();
        public Dictionary<string, int> WarningsByRule { get; set; } = new();
    }

    /// <summary>
    /// Individual validation issue
    /// </summary>
    public class ValidationIssue
    {
        public string RuleId { get; set; } = string.Empty;
        public string RuleName { get; set; } = string.Empty;
        public ValidationSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public string FieldName { get; set; } = string.Empty;
        public object? FieldValue { get; set; }
        public string Suggestion { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Validation severity levels
    /// </summary>
    public enum ValidationSeverity
    {
        Information,
        Warning,
        Error
    }

    /// <summary>
    /// Business rule definition
    /// </summary>
    public class BusinessRule
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public ValidationSeverity DefaultSeverity { get; set; }
        public bool IsEnabled { get; set; } = true;
        public int Priority { get; set; } = 100;
        public string[] ApplicableEntityTypes { get; set; } = Array.Empty<string>();
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// Access permission validation request
    /// </summary>
    public class AccessPermissionRequest
    {
        public Guid CardId { get; set; }
        public Guid? SiteId { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? ModelId { get; set; }
        public Guid? VehicleId { get; set; }
        public Guid PermissionId { get; set; }
        public string AccessLevel { get; set; } = string.Empty;
    }
}
