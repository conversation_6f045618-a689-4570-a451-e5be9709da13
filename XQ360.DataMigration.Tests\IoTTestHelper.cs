using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Helper class for IoT-related unit tests
    /// Provides common test data creation, mock setups, and assertion helpers
    /// </summary>
    public static class IoTTestHelper
    {
        #region Test Data Creation

        /// <summary>
        /// Creates test IoT device requests with sequential naming
        /// </summary>
        public static List<IoTDeviceCreateRequest> CreateIoTDeviceRequests(int count, string dealerName = "Test Dealer", int startIndex = 1)
        {
            var requests = new List<IoTDeviceCreateRequest>();
            for (int i = 0; i < count; i++)
            {
                var index = startIndex + i;
                requests.Add(new IoTDeviceCreateRequest
                {
                    IoTDeviceID = $"TEST_IOT_{index:D6}",
                    Dealer = dealerName,
                    CCID = 1000 + index,
                    RANumber = 2000 + index,
                    TechNumber = 3000 + index
                });
            }
            return requests;
        }

        /// <summary>
        /// Creates test vehicle requests with linked IoT devices
        /// </summary>
        public static List<VehicleCreateRequest> CreateVehicleRequests(int count, int startIndex = 1, bool linkToIoTDevices = true)
        {
            var requests = new List<VehicleCreateRequest>();
            for (int i = 0; i < count; i++)
            {
                var index = startIndex + i;
                requests.Add(new VehicleCreateRequest
                {
                    SerialNo = $"TEST_VEH_{index:D6}",
                    HireNo = $"HIRE_{index:D4}",
                    IdleTimer = 300,
                    OnHire = true,
                    ImpactLockout = false,
                    TimeoutEnabled = true,
                    IsCanbus = i % 2 == 0,
                    ModelId = Guid.NewGuid(),
                    SiteId = Guid.NewGuid(),
                    DepartmentId = Guid.NewGuid(),
                    CustomerId = Guid.NewGuid(),
                    ModuleIoTDevice = linkToIoTDevices ? $"TEST_IOT_{index:D6}" : null,
                    ChecklistType = ChecklistType.TimeBased
                });
            }
            return requests;
        }

        /// <summary>
        /// Creates test module allocation requests
        /// </summary>
        public static List<ModuleAllocationRequest> CreateModuleAllocationRequests(int count, int startIndex = 1)
        {
            var requests = new List<ModuleAllocationRequest>();
            for (int i = 0; i < count; i++)
            {
                var index = startIndex + i;
                requests.Add(new ModuleAllocationRequest
                {
                    IoTDevice = $"TEST_IOT_{index:D6}",
                    VehicleId = Guid.NewGuid(),
                    ValidateAvailability = true
                });
            }
            return requests;
        }

        #endregion

        #region Test Result Creation

        /// <summary>
        /// Creates a successful IoT device creation result
        /// </summary>
        public static IoTDeviceCreationResult CreateSuccessfulIoTDeviceCreationResult(int totalRequests, List<string> deviceIds = null)
        {
            deviceIds ??= Enumerable.Range(1, totalRequests).Select(i => $"TEST_IOT_{i:D6}").ToList();

            var results = new Dictionary<string, IoTDeviceApiResult>();
            foreach (var deviceId in deviceIds)
            {
                results[$"{deviceId}_Test Dealer"] = new IoTDeviceApiResult
                {
                    Success = true,
                    ModuleId = Guid.NewGuid(),
                    IoTDeviceID = deviceId
                };
            }

            return new IoTDeviceCreationResult
            {
                Success = true,
                TotalRequests = totalRequests,
                SuccessfulRequests = totalRequests,
                FailedRequests = 0,
                Duration = TimeSpan.FromSeconds(2),
                Results = results
            };
        }

        /// <summary>
        /// Creates a failed IoT device creation result
        /// </summary>
        public static IoTDeviceCreationResult CreateFailedIoTDeviceCreationResult(int totalRequests, string errorMessage = "Device creation failed")
        {
            return new IoTDeviceCreationResult
            {
                Success = false,
                TotalRequests = totalRequests,
                SuccessfulRequests = 0,
                FailedRequests = totalRequests,
                Duration = TimeSpan.FromSeconds(1),
                Errors = new List<string> { errorMessage }
            };
        }

        /// <summary>
        /// Creates a successful IoT device sync result
        /// </summary>
        public static IoTDeviceSyncResult CreateSuccessfulIoTDeviceSyncResult(List<string> deviceIds)
        {
            return new IoTDeviceSyncResult
            {
                Success = true,
                TotalDevices = deviceIds.Count,
                SuccessfulSyncs = deviceIds.Count,
                FailedSyncs = 0,
                Duration = TimeSpan.FromSeconds(deviceIds.Count * 0.5),
                SyncedDeviceIds = new List<string>(deviceIds),
                FailedDeviceIds = new List<string>()
            };
        }

        /// <summary>
        /// Creates a failed IoT device sync result
        /// </summary>
        public static IoTDeviceSyncResult CreateFailedIoTDeviceSyncResult(List<string> deviceIds, string errorMessage = "Sync operation failed")
        {
            return new IoTDeviceSyncResult
            {
                Success = false,
                TotalDevices = deviceIds.Count,
                SuccessfulSyncs = 0,
                FailedSyncs = deviceIds.Count,
                Duration = TimeSpan.FromSeconds(1),
                SyncedDeviceIds = new List<string>(),
                FailedDeviceIds = new List<string>(deviceIds),
                Errors = new List<string> { errorMessage }
            };
        }

        /// <summary>
        /// Creates a successful complete IoT workflow result
        /// </summary>
        public static CompleteIoTWorkflowResult CreateSuccessfulCompleteIoTWorkflowResult(int moduleCount, int vehicleCount)
        {
            var deviceIds = Enumerable.Range(1, moduleCount).Select(i => $"TEST_IOT_{i:D6}").ToList();

            return new CompleteIoTWorkflowResult
            {
                SessionId = Guid.NewGuid(),
                Success = true,
                TotalModuleRequests = moduleCount,
                TotalVehicleRequests = vehicleCount,
                DryRun = false,
                Duration = TimeSpan.FromSeconds(10),
                Summary = $"IoT workflow completed: {moduleCount} modules, {vehicleCount} vehicles, {moduleCount} synced",
                ModuleCreationResult = CreateSuccessfulIoTDeviceCreationResult(moduleCount, deviceIds),
                VehicleCreationSuccess = true,
                VehicleCreationSummary = $"Created {vehicleCount} vehicles successfully",
                SyncResult = CreateSuccessfulIoTDeviceSyncResult(deviceIds)
            };
        }

        #endregion

        #region Test Configuration

        /// <summary>
        /// Creates test bulk seeder configuration
        /// </summary>
        public static BulkSeederConfiguration CreateTestBulkSeederConfiguration()
        {
            return new BulkSeederConfiguration
            {
                DefaultBatchSize = 50,
                MaxRetryAttempts = 3,
                RetryDelayMs = 1000,
                DefaultDriversCount = 100,
                DefaultVehiclesCount = 50
            };
        }

        /// <summary>
        /// Creates test migration configuration
        /// </summary>
        public static MigrationConfiguration CreateTestMigrationConfiguration()
        {
            return new MigrationConfiguration
            {
                DatabaseConnection = "Server=test;Database=test;Integrated Security=true;TrustServerCertificate=true;",
                ApiBaseUrl = "https://test-api.example.com",
                ApiUsername = "testuser",
                ApiPassword = "testpass"
            };
        }

        #endregion

        #region Assertion Helpers

        /// <summary>
        /// Asserts that an IoT device creation result is successful
        /// </summary>
        public static void AssertSuccessfulIoTDeviceCreation(IoTDeviceCreationResult result, int expectedTotal)
        {
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(expectedTotal, result.TotalRequests);
            Assert.Equal(expectedTotal, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);
            Assert.True(result.Duration > TimeSpan.Zero);
            Assert.Empty(result.Errors);
        }

        /// <summary>
        /// Asserts that an IoT device sync result is successful
        /// </summary>
        public static void AssertSuccessfulIoTDeviceSync(IoTDeviceSyncResult result, int expectedTotal)
        {
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(expectedTotal, result.TotalDevices);
            Assert.Equal(expectedTotal, result.SuccessfulSyncs);
            Assert.Equal(0, result.FailedSyncs);
            Assert.True(result.Duration > TimeSpan.Zero);
            Assert.Empty(result.Errors);
            Assert.Equal(expectedTotal, result.SyncedDeviceIds.Count);
            Assert.Empty(result.FailedDeviceIds);
        }

        /// <summary>
        /// Asserts that a complete IoT workflow result is successful
        /// </summary>
        public static void AssertSuccessfulCompleteIoTWorkflow(CompleteIoTWorkflowResult result, int expectedModules, int expectedVehicles)
        {
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(expectedModules, result.TotalModuleRequests);
            Assert.Equal(expectedVehicles, result.TotalVehicleRequests);
            Assert.True(result.Duration > TimeSpan.Zero);
            Assert.Contains("completed", result.Summary.ToLower());

            // Assert Phase 1: Module Creation
            Assert.NotNull(result.ModuleCreationResult);
            AssertSuccessfulIoTDeviceCreation(result.ModuleCreationResult, expectedModules);

            // Assert Phase 2: Vehicle Creation
            Assert.True(result.VehicleCreationSuccess);

            // Assert Phase 3: IoT Sync
            Assert.NotNull(result.SyncResult);
            AssertSuccessfulIoTDeviceSync(result.SyncResult, expectedModules);
        }

        /// <summary>
        /// Asserts that a result indicates failure with expected error patterns
        /// </summary>
        public static void AssertFailureWithError(object result, string expectedErrorPattern)
        {
            Assert.NotNull(result);

            // Check different result types for failure indicators
            switch (result)
            {
                case IoTDeviceCreationResult iotResult:
                    Assert.False(iotResult.Success);
                    Assert.True(iotResult.FailedRequests > 0);
                    Assert.NotEmpty(iotResult.Errors);
                    Assert.Contains(iotResult.Errors, error => error.Contains(expectedErrorPattern, StringComparison.OrdinalIgnoreCase));
                    break;

                case IoTDeviceSyncResult syncResult:
                    Assert.False(syncResult.Success);
                    Assert.True(syncResult.FailedSyncs > 0);
                    Assert.NotEmpty(syncResult.Errors);
                    Assert.Contains(syncResult.Errors, error => error.Contains(expectedErrorPattern, StringComparison.OrdinalIgnoreCase));
                    break;

                case CompleteIoTWorkflowResult workflowResult:
                    Assert.False(workflowResult.Success);
                    Assert.Contains(expectedErrorPattern, workflowResult.Summary, StringComparison.OrdinalIgnoreCase);
                    break;

                default:
                    throw new ArgumentException($"Unsupported result type: {result.GetType()}");
            }
        }

        #endregion

        #region Mock API Response Helpers

        /// <summary>
        /// Creates a successful IoT device API response
        /// </summary>
        public static IoTDeviceApiResponse CreateSuccessfulIoTDeviceApiResponse()
        {
            return new IoTDeviceApiResponse
            {
                InternalObjectId = Random.Shared.Next(1, 1000),
                PrimaryKey = Guid.NewGuid().ToString(),
                ObjectsDataSet = new { Status = "Created", Timestamp = DateTime.UtcNow }
            };
        }

        /// <summary>
        /// Creates test API result for successful operations
        /// </summary>
        public static ApiResult<T> CreateSuccessfulApiResult<T>(T data)
        {
            return new ApiResult<T>
            {
                Success = true,
                Data = data,
                ErrorMessage = null
            };
        }

        /// <summary>
        /// Creates test API result for failed operations
        /// </summary>
        public static ApiResult<T> CreateFailedApiResult<T>(string errorMessage)
        {
            return new ApiResult<T>
            {
                Success = false,
                Data = default(T),
                ErrorMessage = errorMessage
            };
        }

        #endregion
    }
}
