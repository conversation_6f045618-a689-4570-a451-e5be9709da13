using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Data.SqlClient;
using System.Threading.RateLimiting;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Unit tests for IoTDeviceCreationService
    /// Tests IoT device creation via API calls, vehicle IoT settings synchronization,
    /// API connectivity validation, error handling, and rate limiting
    /// </summary>
    public class IoTDeviceCreationServiceTests : IDisposable
    {
        private readonly Mock<ILogger<IoTDeviceCreationService>> _mockLogger;
        private readonly Mock<XQ360ApiClient> _mockApiClient;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockConfig;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly BulkSeederConfiguration _testConfig;
        private readonly MigrationConfiguration _testMigrationConfig;

        public IoTDeviceCreationServiceTests()
        {
            _mockLogger = new Mock<ILogger<IoTDeviceCreationService>>();
            _mockApiClient = new Mock<XQ360ApiClient>();
            _mockConfig = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();

            _testConfig = new BulkSeederConfiguration
            {
                DefaultBatchSize = 50,
                MaxRetryAttempts = 3,
                RetryDelayMs = 1000
            };

            _testMigrationConfig = new MigrationConfiguration
            {
                DatabaseConnection = "Server=test;Database=test;Integrated Security=true;",
                ApiBaseUrl = "https://test-api.example.com",
                ApiUsername = "testuser",
                ApiPassword = "testpass"
            };

            _mockConfig.Setup(x => x.Value).Returns(_testConfig);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(_testMigrationConfig);
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act
            var service = CreateIoTDeviceCreationService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new IoTDeviceCreationService(null!, _mockApiClient.Object, _mockConfig.Object, _mockEnvironmentService.Object));
        }

        [Fact]
        public void Constructor_WithNullApiClient_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new IoTDeviceCreationService(_mockLogger.Object, null!, _mockConfig.Object, _mockEnvironmentService.Object));
        }

        [Fact]
        public void Constructor_WithNullConfig_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new IoTDeviceCreationService(_mockLogger.Object, _mockApiClient.Object, null!, _mockEnvironmentService.Object));
        }

        [Fact]
        public void Constructor_WithNullEnvironmentService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new IoTDeviceCreationService(_mockLogger.Object, _mockApiClient.Object, _mockConfig.Object, null!));
        }

        #endregion

        #region ValidateIoTApiConnectivityAsync Tests

        [Fact]
        public async Task ValidateIoTApiConnectivityAsync_WhenAlreadyAuthenticated_ShouldReturnTrue()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(true);

            // Act
            var result = await service.ValidateIoTApiConnectivityAsync();

            // Assert
            Assert.True(result);
            _mockApiClient.Verify(x => x.TestAuthenticationAsync(), Times.Once);
            _mockApiClient.Verify(x => x.AuthenticateAsync(), Times.Never);
        }

        [Fact]
        public async Task ValidateIoTApiConnectivityAsync_WhenNotAuthenticatedButCanAuthenticate_ShouldReturnTrue()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(false);
            _mockApiClient.Setup(x => x.AuthenticateAsync()).ReturnsAsync(true);

            // Act
            var result = await service.ValidateIoTApiConnectivityAsync();

            // Assert
            Assert.True(result);
            _mockApiClient.Verify(x => x.TestAuthenticationAsync(), Times.Once);
            _mockApiClient.Verify(x => x.AuthenticateAsync(), Times.Once);
        }

        [Fact]
        public async Task ValidateIoTApiConnectivityAsync_WhenAuthenticationFails_ShouldReturnFalse()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(false);
            _mockApiClient.Setup(x => x.AuthenticateAsync()).ReturnsAsync(false);

            // Act
            var result = await service.ValidateIoTApiConnectivityAsync();

            // Assert
            Assert.False(result);
            _mockApiClient.Verify(x => x.TestAuthenticationAsync(), Times.Once);
            _mockApiClient.Verify(x => x.AuthenticateAsync(), Times.Once);
        }

        [Fact]
        public async Task ValidateIoTApiConnectivityAsync_WhenExceptionThrown_ShouldReturnFalse()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ThrowsAsync(new Exception("API error"));

            // Act
            var result = await service.ValidateIoTApiConnectivityAsync();

            // Assert
            Assert.False(result);
        }

        #endregion

        #region CreateIoTDeviceBatchAsync Tests

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WithValidRequests_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = CreateTestIoTDeviceRequests(3);

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulModuleCreation();
            SetupDealerLookup("Test Dealer", Guid.NewGuid());

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests, batchSize: 10);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(3, result.TotalRequests);
            Assert.Equal(3, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);
            Assert.True(result.Duration > TimeSpan.Zero);
        }

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WithEmptyRequests_ShouldReturnSuccessWithZeroCounts()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = new List<IoTDeviceCreateRequest>();

            SetupSuccessfulApiAuthentication();

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(0, result.TotalRequests);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);
        }

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WhenApiAuthenticationFails_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = CreateTestIoTDeviceRequests(2);

            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(false);
            _mockApiClient.Setup(x => x.AuthenticateAsync()).ReturnsAsync(false);

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(2, result.TotalRequests);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(0, result.FailedRequests);
            Assert.Contains("API authentication failed", result.Errors);
        }

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WithDuplicateDeviceId_ShouldSkipDuplicate()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = CreateTestIoTDeviceRequests(1);

            SetupSuccessfulApiAuthentication();
            SetupExistingModuleCheck(requests.First().IoTDeviceID, Guid.NewGuid());
            SetupDealerLookup("Test Dealer", Guid.NewGuid());

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(1, result.TotalRequests);
            Assert.Equal(1, result.SuccessfulRequests); // Existing module considered success
            Assert.Equal(0, result.FailedRequests);
        }

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WithInvalidDealer_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = CreateTestIoTDeviceRequests(1);

            SetupSuccessfulApiAuthentication();
            SetupNonExistingModuleCheck(requests.First().IoTDeviceID);
            SetupDealerLookup("Invalid Dealer", null); // Dealer not found

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.TotalRequests);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(1, result.FailedRequests);
        }

        #endregion

        #region SyncVehicleIoTSettingsAsync Tests

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WithValidDeviceIds_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new[] { "TEST_IOT_001", "TEST_IOT_002", "TEST_IOT_003" };

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulSyncOperation();

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(3, result.TotalDevices);
            Assert.Equal(3, result.SuccessfulSyncs);
            Assert.Equal(0, result.FailedSyncs);
            Assert.Equal(deviceIds, result.SyncedDeviceIds);
            Assert.Empty(result.FailedDeviceIds);
            Assert.True(result.Duration > TimeSpan.Zero);
        }

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WithEmptyDeviceIds_ShouldReturnSuccessWithZeroCounts()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new string[0];

            SetupSuccessfulApiAuthentication();

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(0, result.TotalDevices);
            Assert.Equal(0, result.SuccessfulSyncs);
            Assert.Equal(0, result.FailedSyncs);
        }

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WhenApiAuthenticationFails_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new[] { "TEST_IOT_001" };

            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(false);
            _mockApiClient.Setup(x => x.AuthenticateAsync()).ReturnsAsync(false);

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.TotalDevices);
            Assert.Equal(0, result.SuccessfulSyncs);
            Assert.Equal(0, result.FailedSyncs);
            Assert.Contains("API authentication failed for sync operation", result.Errors);
        }

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WithPartialFailures_ShouldReturnMixedResult()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new[] { "TEST_IOT_001", "TEST_IOT_002", "TEST_IOT_003" };

            SetupSuccessfulApiAuthentication();
            SetupPartialSyncFailures();

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success); // Should be false if any failures
            Assert.Equal(3, result.TotalDevices);
            Assert.Equal(2, result.SuccessfulSyncs);
            Assert.Equal(1, result.FailedSyncs);
            Assert.Equal(2, result.SyncedDeviceIds.Count);
            Assert.Single(result.FailedDeviceIds);
        }

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WhenSyncApiCallFails_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new[] { "TEST_IOT_001" };

            SetupSuccessfulApiAuthentication();
            _mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
                .ReturnsAsync(new ApiResult<object> { Success = false, ErrorMessage = "Sync API error" });

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.TotalDevices);
            Assert.Equal(0, result.SuccessfulSyncs);
            Assert.Equal(1, result.FailedSyncs);
            Assert.Contains("TEST_IOT_001", result.FailedDeviceIds);
        }

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WithCancellation_ShouldStopProcessing()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new[] { "TEST_IOT_001", "TEST_IOT_002", "TEST_IOT_003" };
            var cancellationTokenSource = new CancellationTokenSource();

            SetupSuccessfulApiAuthentication();

            // Setup to cancel after first call
            var callCount = 0;
            _mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
                .Returns(() =>
                {
                    callCount++;
                    if (callCount == 1)
                    {
                        cancellationTokenSource.Cancel();
                        return Task.FromResult(new ApiResult<object> { Success = true, Data = new object() });
                    }
                    return Task.FromResult(new ApiResult<object> { Success = true, Data = new object() });
                });

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds, cancellationTokenSource.Token);

            // Assert
            Assert.NotNull(result);
            // Should have processed at least one before cancellation
            Assert.True(result.SuccessfulSyncs >= 1);
            Assert.True(result.SuccessfulSyncs < deviceIds.Length);
        }

        #endregion

        #region Error Handling and Edge Cases Tests

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WithNullIoTDeviceId_ShouldReturnFailure()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = new List<IoTDeviceCreateRequest>
            {
                new IoTDeviceCreateRequest
                {
                    IoTDeviceID = null!, // Invalid
                    Dealer = "Test Dealer",
                    CCID = 1001,
                    RANumber = 2001,
                    TechNumber = 3001
                }
            };

            SetupSuccessfulApiAuthentication();

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.TotalRequests);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(1, result.FailedRequests);
        }

        [Fact]
        public async Task CreateIoTDeviceBatchAsync_WithApiException_ShouldHandleGracefully()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var requests = CreateTestIoTDeviceRequests(1);

            SetupSuccessfulApiAuthentication();
            SetupNonExistingModuleCheck(requests.First().IoTDeviceID);
            SetupDealerLookup("Test Dealer", Guid.NewGuid());

            _mockApiClient.Setup(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new Exception("API connection error"));

            // Act
            var result = await service.CreateIoTDeviceBatchAsync(requests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.TotalRequests);
            Assert.Equal(0, result.SuccessfulRequests);
            Assert.Equal(1, result.FailedRequests);
            Assert.Contains("API connection error", result.Errors.First());
        }

        [Fact]
        public async Task SyncVehicleIoTSettingsAsync_WithSyncException_ShouldHandleGracefully()
        {
            // Arrange
            var service = CreateIoTDeviceCreationService();
            var deviceIds = new[] { "TEST_IOT_001" };

            SetupSuccessfulApiAuthentication();
            _mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new Exception("Sync connection error"));

            // Act
            var result = await service.SyncVehicleIoTSettingsAsync(deviceIds);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(1, result.TotalDevices);
            Assert.Equal(0, result.SuccessfulSyncs);
            Assert.Equal(1, result.FailedSyncs);
            Assert.Contains("TEST_IOT_001", result.FailedDeviceIds);
            Assert.Contains("Sync connection error", result.Errors.First());
        }

        #endregion

        #region Helper Methods

        private IoTDeviceCreationService CreateIoTDeviceCreationService()
        {
            return new IoTDeviceCreationService(
                _mockLogger.Object,
                _mockApiClient.Object,
                _mockConfig.Object,
                _mockEnvironmentService.Object);
        }

        private List<IoTDeviceCreateRequest> CreateTestIoTDeviceRequests(int count)
        {
            var requests = new List<IoTDeviceCreateRequest>();
            for (int i = 1; i <= count; i++)
            {
                requests.Add(new IoTDeviceCreateRequest
                {
                    IoTDeviceID = $"TEST_IOT_{i:D3}",
                    Dealer = "Test Dealer",
                    CCID = 1000 + i,
                    RANumber = 2000 + i,
                    TechNumber = 3000 + i
                });
            }
            return requests;
        }

        private void SetupSuccessfulApiAuthentication()
        {
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(true);
        }

        private void SetupSuccessfulModuleCreation()
        {
            var apiResponse = new IoTDeviceApiResponse
            {
                InternalObjectId = 1,
                PrimaryKey = Guid.NewGuid().ToString(),
                ObjectsDataSet = new object()
            };

            _mockApiClient.Setup(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()))
                .ReturnsAsync(new ApiResult<IoTDeviceApiResponse> { Success = true, Data = apiResponse });
        }

        private void SetupExistingModuleCheck(string iotDeviceId, Guid existingModuleId)
        {
            // This would require mocking the database connection, which is complex
            // For now, we'll assume the service handles this internally
        }

        private void SetupNonExistingModuleCheck(string iotDeviceId)
        {
            // This would require mocking the database connection
            // For now, we'll assume the service handles this internally
        }

        private void SetupDealerLookup(string dealerName, Guid? dealerId)
        {
            // This would require mocking the database connection
            // For now, we'll assume the service handles this internally
        }

        private void SetupSuccessfulSyncOperation()
        {
            _mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
                .ReturnsAsync(new ApiResult<object> { Success = true, Data = new object() });
        }

        private void SetupPartialSyncFailures()
        {
            var callCount = 0;
            _mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
                .Returns(() =>
                {
                    callCount++;
                    if (callCount == 2) // Second call fails
                    {
                        return Task.FromResult(new ApiResult<object> { Success = false, ErrorMessage = "Sync failed for device" });
                    }
                    return Task.FromResult(new ApiResult<object> { Success = true, Data = new object() });
                });
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
