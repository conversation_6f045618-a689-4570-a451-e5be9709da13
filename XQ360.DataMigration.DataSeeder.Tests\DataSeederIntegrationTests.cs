using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.DataSeeder.Tests.Mocks;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Integration tests that verify the interaction between DataSeeder services and their dependencies
    /// Tests service integration, dependency injection, and cross-component communication
    /// Uses mocked external dependencies but real service implementations
    /// </summary>
    [Trait("Category", TestCategories.Integration)]
    public class DataSeederIntegrationTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly Mock<IApiOrchestrationService> _mockApiOrchestrationService;
        private readonly Mock<IComplexEntityCreationService> _mockComplexEntityService;
        private readonly Mock<IDatabaseConnectionFactory> _mockConnectionFactory;
        // Mock<IStagingSchemaService> removed - service no longer used
        private readonly Mock<IHubContext<MigrationHub>> _mockHubContext;

        public DataSeederIntegrationTests()
        {
            // Setup mocks for external dependencies
            _mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            _mockComplexEntityService = new Mock<IComplexEntityCreationService>();
            _mockConnectionFactory = new Mock<IDatabaseConnectionFactory>();
            // Mock<IStagingSchemaService> initialization removed - service no longer used
            _mockHubContext = new Mock<IHubContext<MigrationHub>>();

            // Setup SignalR hub context
            var mockClients = new Mock<IHubClients>();
            var mockClientProxy = new Mock<IClientProxy>();
            var mockGroupManager = new Mock<IGroupManager>();
            _mockHubContext.Setup(x => x.Clients).Returns(mockClients.Object);
            _mockHubContext.Setup(x => x.Groups).Returns(mockGroupManager.Object);
            mockClients.Setup(x => x.All).Returns(mockClientProxy.Object);
            mockClients.Setup(x => x.Group(It.IsAny<string>())).Returns(mockClientProxy.Object);

            // Setup mock database connection factory
            _mockConnectionFactory.Setup(x => x.CreateConnection())
                .Returns(() => new MockDatabaseConnection());

            // Setup service collection for dependency injection
            var services = new ServiceCollection();

            // Add logging
            services.AddLogging(builder => builder.AddConsole());

            // Add configuration
            var bulkSeederConfig = TestConfigurationHelper.GetBulkSeederConfiguration();
            services.AddSingleton(Options.Create(bulkSeederConfig));

            // Add environment configuration service
            var environmentService = TestConfigurationHelper.GetEnvironmentService();
            services.AddSingleton<IEnvironmentConfigurationService>(environmentService);

            // Add mocked services
            services.AddSingleton(_mockApiOrchestrationService.Object);
            services.AddSingleton(_mockComplexEntityService.Object);
            services.AddSingleton(_mockConnectionFactory.Object);
            // Mock IStagingSchemaService registration removed - service no longer used
            services.AddSingleton(_mockHubContext.Object);

            // Add real services under test
            services.AddTransient<ISqlDataGenerationService, SqlDataGenerationService>();
            services.AddTransient<IBulkSeederService, BulkSeederService>();
            services.AddTransient<IMigrationPatternSeederService, MigrationPatternSeederService>();

            _serviceProvider = services.BuildServiceProvider();
        }

        #region Service Integration Tests





        #endregion

        #region SignalR Integration Tests

        [Fact]
        public async Task BulkSeederService_ShouldSendSignalRNotifications()
        {
            // Arrange
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var options = TestConfigurationHelper.CreateTestSeederOptions(driversCount: 1, vehiclesCount: 1);

            // Act
            await bulkSeederService.ExecuteSeederAsync(options);

            // Assert
            // Verify that SignalR notifications were sent
            var mockClientProxy = Mock.Get(_mockHubContext.Object.Clients.All);
            mockClientProxy.Verify(x => x.SendCoreAsync(
                "UpdateSeederProgress",
                It.IsAny<object[]>(),
                It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        }



        #endregion

        #region Configuration Integration Tests

        [Fact]
        public void Services_ShouldUseCorrectConfiguration()
        {
            // Arrange & Act
            var bulkSeederService = _serviceProvider.GetRequiredService<IBulkSeederService>();
            var migrationSeederService = _serviceProvider.GetRequiredService<IMigrationPatternSeederService>();
            var sqlDataService = _serviceProvider.GetRequiredService<ISqlDataGenerationService>();

            // Assert
            Assert.NotNull(bulkSeederService);
            Assert.NotNull(migrationSeederService);
            Assert.NotNull(sqlDataService);
        }

        [Fact]
        public void EnvironmentConfigurationService_ShouldProvideCorrectConfiguration()
        {
            // Arrange
            var environmentService = _serviceProvider.GetRequiredService<IEnvironmentConfigurationService>();

            // Act
            var currentEnvironment = environmentService.CurrentEnvironment;
            var migrationConfig = environmentService.CurrentMigrationConfiguration;

            // Assert
            Assert.NotNull(currentEnvironment);
            Assert.NotNull(migrationConfig);
            Assert.NotEmpty(currentEnvironment.DatabaseConnection);
            Assert.NotEmpty(currentEnvironment.ApiBaseUrl);
        }

        #endregion

        #region Error Handling Integration Tests





        #endregion

        #region Cancellation Integration Tests





        #endregion

        #region Dependency Injection Tests

        [Fact]
        public void ServiceProvider_ShouldResolveAllRequiredServices()
        {
            // Act & Assert
            Assert.NotNull(_serviceProvider.GetRequiredService<IBulkSeederService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IMigrationPatternSeederService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<ISqlDataGenerationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IEnvironmentConfigurationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IApiOrchestrationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IComplexEntityCreationService>());
        }

        [Fact]
        public void ServiceProvider_ShouldCreateSingletonServices()
        {
            // Act
            var environmentService1 = _serviceProvider.GetRequiredService<IEnvironmentConfigurationService>();
            var environmentService2 = _serviceProvider.GetRequiredService<IEnvironmentConfigurationService>();

            // Assert
            Assert.Same(environmentService1, environmentService2);
        }

        #endregion

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}
