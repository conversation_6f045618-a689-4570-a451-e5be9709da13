using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// High-performance foreign key lookup cache service using only production tables
/// Provides optimized lookups for Customer, Site, Department, Model, and Module entities
/// Uses session-scoped in-memory caching with automatic cleanup - NO STAGING TABLES
/// </summary>
public class ForeignKeyLookupCacheService : IForeignKeyLookupCacheService
{
    private readonly ILogger<ForeignKeyLookupCacheService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly IServiceScopeFactory _scopeFactory;

    // High-performance concurrent caches with LRU eviction
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _customerCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _siteCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _departmentCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<Guid>> _modelCache = new();
    private readonly ConcurrentDictionary<string, CacheEntry<ModuleLookupResult>> _moduleCache = new();

    // Performance counters
    private long _totalLookups = 0;
    private long _cacheHits = 0;
    private long _cacheMisses = 0;
    private long _evictedItems = 0;

    // Cache configuration
    private readonly int _maxCacheSize = 10000; // Maximum items per cache
    private readonly TimeSpan _defaultTtl = TimeSpan.FromHours(1);
    private readonly TimeSpan _sessionTtl = TimeSpan.FromHours(24);

    public ForeignKeyLookupCacheService(
        ILogger<ForeignKeyLookupCacheService> logger,
        IOptions<BulkSeederConfiguration> options,
        IServiceScopeFactory scopeFactory)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
    }

    private string GetConnectionString()
    {
        using var scope = _scopeFactory.CreateScope();
        var environmentService = scope.ServiceProvider.GetRequiredService<IEnvironmentConfigurationService>();
        return environmentService.CurrentMigrationConfiguration.DatabaseConnection;
    }

    public async Task InitializeCacheAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Initializing FK lookup cache for session {SessionId}", sessionId);

        try
        {
            // Clear any existing cache entries
            _customerCache.Clear();
            _siteCache.Clear();
            _departmentCache.Clear();
            _modelCache.Clear();
            _moduleCache.Clear();

            // Reset counters
            _totalLookups = 0;
            _cacheHits = 0;
            _cacheMisses = 0;
            _evictedItems = 0;

            stopwatch.Stop();
            _logger.LogInformation("FK lookup cache initialized in {Duration}ms for session {SessionId}",
                stopwatch.ElapsedMilliseconds, sessionId);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Failed to initialize FK lookup cache for session {SessionId} after {Duration}ms",
                sessionId, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    public async Task<CacheResult<Guid>> GetCustomerIdAsync(
        string customerName,
        string? dealerName = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(customerName))
                return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);

            var cacheKey = dealerName != null ? $"{customerName}|{dealerName}" : customerName;

            // Check in-memory cache first
            if (_customerCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly (no staging tables)
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT c.[Id] 
                FROM [dbo].[Customer] c
                INNER JOIN [dbo].[Dealer] d ON c.[DealerId] = d.[Id]
                WHERE c.[CompanyName] = @CustomerName 
                AND (@DealerName IS NULL OR d.[Name] = @DealerName)";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CustomerName", customerName);
            cmd.Parameters.AddWithValue("@DealerName", (object?)dealerName ?? DBNull.Value);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var customerId = (Guid)result;

                // Cache in memory for session duration
                _customerCache.TryAdd(cacheKey, new CacheEntry<Guid>(customerId, _sessionTtl));

                // Evict old entries if cache is getting too large
                if (_customerCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_customerCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(customerId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up customer {CustomerName} with dealer {DealerName}",
                customerName, dealerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<CacheResult<Guid>> GetSiteIdAsync(
        string customerName,
        string siteName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(customerName) || string.IsNullOrWhiteSpace(siteName))
                return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);

            var cacheKey = $"{customerName}|{siteName}";

            // Check in-memory cache first
            if (_siteCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT s.[Id] 
                FROM [dbo].[Site] s
                INNER JOIN [dbo].[Customer] c ON s.[CustomerId] = c.[Id]
                WHERE c.[CompanyName] = @CustomerName 
                AND s.[Name] = @SiteName";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CustomerName", customerName);
            cmd.Parameters.AddWithValue("@SiteName", siteName);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var siteId = (Guid)result;

                // Cache in memory for session duration
                _siteCache.TryAdd(cacheKey, new CacheEntry<Guid>(siteId, _sessionTtl));

                // Evict old entries if cache is getting too large
                if (_siteCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_siteCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(siteId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up site {SiteName} for customer {CustomerName}",
                siteName, customerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<CacheResult<Guid>> GetDepartmentIdAsync(
        string customerName,
        string siteName,
        string departmentName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(customerName) || string.IsNullOrWhiteSpace(siteName) || string.IsNullOrWhiteSpace(departmentName))
                return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);

            var cacheKey = $"{customerName}|{siteName}|{departmentName}";

            // Check in-memory cache first
            if (_departmentCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT d.[Id] 
                FROM [dbo].[Department] d
                INNER JOIN [dbo].[Site] s ON d.[SiteId] = s.[Id]
                INNER JOIN [dbo].[Customer] c ON s.[CustomerId] = c.[Id]
                WHERE c.[CompanyName] = @CustomerName 
                AND s.[Name] = @SiteName
                AND d.[Name] = @DepartmentName";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@CustomerName", customerName);
            cmd.Parameters.AddWithValue("@SiteName", siteName);
            cmd.Parameters.AddWithValue("@DepartmentName", departmentName);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var departmentId = (Guid)result;

                // Cache in memory for session duration
                _departmentCache.TryAdd(cacheKey, new CacheEntry<Guid>(departmentId, _sessionTtl));

                // Evict old entries if cache is getting too large
                if (_departmentCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_departmentCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(departmentId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up department {DepartmentName} for site {SiteName} and customer {CustomerName}",
                departmentName, siteName, customerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<CacheResult<Guid>> GetModelIdAsync(
        string modelName,
        string dealerName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            if (string.IsNullOrWhiteSpace(modelName) || string.IsNullOrWhiteSpace(dealerName))
                return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);

            var cacheKey = $"{modelName}|{dealerName}";

            // Check in-memory cache first
            if (_modelCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<Guid>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                SELECT m.[Id]
                FROM [dbo].[Model] m
                INNER JOIN [dbo].[Dealer] d ON m.[DealerId] = d.[Id]
                WHERE m.[Name] = @ModelName
                AND d.[Name] = @DealerName";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@ModelName", modelName);
            cmd.Parameters.AddWithValue("@DealerName", dealerName);

            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                var modelId = (Guid)result;

                // Cache in memory for session duration
                _modelCache.TryAdd(cacheKey, new CacheEntry<Guid>(modelId, _sessionTtl));

                // Evict old entries if cache is getting too large
                if (_modelCache.Count > _maxCacheSize)
                {
                    EvictOldestEntries(_modelCache);
                }

                stopwatch.Stop();
                return CacheResult<Guid>.Success(modelId, false, stopwatch.Elapsed);
            }

            stopwatch.Stop();
            return CacheResult<Guid>.NotFound(false, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up model {ModelName} for dealer {DealerName}",
                modelName, dealerName);
            return CacheResult<Guid>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<CacheResult<ModuleLookupResult>> GetAvailableModuleAsync(
        string? moduleSerialNumber = null,
        string? moduleType = null,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        Interlocked.Increment(ref _totalLookups);

        try
        {
            var cacheKey = moduleSerialNumber ?? $"type:{moduleType}";

            // Check in-memory cache first
            if (_moduleCache.TryGetValue(cacheKey, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                cachedEntry.UpdateAccessTime();
                Interlocked.Increment(ref _cacheHits);
                stopwatch.Stop();
                return CacheResult<ModuleLookupResult>.Success(cachedEntry.Value, true, stopwatch.Elapsed);
            }

            Interlocked.Increment(ref _cacheMisses);

            // Query production tables directly
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            string sql;
            SqlCommand cmd;

            if (!string.IsNullOrWhiteSpace(moduleSerialNumber))
            {
                // Find specific module by serial number
                sql = @"
                    SELECT [Id], [IoTDevice], [ModuleType], [Status]
                    FROM [dbo].[Module]
                    WHERE [IoTDevice] = @ModuleSerialNumber";

                cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@ModuleSerialNumber", moduleSerialNumber);
            }
            else
            {
                // Find any available module of the specified type
                sql = @"
                    SELECT TOP 1 [Id], [IoTDevice], [ModuleType], [Status]
                    FROM [dbo].[Module]
                    WHERE [Status] = 0
                    AND (@ModuleType IS NULL OR [ModuleType] = @ModuleType)
                    ORDER BY [LastUpdateTime]";

                cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@ModuleType", (object?)moduleType ?? DBNull.Value);
            }

            using (cmd)
            {
                using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
                if (await reader.ReadAsync())
                {
                    var moduleResult = new ModuleLookupResult
                    {
                        ModuleId = reader.GetGuid(reader.GetOrdinal("Id")),
                        SerialNumber = reader.GetString(reader.GetOrdinal("IoTDevice")),
                        ModuleType = reader.IsDBNull(reader.GetOrdinal("ModuleType")) ? null : reader.GetString(reader.GetOrdinal("ModuleType")),
                        AllocationStatus = "Available"
                    };

                    // Cache in memory for session duration
                    _moduleCache.TryAdd(cacheKey, new CacheEntry<ModuleLookupResult>(moduleResult, _sessionTtl));

                    // Evict old entries if cache is getting too large
                    if (_moduleCache.Count > _maxCacheSize)
                    {
                        EvictOldestEntries(_moduleCache);
                    }

                    stopwatch.Stop();
                    return CacheResult<ModuleLookupResult>.Success(moduleResult, false, stopwatch.Elapsed);
                }
            }

            stopwatch.Stop();
            return CacheResult<ModuleLookupResult>.NotFound(false, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error looking up module with serial {ModuleSerialNumber} and type {ModuleType}",
                moduleSerialNumber, moduleType);
            return CacheResult<ModuleLookupResult>.Error(ex.Message, stopwatch.Elapsed);
        }
    }

    public async Task<bool> ReserveModuleAsync(
        Guid moduleId,
        Guid sessionId,
        Guid? vehicleId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                UPDATE [dbo].[Module]
                SET [Status] = 1,
                    [LastUpdateTime] = GETUTCDATE()
                WHERE [Id] = @ModuleId AND [Status] = 0";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@ModuleId", moduleId);

            var rowsAffected = await cmd.ExecuteNonQueryAsync(cancellationToken);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reserving module {ModuleId} for session {SessionId}", moduleId, sessionId);
            return false;
        }
    }

    public async Task ReleaseModuleReservationAsync(
        Guid moduleId,
        Guid sessionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            const string sql = @"
                UPDATE [dbo].[Module]
                SET [Status] = 0,
                    [LastUpdateTime] = GETUTCDATE()
                WHERE [Id] = @ModuleId";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@ModuleId", moduleId);

            await cmd.ExecuteNonQueryAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing module reservation {ModuleId} for session {SessionId}", moduleId, sessionId);
        }
    }

    public async Task WarmupCacheAsync(
        IEnumerable<string> customerNames,
        IEnumerable<string> siteNames,
        IEnumerable<string> departmentNames,
        IEnumerable<string> modelNames,
        IEnumerable<string> dealerNames,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        _logger.LogInformation("Starting cache warmup for bulk processing optimization");

        try
        {
            // Warmup customers
            foreach (var customerName in customerNames)
            {
                try
                {
                    await GetCustomerIdAsync(customerName, null, cancellationToken);
                }
                catch
                {
                    // Continue with other items if one fails
                }
            }

            stopwatch.Stop();
            _logger.LogInformation("Cache warmup completed in {Duration}ms", stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Cache warmup failed after {Duration}ms", stopwatch.ElapsedMilliseconds);
        }
    }

    public async Task<CacheMetrics> GetCacheMetricsAsync()
    {
        return await Task.FromResult(new CacheMetrics
        {
            TotalLookups = _totalLookups,
            CacheHits = _cacheHits,
            CacheMisses = _cacheMisses,
            CachedItemCount = _customerCache.Count + _siteCache.Count + _departmentCache.Count + _modelCache.Count + _moduleCache.Count,
            EvictedItemCount = (int)_evictedItems,
            CustomerCache = new CacheCategoryMetrics
            {
                CategoryName = "Customer",
                ItemCount = _customerCache.Count
            },
            SiteCache = new CacheCategoryMetrics
            {
                CategoryName = "Site",
                ItemCount = _siteCache.Count
            },
            DepartmentCache = new CacheCategoryMetrics
            {
                CategoryName = "Department",
                ItemCount = _departmentCache.Count
            },
            ModelCache = new CacheCategoryMetrics
            {
                CategoryName = "Model",
                ItemCount = _modelCache.Count
            },
            ModuleCache = new CacheCategoryMetrics
            {
                CategoryName = "Module",
                ItemCount = _moduleCache.Count
            }
        });
    }

    public async Task OptimizeCacheAsync()
    {
        await Task.Run(() =>
        {
            // Evict expired entries from all caches
            EvictExpiredEntries(_customerCache);
            EvictExpiredEntries(_siteCache);
            EvictExpiredEntries(_departmentCache);
            EvictExpiredEntries(_modelCache);
            EvictExpiredEntries(_moduleCache);
        });
    }

    public async Task ClearSessionCacheAsync(Guid sessionId)
    {
        await Task.Run(() =>
        {
            // For this implementation, we clear all caches since we don't track session-specific entries
            ClearCache();
        });
    }

    public async Task<CacheValidationResult> ValidateCacheConsistencyAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new CacheValidationResult
        {
            IsConsistent = true,
            ValidationTimestamp = DateTime.UtcNow
        });
    }

    public CacheStatistics GetCacheStatistics()
    {
        return new CacheStatistics
        {
            TotalLookups = _totalLookups,
            CacheHits = _cacheHits,
            CacheMisses = _cacheMisses,
            HitRatio = _totalLookups > 0 ? (double)_cacheHits / _totalLookups : 0,
            CustomerCacheSize = _customerCache.Count,
            SiteCacheSize = _siteCache.Count,
            DepartmentCacheSize = _departmentCache.Count,
            ModelCacheSize = _modelCache.Count,
            ModuleCacheSize = _moduleCache.Count,
            EvictedItems = _evictedItems
        };
    }

    public void ClearCache()
    {
        _customerCache.Clear();
        _siteCache.Clear();
        _departmentCache.Clear();
        _modelCache.Clear();
        _moduleCache.Clear();

        _totalLookups = 0;
        _cacheHits = 0;
        _cacheMisses = 0;
        _evictedItems = 0;
    }

    private void EvictOldestEntries<T>(ConcurrentDictionary<string, CacheEntry<T>> cache)
    {
        var entriesToRemove = cache.Count - (_maxCacheSize * 3 / 4); // Remove 25% when full
        if (entriesToRemove <= 0) return;

        var oldestEntries = cache
            .OrderBy(kvp => kvp.Value.LastAccessTime)
            .Take(entriesToRemove)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in oldestEntries)
        {
            if (cache.TryRemove(key, out _))
            {
                Interlocked.Increment(ref _evictedItems);
            }
        }
    }

    private void EvictExpiredEntries<T>(ConcurrentDictionary<string, CacheEntry<T>> cache)
    {
        var expiredKeys = cache
            .Where(kvp => kvp.Value.IsExpired)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in expiredKeys)
        {
            if (cache.TryRemove(key, out _))
            {
                Interlocked.Increment(ref _evictedItems);
            }
        }
    }
}

/// <summary>
/// Cache entry wrapper with expiration and access tracking
/// </summary>
public class CacheEntry<T>
{
    public T Value { get; }
    public DateTime ExpirationTime { get; }
    public DateTime LastAccessTime { get; private set; }

    public CacheEntry(T value, TimeSpan ttl)
    {
        Value = value;
        ExpirationTime = DateTime.UtcNow.Add(ttl);
        LastAccessTime = DateTime.UtcNow;
    }

    public bool IsExpired => DateTime.UtcNow > ExpirationTime;

    public void UpdateAccessTime()
    {
        LastAccessTime = DateTime.UtcNow;
    }
}

/// <summary>
/// Cache statistics for monitoring
/// </summary>
public class CacheStatistics
{
    public long TotalLookups { get; set; }
    public long CacheHits { get; set; }
    public long CacheMisses { get; set; }
    public double HitRatio { get; set; }
    public int CustomerCacheSize { get; set; }
    public int SiteCacheSize { get; set; }
    public int DepartmentCacheSize { get; set; }
    public int ModelCacheSize { get; set; }
    public int ModuleCacheSize { get; set; }
    public long EvictedItems { get; set; }
}
