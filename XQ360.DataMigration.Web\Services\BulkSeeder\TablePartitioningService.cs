using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using System.Data;
using System.Diagnostics;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Table partitioning service for Phase 3.2.3: Table Partitioning
/// Implements SessionId-based partitioning for improved concurrency and performance
/// </summary>
public class TablePartitioningService : ITablePartitioningService
{
    private readonly ILogger<TablePartitioningService> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly BulkSeederConfiguration _config;

    public TablePartitioningService(
        ILogger<TablePartitioningService> logger,
        IEnvironmentConfigurationService environmentService,
        IOptions<BulkSeederConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
    }

    public async Task<PartitioningResult> InitializeTablePartitioningAsync(
        PartitioningOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PartitioningResult
        {
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting table partitioning initialization");

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            // Execute the partitioning script
            var scriptPath = "Scripts/005-CreateTablePartitioning.sql";
            var scriptContent = await File.ReadAllTextAsync(scriptPath, cancellationToken).ConfigureAwait(false);
            
            await ExecutePartitioningScriptAsync(connection, scriptContent, result, cancellationToken)
                .ConfigureAwait(false);

            // Verify partitioning was successful
            result.PartitioningInfo = await GetPartitionInfoAsync(connection, cancellationToken)
                .ConfigureAwait(false);

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Table partitioning initialization completed successfully in {Duration}ms. Created {PartitionCount} partitions",
                result.Duration.TotalMilliseconds, result.PartitioningInfo.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Table partitioning initialization failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<List<PartitionInfo>> GetPartitionInfoAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Retrieving partition information");

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            return await GetPartitionInfoAsync(connection, cancellationToken).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve partition information");
            return new List<PartitionInfo>();
        }
    }

    public async Task<PartitionCleanupResult> CleanupSessionPartitionsAsync(
        PartitionCleanupOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PartitionCleanupResult
        {
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting session partition cleanup for session {SessionId}", options.SessionId);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            using var command = new SqlCommand("dbo.sp_CleanupSessionPartitions", connection)
            {
                CommandType = CommandType.StoredProcedure,
                CommandTimeout = options.CommandTimeoutSeconds
            };

            // Add parameters based on cleanup type
            if (options.SessionId.HasValue)
            {
                command.Parameters.AddWithValue("@SessionId", options.SessionId.Value);
            }
            else
            {
                command.Parameters.AddWithValue("@SessionId", DBNull.Value);
                command.Parameters.AddWithValue("@CleanupOlderThanHours", options.CleanupOlderThanHours);
            }

            command.Parameters.AddWithValue("@DryRun", options.DryRun);

            // Execute cleanup
            if (options.DryRun)
            {
                using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);
                var dryRunResults = new List<PartitionCleanupItem>();

                while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
                {
                    dryRunResults.Add(new PartitionCleanupItem
                    {
                        TableName = reader.GetString("TableName"),
                        RecordsToDelete = reader.GetInt32("RecordsToDelete")
                    });
                }

                result.DryRunResults = dryRunResults;
                result.TotalRecordsToDelete = dryRunResults.Sum(r => r.RecordsToDelete);
            }
            else
            {
                await command.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);
                result.CleanupExecuted = true;
            }

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Session partition cleanup completed in {Duration}ms. {Action}: {RecordCount} records",
                result.Duration.TotalMilliseconds, 
                options.DryRun ? "Would delete" : "Deleted", 
                result.TotalRecordsToDelete);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Session partition cleanup failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<PartitionSwitchResult> SwitchOutPartitionAsync(
        Guid sessionId,
        string tableName,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PartitionSwitchResult
        {
            SessionId = sessionId,
            TableName = tableName,
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting partition switch for session {SessionId} on table {TableName}", 
            sessionId, tableName);

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            // Get partition number for the session
            result.PartitionNumber = await GetPartitionNumberAsync(connection, sessionId, cancellationToken)
                .ConfigureAwait(false);

            // Get record count before switch
            result.RecordsSwitched = await GetPartitionRecordCountAsync(
                connection, tableName, sessionId, cancellationToken).ConfigureAwait(false);

            // Execute partition switch
            using var command = new SqlCommand("dbo.sp_SwitchOutStagingPartition", connection)
            {
                CommandType = CommandType.StoredProcedure,
                CommandTimeout = 300 // 5 minutes for partition operations
            };

            command.Parameters.AddWithValue("@SessionId", sessionId);
            command.Parameters.AddWithValue("@TableName", tableName);

            await command.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Partition switch completed for session {SessionId}: switched out {RecordCount} records from partition {PartitionNumber} in {Duration}ms",
                sessionId, result.RecordsSwitched, result.PartitionNumber, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Partition switch failed for session {SessionId} on table {TableName}", 
                sessionId, tableName);
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    public async Task<List<PartitionUsageStatistic>> GetPartitionUsageStatisticsAsync(
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Retrieving partition usage statistics");

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            const string query = @"
                SELECT 
                    TableName,
                    PartitionNumber,
                    RowCount,
                    ApproxSizeMB,
                    UsageLevel,
                    IndexName,
                    IndexType
                FROM dbo.vw_PartitionUsageStats
                ORDER BY TableName, PartitionNumber";

            using var command = new SqlCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);

            var statistics = new List<PartitionUsageStatistic>();

            while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                statistics.Add(new PartitionUsageStatistic
                {
                    TableName = reader.GetString("TableName"),
                    PartitionNumber = reader.GetInt32("PartitionNumber"),
                    RowCount = reader.GetInt64("RowCount"),
                    ApproxSizeMB = reader.GetDecimal("ApproxSizeMB"),
                    UsageLevel = reader.GetString("UsageLevel"),
                    IndexName = reader.GetString("IndexName"),
                    IndexType = reader.GetString("IndexType")
                });
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve partition usage statistics");
            return new List<PartitionUsageStatistic>();
        }
    }

    public async Task<PartitionOptimizationResult> OptimizePartitionPerformanceAsync(
        PartitionOptimizationOptions options,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PartitionOptimizationResult
        {
            StartTime = DateTime.UtcNow
        };

        _logger.LogInformation("Starting partition performance optimization");

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

            var optimizationTasks = new List<Task>();

            // Update partition statistics if requested
            if (options.UpdatePartitionStatistics)
            {
                optimizationTasks.Add(UpdatePartitionStatisticsAsync(connection, result, cancellationToken));
            }

            // Rebalance partitions if requested
            if (options.RebalancePartitions)
            {
                optimizationTasks.Add(RebalancePartitionsAsync(connection, options, result, cancellationToken));
            }

            // Cleanup empty partitions if requested
            if (options.CleanupEmptyPartitions)
            {
                optimizationTasks.Add(CleanupEmptyPartitionsAsync(connection, result, cancellationToken));
            }

            // Execute all optimization tasks
            await Task.WhenAll(optimizationTasks).ConfigureAwait(false);

            result.Success = true;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Partition performance optimization completed in {Duration}ms", 
                result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Partition performance optimization failed");
            result.Success = false;
            result.Duration = stopwatch.Elapsed;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            return result;
        }
    }

    private async Task ExecutePartitioningScriptAsync(
        SqlConnection connection,
        string scriptContent,
        PartitioningResult result,
        CancellationToken cancellationToken)
    {
        var commands = scriptContent.Split(new[] { "GO" }, StringSplitOptions.RemoveEmptyEntries);

        foreach (var commandText in commands)
        {
            if (string.IsNullOrWhiteSpace(commandText) || cancellationToken.IsCancellationRequested)
                continue;

            try
            {
                using var command = new SqlCommand(commandText.Trim(), connection)
                {
                    CommandTimeout = 600, // 10 minutes for partitioning operations
                    CommandType = CommandType.Text
                };

                await command.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);
                
                // Track created components
                if (commandText.Contains("CREATE PARTITION FUNCTION", StringComparison.OrdinalIgnoreCase))
                {
                    result.PartitionFunctionCreated = true;
                }
                else if (commandText.Contains("CREATE PARTITION SCHEME", StringComparison.OrdinalIgnoreCase))
                {
                    result.PartitionSchemeCreated = true;
                }
                else if (commandText.Contains("CREATE TABLE", StringComparison.OrdinalIgnoreCase) &&
                         commandText.Contains("Partitioned", StringComparison.OrdinalIgnoreCase))
                {
                    result.PartitionedTablesCreated++;
                }
                else if (commandText.Contains("CREATE NONCLUSTERED INDEX", StringComparison.OrdinalIgnoreCase))
                {
                    result.PartitionAlignedIndexesCreated++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to execute partitioning command: {Command}", 
                    commandText.Substring(0, Math.Min(100, commandText.Length)));
                result.ExecutionErrors.Add($"Command failed: {ex.Message}");
            }
        }
    }

    private async Task<List<PartitionInfo>> GetPartitionInfoAsync(
        SqlConnection connection,
        CancellationToken cancellationToken)
    {
        using var command = new SqlCommand("dbo.sp_GetPartitionInfo", connection)
        {
            CommandType = CommandType.StoredProcedure
        };

        using var reader = await command.ExecuteReaderAsync(cancellationToken).ConfigureAwait(false);
        var partitionInfo = new List<PartitionInfo>();

        while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
        {
            partitionInfo.Add(new PartitionInfo
            {
                TableName = reader.GetString("TableName"),
                PartitionNumber = reader.GetInt32("PartitionNumber"),
                RowCount = reader.GetInt64("RowCount"),
                FileGroupName = reader.GetString("FileGroupName"),
                LowerBoundary = reader.IsDBNull("LowerBoundary") ? null : reader.GetGuid("LowerBoundary"),
                UpperBoundary = reader.IsDBNull("UpperBoundary") ? null : reader.GetGuid("UpperBoundary")
            });
        }

        return partitionInfo;
    }

    private async Task<int> GetPartitionNumberAsync(
        SqlConnection connection,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        const string query = "SELECT $PARTITION.pf_SessionIdPartition(@SessionId) AS PartitionNumber";
        
        using var command = new SqlCommand(query, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);

        var result = await command.ExecuteScalarAsync(cancellationToken).ConfigureAwait(false);
        return (int)result;
    }

    private async Task<int> GetPartitionRecordCountAsync(
        SqlConnection connection,
        string tableName,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        var query = $"SELECT COUNT(*) FROM dbo.{tableName} WHERE SessionId = @SessionId";
        
        using var command = new SqlCommand(query, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);

        var result = await command.ExecuteScalarAsync(cancellationToken).ConfigureAwait(false);
        return (int)result;
    }

    // Additional optimization methods
    private async Task UpdatePartitionStatisticsAsync(
        SqlConnection connection,
        PartitionOptimizationResult result,
        CancellationToken cancellationToken)
    {
        var partitionedTables = new[] { "StagingDriver_Partitioned", "StagingVehicle_Partitioned", "StagingCard_Partitioned" };

        foreach (var table in partitionedTables)
        {
            try
            {
                var updateStatsQuery = $"UPDATE STATISTICS [dbo].[{table}] WITH FULLSCAN";
                using var command = new SqlCommand(updateStatsQuery, connection) { CommandTimeout = 300 };
                await command.ExecuteNonQueryAsync(cancellationToken).ConfigureAwait(false);
                
                result.StatisticsUpdated++;
                _logger.LogDebug("Updated statistics for partitioned table: {TableName}", table);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to update statistics for partitioned table: {TableName}", table);
                result.OptimizationErrors.Add($"Statistics update failed for {table}: {ex.Message}");
            }
        }
    }

    private async Task RebalancePartitionsAsync(
        SqlConnection connection,
        PartitionOptimizationOptions options,
        PartitionOptimizationResult result,
        CancellationToken cancellationToken)
    {
        // Implementation for partition rebalancing would go here
        // This is a placeholder for advanced partition management
        await Task.CompletedTask.ConfigureAwait(false);
        result.PartitionsRebalanced = 0;
    }

    private async Task CleanupEmptyPartitionsAsync(
        SqlConnection connection,
        PartitionOptimizationResult result,
        CancellationToken cancellationToken)
    {
        // Implementation for cleaning up empty partitions would go here
        // This is a placeholder for advanced partition management
        await Task.CompletedTask.ConfigureAwait(false);
        result.EmptyPartitionsCleaned = 0;
    }
}
