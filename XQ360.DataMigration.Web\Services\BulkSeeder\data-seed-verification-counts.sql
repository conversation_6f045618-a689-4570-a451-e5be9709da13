-- ============================================================================
-- Data Seed Verification Counts - XQ360 Data Migration System
-- ============================================================================
-- Purpose: Verify that data seeding operations have successfully populated
--          database tables with the expected number of records
-- Generated: Based on technical documentation and data seeding patterns
-- Usage: Execute this script to count records in all driver and vehicle
--        related tables populated during bulk data seeding operations
-- ============================================================================

-- ============================================================================
-- DRIVER-RELATED TABLES
-- ============================================================================

-- Person Table - Core personnel records containing driver information
-- Contains: FirstName, LastName, Email, Phone, organizational assignments
-- Relationship: Parent table for all personnel, linked to Driver table via DriverId
SELECT COUNT(1) AS PersonRecordCount FROM [dbo].[Person];

-- Driver Table - Driver-specific attributes and permissions
-- Contains: VehicleAccess, Active status, LicenseMode, access permissions
-- Relationship: Child of Person table, references Customer, Department, Site, and Card tables
SELECT COUNT(1) AS DriverRecordCount FROM [dbo].[Driver];

-- PersonAllocation Table - Site and department assignments for personnel
-- Contains: PersonId, SiteId, DepartmentId allocation mappings
-- Relationship: Links Person records to specific organizational units for access control
SELECT COUNT(1) AS PersonAllocationRecordCount FROM [dbo].[PersonAllocation];

-- PersonChecklistLanguageSettings Table - Language preferences for safety checklists
-- Relationship: Referenced by Person table for localized checklist presentation
SELECT COUNT(1) AS PersonChecklistLanguageSettingsRecordCount FROM [dbo].[PersonChecklistLanguageSettings];

-- Card Table - Physical access cards linked to drivers
-- Relationship: Referenced by Driver table via CardDetailsId for physical access
SELECT COUNT(1) AS CardRecordCount FROM [dbo].[Card];

-- ============================================================================
-- VEHICLE-RELATED TABLES
-- ============================================================================

-- Vehicle Table - Core vehicle records with operational settings
-- Contains: SerialNo, HireNo, operational flags (OnHire, ImpactLockout, IsCanbus)
-- Relationship: References multiple supporting tables for complete configuration
SELECT COUNT(1) AS VehicleRecordCount FROM [dbo].[Vehicle];

-- ChecklistSettings Table - Safety checklist configuration for each vehicle
-- Contains: QuestionTimeout, ShowComment, Randomisation, Type, time slots
-- Relationship: One-to-one with Vehicle table, defines safety checklist behavior
-- Data Types: Time-based checklists (Type=2), Driver-based checklists (Type=3)
SELECT COUNT(1) AS ChecklistSettingsRecordCount FROM [dbo].[ChecklistSettings];

-- VehicleOtherSettings Table - Additional vehicle safety and operational settings
-- Contains: FullLockoutTimeout, VORStatus, AmberAlertEnabled, safety flags
-- Relationship: One-to-one with Vehicle table, stores extended vehicle configuration
-- Safety Settings: VORStatusConfirmed, FullLockout, DefaultTechnicianAccess, PedestrianSafety
SELECT COUNT(1) AS VehicleOtherSettingsRecordCount FROM [dbo].[VehicleOtherSettings];

-- Module Table - IoT device allocation and status tracking
-- Contains: Device configuration, calibration settings, allocation status
-- Relationship: Referenced by Vehicle table via ModuleId1, tracks device assignment
-- Updates During Seeding: IsAllocatedToVehicle flag set to true, Status updated
SELECT COUNT(1) AS ModuleRecordCount FROM [dbo].[Module];

-- Module Table - Count of allocated modules (should match vehicle count in ideal seeding)
-- Filters for modules that have been allocated to vehicles during seeding
SELECT COUNT(1) AS AllocatedModuleCount FROM [dbo].[Module] WHERE IsAllocatedToVehicle = 1;

-- ============================================================================
-- ACCESS CONTROL AND SECURITY TABLES
-- ============================================================================

-- CardToCardAccess Table - Card-to-card access permissions
-- Contains: Permission mappings between cards and access levels
-- Relationship: Junction table linking Card and Permission entities
SELECT COUNT(1) AS CardToCardAccessRecordCount FROM [dbo].[CardToCardAccess];

-- SiteVehicleMasterCardAccess Table - Site-level master card access
-- Contains: Master card access permissions at site level
-- Relationship: Links sites to vehicle master card access permissions
SELECT COUNT(1) AS SiteVehicleMasterCardAccessRecordCount FROM [dbo].[SiteVehicleMasterCardAccess];

-- SiteVehicleNormalCardAccess Table - Site-level normal card access
-- Contains: Normal card access permissions at site level
-- Relationship: Links sites to vehicle normal card access permissions with specific permission levels
SELECT COUNT(1) AS SiteVehicleNormalCardAccessRecordCount FROM [dbo].[SiteVehicleNormalCardAccess];

-- DepartmentVehicleMasterCardAccess Table - Department-level master card access
-- Contains: Master card access permissions at department level
-- Relationship: Links departments to vehicle master card access permissions
SELECT COUNT(1) AS DepartmentVehicleMasterCardAccessRecordCount FROM [dbo].[DepartmentVehicleMasterCardAccess];

-- DepartmentVehicleNormalCardAccess Table - Department-level normal card access
-- Contains: Normal card access permissions at department level
-- Relationship: Links departments to vehicle normal card access permissions with specific permission levels
SELECT COUNT(1) AS DepartmentVehicleNormalCardAccessRecordCount FROM [dbo].[DepartmentVehicleNormalCardAccess];

-- ModelVehicleMasterCardAccess Table - Model-level master card access
-- Contains: Master card access permissions based on vehicle model types
-- Relationship: Links vehicle models to master card access permissions
SELECT COUNT(1) AS ModelVehicleMasterCardAccessRecordCount FROM [dbo].[ModelVehicleMasterCardAccess];

-- ModelVehicleNormalCardAccess Table - Model-level normal card access
-- Contains: Normal card access permissions based on vehicle model types
-- Relationship: Links vehicle models to normal card access permissions with specific permission levels
SELECT COUNT(1) AS ModelVehicleNormalCardAccessRecordCount FROM [dbo].[ModelVehicleNormalCardAccess];

-- PerVehicleMasterCardAccess Table - Individual vehicle master card access
-- Contains: Master card access permissions for individual vehicles
-- Relationship: Granular master card access control at the vehicle level
SELECT COUNT(1) AS PerVehicleMasterCardAccessRecordCount FROM [dbo].[PerVehicleMasterCardAccess];

-- PerVehicleNormalCardAccess Table - Individual vehicle normal card access
-- Contains: Normal card access permissions for individual vehicles
-- Relationship: Granular normal card access control at the vehicle level with specific permission levels
SELECT COUNT(1) AS PerVehicleNormalCardAccessRecordCount FROM [dbo].[PerVehicleNormalCardAccess];

-- ============================================================================
-- SUPPORTING ORGANIZATIONAL TABLES (Referenced during seeding)
-- ============================================================================
-- Note: These tables are referenced during seeding but not created by the seeding process

-- Customer Table - Top-level organizational entity
-- Referenced during seeding for organizational hierarchy validation
SELECT COUNT(1) AS CustomerRecordCount FROM [dbo].[Customer];

-- Site Table - Customer locations where vehicles and drivers operate
-- Referenced during seeding for location-based assignments
SELECT COUNT(1) AS SiteRecordCount FROM [dbo].[Site];

-- Department Table - Organizational units within sites
-- Referenced during seeding for departmental assignments
SELECT COUNT(1) AS DepartmentRecordCount FROM [dbo].[Department];

-- Model Table - Vehicle model definitions for proper categorization
-- Referenced during seeding for vehicle model assignments
SELECT COUNT(1) AS ModelRecordCount FROM [dbo].[Model];

-- Canrule Table - Global rules for vehicle CAN bus communication
-- Referenced during seeding for vehicle CAN bus configuration
SELECT COUNT(1) AS CanruleRecordCount FROM [dbo].[Canrule];

-- Permission Table - Permission levels for access control
-- Referenced during seeding for access permission assignments
SELECT COUNT(1) AS PermissionRecordCount FROM [dbo].[Permission];

-- ============================================================================
-- CHECKLIST AND OPERATIONAL SUPPORT TABLES
-- ============================================================================

-- PreOperationalChecklist Table - Pre-operational checklist questions
-- Referenced during seeding for checklist assignments to vehicles
SELECT COUNT(1) AS PreOperationalChecklistRecordCount FROM [dbo].[PreOperationalChecklist];

-- DepartmentChecklist Table - Department-specific checklist configurations
-- Referenced during seeding for department-specific checklist assignments
SELECT COUNT(1) AS DepartmentChecklistRecordCount FROM [dbo].[DepartmentChecklist];

-- ============================================================================
-- SUMMARY QUERIES FOR VALIDATION
-- ============================================================================

-- Summary: Core Entity Relationships
-- Verify that the number of drivers matches the number of person records with IsDriver = true
SELECT 
    'Core Entity Validation' AS ValidationCategory,
    (SELECT COUNT(1) FROM [dbo].[Person] WHERE IsDriver = 1) AS PersonsMarkedAsDrivers,
    (SELECT COUNT(1) FROM [dbo].[Driver]) AS DriverRecords,
    CASE 
        WHEN (SELECT COUNT(1) FROM [dbo].[Person] WHERE IsDriver = 1) = (SELECT COUNT(1) FROM [dbo].[Driver])
        THEN 'PASS' 
        ELSE 'FAIL' 
    END AS ValidationResult;

-- Summary: Vehicle Configuration Completeness
-- Verify that vehicles have corresponding checklist and other settings
SELECT 
    'Vehicle Configuration Validation' AS ValidationCategory,
    (SELECT COUNT(1) FROM [dbo].[Vehicle]) AS VehicleRecords,
    (SELECT COUNT(1) FROM [dbo].[ChecklistSettings]) AS ChecklistSettingsRecords,
    (SELECT COUNT(1) FROM [dbo].[VehicleOtherSettings]) AS VehicleOtherSettingsRecords,
    CASE 
        WHEN (SELECT COUNT(1) FROM [dbo].[Vehicle]) = (SELECT COUNT(1) FROM [dbo].[ChecklistSettings])
             AND (SELECT COUNT(1) FROM [dbo].[Vehicle]) = (SELECT COUNT(1) FROM [dbo].[VehicleOtherSettings])
        THEN 'PASS' 
        ELSE 'FAIL' 
    END AS ValidationResult;

-- Summary: Module Allocation Validation
-- Verify that allocated modules don't exceed total modules
SELECT 
    'Module Allocation Validation' AS ValidationCategory,
    (SELECT COUNT(1) FROM [dbo].[Module]) AS TotalModules,
    (SELECT COUNT(1) FROM [dbo].[Module] WHERE IsAllocatedToVehicle = 1) AS AllocatedModules,
    (SELECT COUNT(1) FROM [dbo].[Vehicle]) AS VehicleRecords,
    CASE 
        WHEN (SELECT COUNT(1) FROM [dbo].[Module] WHERE IsAllocatedToVehicle = 1) <= (SELECT COUNT(1) FROM [dbo].[Module])
             AND (SELECT COUNT(1) FROM [dbo].[Module] WHERE IsAllocatedToVehicle = 1) <= (SELECT COUNT(1) FROM [dbo].[Vehicle])
        THEN 'PASS' 
        ELSE 'FAIL' 
    END AS ValidationResult;

-- ============================================================================
-- END OF VERIFICATION SCRIPT
-- ============================================================================
