namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Enhanced API orchestration service interface that includes complete IoT workflow functionality
/// Extends the base IApiOrchestrationService with IoT device creation and synchronization
/// </summary>
public interface IEnhancedApiOrchestrationService : IApiOrchestrationService
{
    /// <summary>
    /// Complete IoT-enabled workflow that mirrors the main migration system's 3-phase approach:
    /// Phase 1: Create IoT devices/modules via API (like SpareModuleMigration)
    /// Phase 2: Create vehicles linked to IoT devices (existing functionality)
    /// Phase 3: Sync vehicle IoT settings via API (like VehicleSyncMigration)
    /// </summary>
    /// <param name="moduleRequests">IoT device/module creation requests</param>
    /// <param name="vehicleRequests">Vehicle creation requests</param>
    /// <param name="batchSize">Batch size for API operations</param>
    /// <param name="dryRun">Whether to perform a dry run without actual API calls</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Complete workflow result with all phases</returns>
    Task<CompleteIoTWorkflowResult> CreateCompleteIoTWorkflowAsync(
        IEnumerable<IoTDeviceCreateRequest> moduleRequests,
        IEnumerable<VehicleCreateRequest> vehicleRequests,
        int batchSize = 50,
        bool dryRun = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Extended method for bulk operations with full temp table validation and processing
    /// </summary>
    /// <param name="personRequests">Person creation requests</param>
    /// <param name="batchSize">Batch size for processing</param>
    /// <param name="dryRun">Whether to perform a dry run</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Extended result with temp table details</returns>
    Task<TempTableOrchestrationResult> CreatePersonDriverBatchWithFullValidationAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        bool dryRun = false,
        CancellationToken cancellationToken = default);
}
