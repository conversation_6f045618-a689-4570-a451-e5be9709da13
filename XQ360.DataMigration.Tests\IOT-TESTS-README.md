# IoT Device Creation and Vehicle Linking Unit Tests

## Overview

This document describes the comprehensive unit test suite for the IoT device creation and vehicle-to-IoT device linking functionality in the data seeder. The tests ensure that the data seeder maintains the same IoT integration patterns as the main migration system.

## Test Structure

### Test Files

1. **`IoTDeviceCreationServiceTests.cs`** - Unit tests for `IoTDeviceCreationService`
2. **`TempTableApiOrchestrationServiceTests.cs`** - Unit tests for IoT workflow orchestration
3. **`VehicleModuleLinkageTests.cs`** - Unit tests for vehicle-to-module linkage validation
4. **`IoTWorkflowIntegrationTests.cs`** - Integration tests for complete IoT workflow
5. **`IoTTestHelper.cs`** - Helper class with test data creation and assertion utilities

### Test Categories

#### 1. Unit Tests (`IoTDeviceCreationServiceTests.cs`)

**Purpose**: Test the `IoTDeviceCreationService` in isolation with mocked dependencies.

**Coverage**:
- ✅ Constructor validation and dependency injection
- ✅ API connectivity validation (`ValidateIoTApiConnectivityAsync`)
- ✅ IoT device creation via API calls (`CreateIoTDeviceBatchAsync`)
- ✅ Vehicle IoT settings synchronization (`SyncVehicleIoTSettingsAsync`)
- ✅ Error handling for duplicate devices, missing dealers, API failures
- ✅ Rate limiting and retry policies
- ✅ Cancellation token support

**Key Test Methods**:
```csharp
[Fact] ValidateIoTApiConnectivityAsync_WhenAlreadyAuthenticated_ShouldReturnTrue()
[Fact] CreateIoTDeviceBatchAsync_WithValidRequests_ShouldReturnSuccessResult()
[Fact] SyncVehicleIoTSettingsAsync_WithValidDeviceIds_ShouldReturnSuccessResult()
[Fact] CreateIoTDeviceBatchAsync_WithDuplicateDeviceId_ShouldSkipDuplicate()
[Fact] SyncVehicleIoTSettingsAsync_WithPartialFailures_ShouldReturnMixedResult()
```

#### 2. Workflow Orchestration Tests (`TempTableApiOrchestrationServiceTests.cs`)

**Purpose**: Test the complete 3-phase IoT workflow orchestration.

**Coverage**:
- ✅ Complete IoT workflow execution (device creation → vehicle creation → device synchronization)
- ✅ Dry run functionality
- ✅ Batch processing with different batch sizes
- ✅ Error handling when phases fail
- ✅ Progress tracking and result aggregation
- ✅ Cancellation support

**Key Test Methods**:
```csharp
[Fact] CreateCompleteIoTWorkflowAsync_WithValidRequests_ShouldReturnSuccessResult()
[Fact] CreateCompleteIoTWorkflowAsync_WithDryRun_ShouldReturnSuccessWithoutApiCalls()
[Fact] CreateCompleteIoTWorkflowAsync_WhenPhase1Fails_ShouldStopAndReturnFailure()
[Fact] CreateCompleteIoTWorkflowAsync_WithBatchProcessing_ShouldRespectBatchSize()
```

#### 3. Vehicle-Module Linkage Tests (`VehicleModuleLinkageTests.cs`)

**Purpose**: Test vehicle creation with proper ModuleId1 assignment and validation.

**Coverage**:
- ✅ Vehicle creation with proper ModuleId1 assignment
- ✅ Module allocation status updates (IsAllocatedToVehicle flag)
- ✅ Prevention of duplicate module assignments
- ✅ Validation that modules exist before vehicle creation
- ✅ Business rule validation integration

**Key Test Methods**:
```csharp
[Fact] CreateVehicleBatchAsync_WithValidModuleIoTDevice_ShouldLinkVehicleToModule()
[Fact] CreateVehicleBatchAsync_WithNonExistentModule_ShouldReturnFailure()
[Fact] CreateVehicleBatchAsync_WithAlreadyAllocatedModule_ShouldReturnFailure()
[Fact] CreateVehicleBatchAsync_WithMultipleVehiclesSameModule_ShouldPreventDuplicateAssignment()
```

#### 4. Integration Tests (`IoTWorkflowIntegrationTests.cs`)

**Purpose**: Test service interactions and end-to-end workflow execution.

**Coverage**:
- ✅ Service integration with real dependency injection
- ✅ Integration with XQ360ApiClient for API calls
- ✅ End-to-end workflow execution
- ✅ Performance and reliability testing
- ✅ Concurrent request handling
- ✅ Large dataset processing

**Key Test Methods**:
```csharp
[Fact] ServiceProvider_ShouldResolveAllIoTServices()
[Fact] CompleteIoTWorkflow_WithValidData_ShouldExecuteAllPhases()
[Fact] CompleteIoTWorkflow_WithApiFailure_ShouldHandleGracefully()
[Fact] CompleteIoTWorkflow_WithLargeDataSet_ShouldProcessInBatches()
```

## Test Data and Mocking Strategy

### Mocked Dependencies

1. **XQ360ApiClient**: Mocked to simulate API responses without actual HTTP calls
2. **Database Connections**: Mocked to avoid database dependencies in unit tests
3. **ILogger**: Mocked for logging verification
4. **Configuration Services**: Mocked with test configurations

### Test Data Creation

The `IoTTestHelper` class provides standardized test data creation:

```csharp
// Create IoT device requests
var moduleRequests = IoTTestHelper.CreateIoTDeviceRequests(5, "Test Dealer");

// Create vehicle requests with linked IoT devices
var vehicleRequests = IoTTestHelper.CreateVehicleRequests(5, linkToIoTDevices: true);

// Create successful results for assertions
var successResult = IoTTestHelper.CreateSuccessfulIoTDeviceCreationResult(5);
```

### Mock Setup Patterns

```csharp
// Successful API authentication
_mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(true);

// Successful module creation
_mockApiClient.Setup(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()))
    .ReturnsAsync(IoTTestHelper.CreateSuccessfulApiResult(response));

// Successful device synchronization
_mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
    .ReturnsAsync(IoTTestHelper.CreateSuccessfulApiResult(new object()));
```

## Test Execution

### Running Tests

```bash
# Run all IoT-related tests
dotnet test --filter "IoT"

# Run specific test class
dotnet test --filter "IoTDeviceCreationServiceTests"

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Expected Results

- **Total Tests**: ~80 tests across all IoT functionality
- **Expected Pass Rate**: 100%
- **Execution Time**: < 60 seconds for all tests
- **Coverage Target**: > 90% for IoT-related services

## Test Scenarios Covered

### Positive Test Cases

1. **Successful Operations**:
   - Complete 3-phase IoT workflow execution
   - Batch processing with various sizes
   - Vehicle-to-module linkage creation
   - API authentication and connectivity

2. **Data Validation**:
   - Valid IoT device creation requests
   - Proper vehicle-module relationships
   - Correct API endpoint usage

### Negative Test Cases

1. **Error Handling**:
   - API authentication failures
   - Network connectivity issues
   - Invalid or missing data
   - Duplicate device assignments

2. **Edge Cases**:
   - Empty request collections
   - Large dataset processing
   - Concurrent operations
   - Cancellation scenarios

### Performance Test Cases

1. **Scalability**:
   - Large batch processing (100+ items)
   - Concurrent workflow execution
   - Memory usage optimization

2. **Reliability**:
   - Timeout handling
   - Retry mechanism validation
   - Rate limiting compliance

## Consistency Verification

### Main Migration System Alignment

The tests verify that the data seeder maintains consistency with the main migration system:

1. **API Endpoints**: Same endpoints (`module`, `iothubmanager/syncvehiclesettings`)
2. **Authentication**: Same XQ360ApiClient usage patterns
3. **Database Schema**: Same ModuleId1 relationships and allocation flags
4. **Workflow Order**: Same 3-phase execution pattern
5. **Error Handling**: Same retry policies and error reporting

### Test Assertions

```csharp
// Verify API endpoint usage
_mockApiClient.Verify(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()), Times.AtLeastOnce);

// Verify workflow phase execution order
Assert.NotNull(result.ModuleCreationResult); // Phase 1
Assert.True(result.VehicleCreationSuccess);  // Phase 2
Assert.NotNull(result.SyncResult);           // Phase 3

// Verify data consistency
IoTTestHelper.AssertSuccessfulCompleteIoTWorkflow(result, expectedModules, expectedVehicles);
```

## Maintenance and Updates

### Adding New Tests

1. Follow existing naming conventions (`MethodName_Scenario_ExpectedResult`)
2. Use `IoTTestHelper` for test data creation
3. Include both positive and negative test cases
4. Add integration tests for new service interactions

### Mock Updates

When the actual services change:
1. Update mock setups to match new method signatures
2. Add new test cases for new functionality
3. Verify consistency with main migration system patterns
4. Update test documentation

### Performance Benchmarks

Monitor test execution times and update benchmarks:
- Individual test methods: < 100ms
- Test class execution: < 10 seconds
- Full IoT test suite: < 60 seconds

This comprehensive test suite ensures that the IoT device creation and vehicle linking functionality in the data seeder maintains full compatibility with the main migration system while providing enhanced bulk processing capabilities.
