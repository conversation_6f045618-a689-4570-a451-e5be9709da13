using Microsoft.Data.SqlClient;

namespace XQ360.DataMigration.Web.Services.BusinessRules.Rules
{
    /// <summary>
    /// Business rule for validating vehicle module assignments
    /// </summary>
    public class VehicleModuleAssignmentRule : BusinessRuleBase
    {
        private readonly ILogger<VehicleModuleAssignmentRule> _logger;

        public VehicleModuleAssignmentRule(ILogger<VehicleModuleAssignmentRule> logger)
        {
            _logger = logger;
        }

        public override string Id => "VEHICLE_MODULE_ASSIGNMENT";
        public override string Name => "Vehicle Module Assignment Validation";
        public override string Description => "Validates that vehicle module assignments follow business rules including availability, compatibility, and allocation limits";
        public override string Category => "Vehicle Management";
        public override string[] ApplicableEntityTypes => new[] { "VehicleModuleAssignment", "Vehicle", "Module" };

        public override async Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var issues = new List<ValidationIssue>();

            try
            {
                // Extract vehicle and module IDs from the entity
                var vehicleId = GetVehicleId(entity);
                var moduleId = GetModuleId(entity);

                if (vehicleId == Guid.Empty || moduleId == Guid.Empty)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error, 
                        "Invalid vehicle or module ID", 
                        "VehicleId/ModuleId", 
                        $"Vehicle: {vehicleId}, Module: {moduleId}",
                        "Ensure both vehicle and module IDs are valid GUIDs"));
                    return CreateFailureResult(issues.ToArray());
                }

                using var connection = new SqlConnection(context.ConnectionString);
                await connection.OpenAsync(cancellationToken);

                // Rule 1: Check if module exists and is available
                var moduleAvailable = await CheckModuleAvailabilityAsync(moduleId, connection, cancellationToken);
                if (!moduleAvailable.IsAvailable)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Module is not available for assignment: {moduleAvailable.Reason}",
                        "ModuleId",
                        moduleId,
                        "Select a different module or check module status"));
                }

                // Rule 2: Check if vehicle exists and can accept modules
                var vehicleValid = await CheckVehicleValidityAsync(vehicleId, connection, cancellationToken);
                if (!vehicleValid.IsValid)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Vehicle cannot accept module assignment: {vehicleValid.Reason}",
                        "VehicleId",
                        vehicleId,
                        "Verify vehicle exists and is in active status"));
                }

                // Rule 3: Check if module is already assigned to another vehicle
                var existingAssignment = await CheckExistingAssignmentAsync(moduleId, connection, cancellationToken);
                if (existingAssignment.HasAssignment)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Module is already assigned to vehicle {existingAssignment.AssignedVehicleId}",
                        "ModuleId",
                        moduleId,
                        "Unassign module from current vehicle first or select a different module"));
                }

                // Rule 4: Check vehicle module capacity limits
                var capacityCheck = await CheckVehicleModuleCapacityAsync(vehicleId, connection, cancellationToken);
                if (!capacityCheck.HasCapacity)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Vehicle has reached maximum module capacity ({capacityCheck.CurrentCount}/{capacityCheck.MaxCapacity})",
                        "VehicleId",
                        vehicleId,
                        "Consider removing existing modules or verify capacity requirements"));
                }

                // Rule 5: Check module compatibility with vehicle type
                var compatibilityCheck = await CheckModuleVehicleCompatibilityAsync(vehicleId, moduleId, connection, cancellationToken);
                if (!compatibilityCheck.IsCompatible)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Module may not be compatible with vehicle type: {compatibilityCheck.Reason}",
                        "ModuleId",
                        moduleId,
                        "Verify module specifications match vehicle requirements"));
                }

                return issues.Any(i => i.Severity == ValidationSeverity.Error) 
                    ? CreateFailureResult(issues.ToArray()) 
                    : new ValidationResult { IsValid = true, Issues = issues };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating vehicle module assignment for Vehicle: {VehicleId}, Module: {ModuleId}", 
                    GetVehicleId(entity), GetModuleId(entity));
                
                issues.Add(CreateIssue(ValidationSeverity.Error,
                    $"Validation error: {ex.Message}",
                    "System",
                    null,
                    "Contact system administrator"));
                
                return CreateFailureResult(issues.ToArray());
            }
        }

        private Guid GetVehicleId(object entity)
        {
            var vehicleId = GetPropertyValue(entity, "VehicleId");
            return vehicleId is Guid guid ? guid : Guid.Empty;
        }

        private Guid GetModuleId(object entity)
        {
            var moduleId = GetPropertyValue(entity, "ModuleId");
            return moduleId is Guid guid ? guid : Guid.Empty;
        }

        private async Task<(bool IsAvailable, string Reason)> CheckModuleAvailabilityAsync(Guid moduleId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT Status, IsAllocated 
                FROM [dbo].[Module] 
                WHERE Id = @ModuleId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@ModuleId", moduleId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                var status = reader.GetString(reader.GetOrdinal("Status"));
                var isAllocated = reader.GetBoolean(reader.GetOrdinal("IsAllocated"));

                if (status != "Active")
                    return (false, $"Module status is {status}");
                
                if (isAllocated)
                    return (false, "Module is already allocated");

                return (true, "Available");
            }

            return (false, "Module not found");
        }

        private async Task<(bool IsValid, string Reason)> CheckVehicleValidityAsync(Guid vehicleId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT COUNT(*) 
                FROM [dbo].[Vehicle] 
                WHERE Id = @VehicleId AND IsActive = 1";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@VehicleId", vehicleId);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Valid") : (false, "Vehicle not found or inactive");
        }

        private async Task<(bool HasAssignment, Guid? AssignedVehicleId)> CheckExistingAssignmentAsync(Guid moduleId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT VehicleId 
                FROM [dbo].[Vehicle] 
                WHERE ModuleId = @ModuleId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@ModuleId", moduleId);

            var result = await command.ExecuteScalarAsync(cancellationToken);
            if (result != null && result != DBNull.Value)
            {
                return (true, (Guid)result);
            }

            return (false, null);
        }

        private async Task<(bool HasCapacity, int CurrentCount, int MaxCapacity)> CheckVehicleModuleCapacityAsync(Guid vehicleId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT COUNT(*) as CurrentModules
                FROM [dbo].[Module] m
                INNER JOIN [dbo].[Vehicle] v ON m.Id = v.ModuleId
                WHERE v.Id = @VehicleId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@VehicleId", vehicleId);

            var currentCount = (int)await command.ExecuteScalarAsync(cancellationToken);
            const int maxCapacity = 1; // Most vehicles support one primary module

            return (currentCount < maxCapacity, currentCount, maxCapacity);
        }

        private async Task<(bool IsCompatible, string Reason)> CheckModuleVehicleCompatibilityAsync(Guid vehicleId, Guid moduleId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT v.ModelId, m.ModuleType
                FROM [dbo].[Vehicle] v, [dbo].[Module] m
                WHERE v.Id = @VehicleId AND m.Id = @ModuleId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@VehicleId", vehicleId);
            command.Parameters.AddWithValue("@ModuleId", moduleId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                var modelId = reader.IsDBNull(reader.GetOrdinal("ModelId")) ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("ModelId"));
                var moduleType = reader.IsDBNull(reader.GetOrdinal("ModuleType")) ? null : reader.GetString(reader.GetOrdinal("ModuleType"));

                // Basic compatibility check - in a real system this would be more sophisticated
                if (string.IsNullOrEmpty(moduleType))
                    return (false, "Module type not specified");

                // For now, assume all modules are compatible but log a warning for manual verification
                return (true, "Compatibility assumed - manual verification recommended");
            }

            return (false, "Unable to determine compatibility");
        }
    }
}
