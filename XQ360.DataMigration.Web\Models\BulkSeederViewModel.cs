using System.ComponentModel.DataAnnotations;

namespace XQ360.DataMigration.Web.Models;

/// <summary>
/// View model for the bulk seeder wizard
/// </summary>
public class BulkSeederViewModel
{
    /// <summary>
    /// Selected dealer for the seeding operation
    /// </summary>
    public DealerInfo? SelectedDealer { get; set; }

    /// <summary>
    /// Selected customer for the seeding operation
    /// </summary>
    public CustomerInfo? SelectedCustomer { get; set; }

    /// <summary>
    /// Available dealers for selection
    /// </summary>
    public List<DealerInfo> Dealers { get; set; } = new();

    /// <summary>
    /// Available customers for selection
    /// </summary>
    public List<CustomerInfo> Customers { get; set; } = new();

    /// <summary>
    /// Number of vehicles to generate/seed
    /// </summary>
    [Range(1, 1000000, ErrorMessage = "Vehicle count must be between 1 and 1,000,000")]
    public int? VehicleCount { get; set; }

    /// <summary>
    /// Number of drivers to generate/seed
    /// </summary>
    [Range(1, 1000000, ErrorMessage = "Driver count must be between 1 and 1,000,000")]
    public int? DriverCount { get; set; }



    /// <summary>
    /// Current environment being used (from existing system)
    /// </summary>
    public string? CurrentEnvironment { get; set; }

    /// <summary>
    /// Available environments for selection
    /// </summary>
    public List<EnvironmentOption> AvailableEnvironments { get; set; } = new();

    /// <summary>
    /// Selected environment key for the seeding operation
    /// </summary>
    [Required(ErrorMessage = "Please choose an environment")]
    public string? SelectedEnvironment { get; set; }

    /// <summary>
    /// Current session ID if a seeding operation is active
    /// </summary>
    public string? ActiveSessionId { get; set; }

    /// <summary>
    /// Whether a seeding operation is currently in progress
    /// </summary>
    public bool IsImporting { get; set; }


}

/// <summary>
/// Information about a dealer
/// </summary>
public class DealerInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Subdomain { get; set; } = string.Empty;
    public string DisplayName => $"{Name} ({Subdomain})";
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Information about a customer
/// </summary>
public class CustomerInfo
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? ContactName { get; set; }
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string DealerId { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}



/// <summary>
/// Request model for creating a new seeding session
/// </summary>
public class CreateSeederSessionRequest
{
    [Required]
    public string DealerId { get; set; } = string.Empty;

    [Required]
    public string CustomerId { get; set; } = string.Empty;

    [Range(1, 1000000)]
    public int VehicleCount { get; set; }

    [Range(1, 1000000)]
    public int DriverCount { get; set; }

    public bool GenerateData { get; set; } = true;
    public bool DryRun { get; set; } = false;
}



/// <summary>
/// Information about an available environment option
/// </summary>
public class EnvironmentOption
{
    public string Key { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Information about a seeding session
/// </summary>
public class SeederSessionInfo
{
    public Guid Id { get; set; }
    public string? DealerId { get; set; }
    public string? CustomerName { get; set; }
    public int DriversCount { get; set; }
    public int VehiclesCount { get; set; }
    public string Environment { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public int TotalRows { get; set; }
    public int ProcessedRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public List<string> Errors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Response for dealer list requests
/// </summary>
public class DealerListResponse
{
    /// <summary>
    /// List of dealers
    /// </summary>
    public List<DealerInfo> Dealers { get; set; } = new();

    /// <summary>
    /// Total number of dealers matching the criteria
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size used for the request
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }
}

/// <summary>
/// Response for customer list requests
/// </summary>
public class CustomerListResponse
{
    /// <summary>
    /// List of customers
    /// </summary>
    public List<CustomerInfo> Customers { get; set; } = new();

    /// <summary>
    /// Total number of customers matching the criteria
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Page size used for the request
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Dealer ID these customers belong to
    /// </summary>
    public string DealerId { get; set; } = string.Empty;
}
