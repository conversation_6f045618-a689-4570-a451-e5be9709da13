# DataSeeder Testing Strategy

This document outlines the testing strategy for the DataSeeder components to ensure reliable, maintainable, and fast-running tests.

## Test Categories

### Unit Tests (`TestCategories.Unit`)
Unit tests are designed to test individual components in isolation. They:

- **Use mocked dependencies** for all external services (database, APIs, etc.)
- **Run fast** (typically under 1 second each)
- **Require no external infrastructure** (database, web services, file system)
- **Test single units of functionality** with precise control over inputs and outputs

**Files:**
- `SqlDataGenerationServiceTests.cs` - Tests SqlDataGenerationService with mocked database connections
- `BulkSeederServiceTests.cs` - Tests BulkSeederService with mocked SQL data service
- `MigrationPatternSeederServiceTests.cs` - Tests MigrationPatternSeederService with mocked dependencies

### Integration Tests (`TestCategories.Integration`)
Integration tests verify that components work together correctly. They:

- **Use real service implementations** but may mock external dependencies
- **Test service interactions** and dependency injection
- **May take longer to run** than unit tests
- **Focus on cross-component communication** and data flow

**Files:**
- `DataSeederIntegrationTests.cs` - Tests service integration with real DI container

### Performance Tests (`TestCategories.Performance`)
Performance tests measure performance characteristics and behavior under load. They:

- **Measure execution time, memory usage, and throughput**
- **Test with realistic data volumes**
- **Use mocked dependencies** to isolate performance to the service under test
- **May take significantly longer to run**

**Files:**
- `DataSeederPerformanceTests.cs` - Performance and load testing

## Mock Infrastructure

### Database Mocking
The `MockDatabaseConnection` class and related mocks provide:
- **IDbConnection implementation** that doesn't require actual database
- **Predictable behavior** for connection, command, and reader operations
- **Support for transactions** and parameter handling
- **Mock data readers** that return test data

### Key Mock Classes
- `MockDatabaseConnection` - Simulates database connections
- `MockDatabaseCommand` - Simulates database commands
- `MockDataReader` - Simulates data readers with test data
- `MockDatabaseTransaction` - Simulates database transactions

## Database Connection Abstraction

### IDatabaseConnectionFactory
A factory interface that abstracts database connection creation:
```csharp
public interface IDatabaseConnectionFactory
{
    IDbConnection CreateConnection();
    IDbConnection CreateConnection(string connectionString);
}
```

### Production Implementation
- `DatabaseConnectionFactory` - Creates real SQL Server connections using SqlConnection

### Test Implementation
- Mocked via `Mock<IDatabaseConnectionFactory>` to return `MockDatabaseConnection`

## Running Tests

### Run All Tests
```bash
dotnet test
```

### Run Only Unit Tests
```bash
dotnet test --filter Category=Unit
```

### Run Only Integration Tests
```bash
dotnet test --filter Category=Integration
```

### Run Only Performance Tests
```bash
dotnet test --filter Category=Performance
```

## Test Configuration

### TestConfigurationHelper
Provides standardized test configuration:
- **Mock environment configurations**
- **Test options objects**
- **Consistent test data**

### Mock Setup Patterns
- All unit tests use mocked dependencies
- Database connections always use `MockDatabaseConnection`
- API services use `Mock<IApiOrchestrationService>`
- SignalR hubs use `Mock<IHubContext<MigrationHub>>`

## Benefits of This Approach

1. **Fast Unit Tests** - No database I/O means tests run in milliseconds
2. **Reliable Tests** - No external dependencies means no flaky tests
3. **Isolated Testing** - Each component can be tested in isolation
4. **Comprehensive Coverage** - Can test error conditions and edge cases easily
5. **CI/CD Friendly** - Tests run quickly in build pipelines without infrastructure

## Troubleshooting

### Common Issues

**Test fails with "Execution Timeout Expired"**
- Indicates a test is trying to connect to a real database
- Check that all database dependencies are properly mocked
- Verify `IDatabaseConnectionFactory` is mocked in test setup

**Test fails with SQL connection errors**
- Same as above - database abstraction is not properly mocked
- Check service constructor dependencies

**Mock setup errors**
- Ensure all required mock setups are in place in test constructors
- Use `SetupSuccessfulDataGeneration()` helper methods where available
