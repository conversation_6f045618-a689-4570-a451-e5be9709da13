using Microsoft.Extensions.Options;
using Polly;
using System.Diagnostics;
using System.Threading.RateLimiting;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Models;
using Newtonsoft.Json;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// API orchestration service with rate limiting, batching, and error recovery
/// Implementation of Phase 2.1: API Integration Enhancement
/// </summary>
public class ApiOrchestrationService : IApiOrchestrationService
{
    private readonly ILogger<ApiOrchestrationService> _logger;
    private readonly XQ360ApiClient _apiClient;
    private readonly BulkSeederConfiguration _config;
    private readonly RateLimiter _rateLimiter;

    public ApiOrchestrationService(
        ILogger<ApiOrchestrationService> logger,
        XQ360ApiClient apiClient,
        IOptions<BulkSeederConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));

        // Initialize rate limiter: Maximum 50 API calls per second
        _rateLimiter = new TokenBucketRateLimiter(new TokenBucketRateLimiterOptions
        {
            TokenLimit = 50,
            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
            QueueLimit = 1000,
            ReplenishmentPeriod = TimeSpan.FromSeconds(1),
            TokensPerPeriod = 50,
            AutoReplenishment = true
        });

        // Circuit breaker will be implemented using Polly retry policies instead
    }

    public async Task<ApiOrchestrationResult> CreatePersonDriverBatchAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var requests = personRequests.ToList();
        var result = new ApiOrchestrationResult
        {
            TotalRequests = requests.Count
        };

        _logger.LogInformation("Starting Person/Driver batch creation: {TotalRequests} requests", result.TotalRequests);

        try
        {
            // Ensure API authentication
            if (!await ValidateApiConnectivityAsync())
            {
                result.Errors.Add("API authentication failed");
                return result;
            }

            // Process requests in batches
            var batches = requests.Chunk(batchSize);

            foreach (var batch in batches)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessPersonBatchAsync(batch, result, cancellationToken);
            }

            result.Success = result.FailedRequests == 0;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Person/Driver batch creation completed: {Successful}/{Total} successful in {Duration}ms",
                result.SuccessfulRequests, result.TotalRequests, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Person/Driver batch creation failed");
            result.Errors.Add($"Batch operation failed: {ex.Message}");
            result.Duration = stopwatch.Elapsed;
            return result;
        }
    }

    private async Task ProcessPersonBatchAsync(
        PersonCreateRequest[] batch,
        ApiOrchestrationResult result,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Processing Person batch: {BatchSize} requests", batch.Length);

        var tasks = batch.Select(async request =>
        {
            var key = $"{request.FirstName}_{request.LastName}_{request.SiteId}";

            try
            {
                // Apply rate limiting
                using var lease = await _rateLimiter.AcquireAsync(1, cancellationToken);
                if (!lease.IsAcquired)
                {
                    result.Results[key] = new PersonApiResult
                    {
                        Success = false,
                        ErrorMessage = "Rate limit exceeded"
                    };
                    lock (result) { result.FailedRequests++; }
                    return;
                }

                // Execute API call with retry policy
                var apiResult = await CreateSinglePersonAsync(request, cancellationToken);

                result.Results[key] = apiResult;

                if (apiResult.Success)
                {
                    lock (result) { result.SuccessfulRequests++; }
                }
                else
                {
                    lock (result) { result.FailedRequests++; }
                    result.Errors.Add($"Person creation failed for {request.FirstName} {request.LastName}: {apiResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Person creation failed for {FirstName} {LastName}",
                    request.FirstName, request.LastName);

                result.Results[key] = new PersonApiResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };

                lock (result) { result.FailedRequests++; }
                result.Errors.Add($"Person creation exception for {request.FirstName} {request.LastName}: {ex.Message}");
            }
        });

        await Task.WhenAll(tasks);
    }

    private async Task<PersonApiResult> CreateSinglePersonAsync(
        PersonCreateRequest request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Convert request to API entity format (following PersonMigration pattern)
            var personEntity = new PersonApiEntity
            {
                IsNew = true,
                FirstName = request.FirstName,
                LastName = request.LastName,
                SiteId = request.SiteId,
                DepartmentId = request.DepartmentId,
                CustomerId = request.CustomerId,
                SendDenyMessage = true, // Default value - not exposed in data seeder request
                WebsiteAccess = request.WebsiteAccess,
                IsDriver = request.IsDriver,
                Supervisor = request.IsSupervisor,
                VORActivateDeactivate = request.VORActivateDeactivate,
                NormalDriverAccess = request.NormalDriverAccess,
                CanUnlockVehicle = request.CanUnlockVehicle,
                // Required fields
                Active = true,
                Language = "en-US",
                IsActiveDriver = request.IsDriver,
                VehicleAccess = true,
                OnDemand = false,
                MaintenanceMode = false
            };

            // Create form data (following PersonMigration pattern)
            var formData = new Dictionary<string, string>
            {
                ["entity"] = JsonConvert.SerializeObject(personEntity),
                ["include"] = "Site,Department"
            };

            // Execute API call with retry policy
            var retryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Retrying Person API call for {FirstName} {LastName}. Attempt {RetryCount}",
                            request.FirstName, request.LastName, retryCount);
                    });

            var apiResult = await retryPolicy.ExecuteAsync(async () =>
            {
                return await _apiClient.PostFormAsync<PersonApiResponse>("person", formData);
            });

            if (apiResult.Success && apiResult.Data != null)
            {
                // Parse response to extract Person and Driver IDs
                var personId = Guid.TryParse(apiResult.Data.PrimaryKey, out var parsedPersonId) ? parsedPersonId : (Guid?)null;

                return new PersonApiResult
                {
                    Success = true,
                    PersonId = personId,
                    DriverId = request.IsDriver ? personId : null // Driver ID equals Person ID when IsDriver=true
                };
            }
            else
            {
                return new PersonApiResult
                {
                    Success = false,
                    ErrorMessage = apiResult.ErrorMessage ?? "Unknown API error"
                };
            }
        }
        catch (Exception ex)
        {
            return new PersonApiResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> ValidateApiConnectivityAsync()
    {
        try
        {
            _logger.LogDebug("Validating API connectivity...");

            // Test authentication if not already authenticated
            var isAuthenticated = await _apiClient.TestAuthenticationAsync();
            if (!isAuthenticated)
            {
                _logger.LogInformation("API not authenticated, attempting authentication...");
                isAuthenticated = await _apiClient.AuthenticateAsync();
            }

            if (isAuthenticated)
            {
                _logger.LogDebug("API connectivity validated successfully");
                return true;
            }
            else
            {
                _logger.LogWarning("API connectivity validation failed");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API connectivity validation failed with exception");
            return false;
        }
    }

    public async Task<bool> RefreshAuthenticationAsync()
    {
        try
        {
            _logger.LogInformation("Refreshing API authentication...");

            // Refresh CSRF token first
            var csrfRefreshed = await _apiClient.RefreshCsrfTokenAsync();
            if (!csrfRefreshed)
            {
                _logger.LogWarning("CSRF token refresh failed");
                return false;
            }

            // Re-authenticate to get fresh tokens
            var authenticated = await _apiClient.AuthenticateAsync();
            if (authenticated)
            {
                _logger.LogInformation("API authentication refreshed successfully");
                return true;
            }
            else
            {
                _logger.LogWarning("API authentication refresh failed");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API authentication refresh failed with exception");
            return false;
        }
    }

    public void Dispose()
    {
        _rateLimiter?.Dispose();
    }
}

// API entity models (copied from PersonMigration for consistency)
public class PersonApiEntity
{
    public bool IsNew { get; set; }
    public required string FirstName { get; set; }
    public required string LastName { get; set; }
    public Guid SiteId { get; set; }
    public Guid DepartmentId { get; set; }
    public Guid CustomerId { get; set; }
    public bool SendDenyMessage { get; set; }
    public bool WebsiteAccess { get; set; }
    public bool IsDriver { get; set; }
    public bool? IsActiveDriver { get; set; }
    public bool Supervisor { get; set; }
    public bool VORActivateDeactivate { get; set; }
    public bool NormalDriverAccess { get; set; }
    public bool CanUnlockVehicle { get; set; }
    public bool VehicleAccess { get; set; }
    public bool OnDemand { get; set; }
    public bool MaintenanceMode { get; set; }
    public bool Active { get; set; }
    public required string Language { get; set; }
}

public class PersonApiResponse
{
    public int InternalObjectId { get; set; }
    public required string PrimaryKey { get; set; }
    public object ObjectsDataSet { get; set; } = new object();
}
