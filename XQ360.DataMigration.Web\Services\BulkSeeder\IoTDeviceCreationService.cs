using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Polly;
using System.Diagnostics;
using System.Threading.RateLimiting;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// IoT device creation service that mirrors SpareModuleMigration and VehicleSyncMigration functionality
/// Implements the same API patterns and workflow as the main migration system
/// </summary>
public class IoTDeviceCreationService : IIoTDeviceCreationService
{
    private readonly ILogger<IoTDeviceCreationService> _logger;
    private readonly XQ360ApiClient _apiClient;
    private readonly BulkSeederConfiguration _config;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly RateLimiter _rateLimiter;

    // Cache for database lookups (mirrors SpareModuleMigration pattern)
    private readonly Dictionary<string, Guid> _dealerCache = new Dictionary<string, Guid>();

    public IoTDeviceCreationService(
        ILogger<IoTDeviceCreationService> logger,
        XQ360ApiClient apiClient,
        IOptions<BulkSeederConfiguration> config,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));

        // Initialize rate limiter for API calls
        _rateLimiter = new TokenBucketRateLimiter(new TokenBucketRateLimiterOptions
        {
            TokenLimit = 30, // Conservative limit for module creation
            QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
            QueueLimit = 500,
            ReplenishmentPeriod = TimeSpan.FromSeconds(1),
            TokensPerPeriod = 30,
            AutoReplenishment = true
        });
    }

    public async Task<IoTDeviceCreationResult> CreateIoTDeviceBatchAsync(
        IEnumerable<IoTDeviceCreateRequest> moduleRequests,
        int batchSize = 50,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var requests = moduleRequests.ToList();
        var result = new IoTDeviceCreationResult
        {
            TotalRequests = requests.Count
        };

        _logger.LogInformation("Starting IoT device batch creation: {TotalRequests} requests", result.TotalRequests);

        try
        {
            // Step 1: Ensure API authentication (mirrors SpareModuleMigration pattern)
            if (!await ValidateIoTApiConnectivityAsync())
            {
                result.Errors.Add("API authentication failed");
                return result;
            }

            // Step 2: Add authentication delay (mirrors SpareModuleMigration pattern)
            await Task.Delay(1000, cancellationToken);
            _logger.LogInformation("Starting IoT device API calls after authentication delay...");

            // Step 3: Process requests in batches
            var batches = requests.Chunk(batchSize);

            foreach (var batch in batches)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessIoTDeviceBatchAsync(batch, result, cancellationToken);
            }

            result.Success = result.FailedRequests == 0;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("IoT device batch creation completed: {Successful}/{Total} successful in {Duration}ms",
                result.SuccessfulRequests, result.TotalRequests, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "IoT device batch creation failed");
            result.Errors.Add($"Batch operation failed: {ex.Message}");
            result.Duration = stopwatch.Elapsed;
            return result;
        }
    }

    private async Task ProcessIoTDeviceBatchAsync(
        IoTDeviceCreateRequest[] batch,
        IoTDeviceCreationResult result,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Processing IoT device batch: {BatchSize} requests", batch.Length);

        var tasks = batch.Select(async request =>
        {
            var key = $"{request.IoTDeviceID}_{request.Dealer}";

            try
            {
                // Apply rate limiting
                using var lease = await _rateLimiter.AcquireAsync(1, cancellationToken);
                if (!lease.IsAcquired)
                {
                    result.Results[key] = new IoTDeviceApiResult
                    {
                        Success = false,
                        IoTDeviceID = request.IoTDeviceID,
                        ErrorMessage = "Rate limit exceeded"
                    };
                    lock (result) { result.FailedRequests++; }
                    return;
                }

                // Execute API call with retry policy
                var apiResult = await CreateSingleIoTDeviceAsync(request, cancellationToken);

                result.Results[key] = apiResult;

                if (apiResult.Success)
                {
                    lock (result) { result.SuccessfulRequests++; }
                }
                else
                {
                    lock (result) { result.FailedRequests++; }
                    result.Errors.Add($"IoT device creation failed for {request.IoTDeviceID}: {apiResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "IoT device creation failed for {IoTDeviceID}", request.IoTDeviceID);

                result.Results[key] = new IoTDeviceApiResult
                {
                    Success = false,
                    IoTDeviceID = request.IoTDeviceID,
                    ErrorMessage = ex.Message
                };

                lock (result) { result.FailedRequests++; }
                result.Errors.Add($"IoT device creation exception for {request.IoTDeviceID}: {ex.Message}");
            }
        });

        await Task.WhenAll(tasks);
    }

    private async Task<IoTDeviceApiResult> CreateSingleIoTDeviceAsync(
        IoTDeviceCreateRequest request,
        CancellationToken cancellationToken)
    {
        try
        {
            // Step 1: Validate request data (mirrors SpareModuleMigration pattern)
            if (string.IsNullOrEmpty(request.IoTDeviceID))
            {
                return new IoTDeviceApiResult
                {
                    Success = false,
                    IoTDeviceID = request.IoTDeviceID,
                    ErrorMessage = "IoT Device ID is required"
                };
            }

            // Step 2: Check if module already exists (mirrors SpareModuleMigration pattern)
            var existingModuleId = await CheckIfModuleExistsAsync(request.IoTDeviceID);
            if (existingModuleId != null)
            {
                _logger.LogWarning("Module with IoTDevice '{IoTDeviceID}' already exists with ID: {ModuleId}. Skipping creation.",
                    request.IoTDeviceID, existingModuleId);

                return new IoTDeviceApiResult
                {
                    Success = true, // Consider existing as success
                    ModuleId = existingModuleId,
                    IoTDeviceID = request.IoTDeviceID,
                    ErrorMessage = "Module already exists"
                };
            }

            // Step 3: Get dealer ID (mirrors SpareModuleMigration pattern)
            var dealerId = await GetDealerIdAsync(request.Dealer);
            if (dealerId == null)
            {
                return new IoTDeviceApiResult
                {
                    Success = false,
                    IoTDeviceID = request.IoTDeviceID,
                    ErrorMessage = $"Dealer '{request.Dealer}' not found"
                };
            }

            // Step 4: Create module entity (mirrors SpareModuleMigration pattern exactly)
            var moduleEntity = new IoTDeviceApiEntity
            {
                IsNew = true,
                IoTDevice = request.IoTDeviceID.Trim(),
                DealerId = dealerId.Value,
                CCID = request.CCID.ToString().Trim(),
                RANumber = request.RANumber.ToString().Trim(),
                TechNumber = request.TechNumber.ToString().Trim(),
                IsSimCardExpired = false,
                Status = 0, // ModuleStatusEnum.Spare
                FSSXMulti = 1.0, // CRITICAL: Must be 1, not 0!
                IsAllocatedToVehicle = false, // FALSE for spare modules
                ModuleType = 0, // ModuleTypeEnum.MK3
                Calibration = 0,
                FSSSBase = 0.0,
                AmberImpact = 0.0,
                BlueImpact = 0.0,
                RedImpact = 0.0
            };

            // Step 5: Create form data (mirrors SpareModuleMigration pattern)
            var formData = new Dictionary<string, string>
            {
                ["entity"] = JsonConvert.SerializeObject(moduleEntity),
                ["include"] = "Dealer"
            };

            // Step 6: Execute API call with retry policy
            var retryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Retrying IoT device API call for {IoTDeviceID}. Attempt {RetryCount}",
                            request.IoTDeviceID, retryCount);
                    });

            var apiResult = await retryPolicy.ExecuteAsync(async () =>
            {
                return await _apiClient.PostFormAsync<IoTDeviceApiResponse>("module", formData);
            });

            if (apiResult.Success && apiResult.Data != null)
            {
                var moduleId = Guid.TryParse(apiResult.Data.PrimaryKey, out var parsedModuleId) ? parsedModuleId : (Guid?)null;

                return new IoTDeviceApiResult
                {
                    Success = true,
                    ModuleId = moduleId,
                    IoTDeviceID = request.IoTDeviceID
                };
            }
            else
            {
                return new IoTDeviceApiResult
                {
                    Success = false,
                    IoTDeviceID = request.IoTDeviceID,
                    ErrorMessage = apiResult.ErrorMessage ?? "Unknown API error"
                };
            }
        }
        catch (Exception ex)
        {
            return new IoTDeviceApiResult
            {
                Success = false,
                IoTDeviceID = request.IoTDeviceID,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> ValidateIoTApiConnectivityAsync()
    {
        try
        {
            _logger.LogDebug("Validating IoT API connectivity...");

            // Test authentication if not already authenticated
            var isAuthenticated = await _apiClient.TestAuthenticationAsync();
            if (!isAuthenticated)
            {
                _logger.LogInformation("API not authenticated, attempting authentication...");
                isAuthenticated = await _apiClient.AuthenticateAsync();
            }

            if (isAuthenticated)
            {
                _logger.LogDebug("IoT API connectivity validated successfully");
                return true;
            }
            else
            {
                _logger.LogWarning("IoT API connectivity validation failed");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "IoT API connectivity validation failed with exception");
            return false;
        }
    }

    public async Task<IoTDeviceSyncResult> SyncVehicleIoTSettingsAsync(
        IEnumerable<string> deviceIds,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var deviceIdList = deviceIds.ToList();
        var result = new IoTDeviceSyncResult
        {
            TotalDevices = deviceIdList.Count
        };

        _logger.LogInformation("Starting vehicle IoT settings sync: {TotalDevices} devices", result.TotalDevices);

        try
        {
            // Step 1: Validate API connectivity
            if (!await ValidateIoTApiConnectivityAsync())
            {
                result.Errors.Add("API authentication failed for sync operation");
                return result;
            }

            // Step 2: Process each device (mirrors VehicleSyncMigration pattern)
            for (int i = 0; i < deviceIdList.Count; i++)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var deviceId = deviceIdList[i];

                try
                {
                    _logger.LogInformation("Syncing vehicle settings for Device ID: {DeviceId} ({Current}/{Total})",
                        deviceId, i + 1, deviceIdList.Count);

                    // Prepare form data for the API call (mirrors VehicleSyncMigration pattern)
                    var formData = new Dictionary<string, string>
                    {
                        { "DeviceId", deviceId }
                    };

                    // Call the sync vehicle settings API (mirrors VehicleSyncMigration pattern)
                    var apiResult = await _apiClient.PostFormAsync<object>("iothubmanager/syncvehiclesettings", formData);

                    if (apiResult.Success)
                    {
                        result.SuccessfulSyncs++;
                        result.SyncedDeviceIds.Add(deviceId);
                        _logger.LogInformation("✅ Successfully synced settings for Device ID: {DeviceId}", deviceId);
                    }
                    else
                    {
                        result.FailedSyncs++;
                        result.FailedDeviceIds.Add(deviceId);
                        var errorMessage = $"Failed to sync settings for Device ID '{deviceId}': {apiResult.ErrorMessage ?? "Unknown error"}";
                        _logger.LogError(errorMessage);
                        result.Errors.Add(errorMessage);
                    }
                }
                catch (Exception ex)
                {
                    result.FailedSyncs++;
                    result.FailedDeviceIds.Add(deviceId);
                    var errorMessage = $"Exception while syncing Device ID '{deviceId}': {ex.Message}";
                    _logger.LogError(ex, errorMessage);
                    result.Errors.Add(errorMessage);
                }

                // Add delay between API calls to avoid overwhelming the server (mirrors VehicleSyncMigration pattern)
                if (i < deviceIdList.Count - 1)
                {
                    await Task.Delay(500, cancellationToken);
                }
            }

            result.Success = result.FailedSyncs == 0;
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Vehicle IoT settings sync completed: {Successful}/{Total} successful in {Duration}ms",
                result.SuccessfulSyncs, result.TotalDevices, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vehicle IoT settings sync failed");
            result.Errors.Add($"Sync operation failed: {ex.Message}");
            result.Duration = stopwatch.Elapsed;
            return result;
        }
    }

    // Helper methods that mirror SpareModuleMigration patterns
    private async Task<Guid?> CheckIfModuleExistsAsync(string iotDeviceId)
    {
        if (string.IsNullOrEmpty(iotDeviceId))
            return null;

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            var sql = "SELECT Id FROM dbo.Module WHERE IoTDevice = @IoTDevice";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@IoTDevice", iotDeviceId);

            var result = await cmd.ExecuteScalarAsync();
            if (result != null)
            {
                var moduleId = (Guid)result;
                return moduleId;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if module exists for IoTDevice '{IoTDeviceId}'", iotDeviceId);
            return null;
        }
    }

    private async Task<Guid?> GetDealerIdAsync(string dealerName)
    {
        if (string.IsNullOrEmpty(dealerName))
            return null;

        if (_dealerCache.ContainsKey(dealerName))
        {
            return _dealerCache[dealerName];
        }

        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result != null)
            {
                var dealerId = (Guid)result;
                _dealerCache[dealerName] = dealerId;
                return dealerId;
            }
            else
            {
                _logger.LogWarning("Dealer '{DealerName}' not found in database", dealerName);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to lookup dealer '{DealerName}'", dealerName);
            return null;
        }
    }

    public void Dispose()
    {
        _rateLimiter?.Dispose();
    }
}
