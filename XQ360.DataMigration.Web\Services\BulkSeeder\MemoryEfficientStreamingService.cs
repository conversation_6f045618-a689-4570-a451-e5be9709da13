using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using System.Data;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Memory-efficient data streaming service for Phase 3.1.3: Memory-Efficient Data Streaming
/// Implements IAsyncEnumerable and yield return patterns to avoid loading large datasets into memory
/// </summary>
public class MemoryEfficientStreamingService : IMemoryEfficientStreamingService
{
    private readonly ILogger<MemoryEfficientStreamingService> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly BulkSeederConfiguration _config;

    public MemoryEfficientStreamingService(
        ILogger<MemoryEfficientStreamingService> logger,
        IEnvironmentConfigurationService environmentService,
        IOptions<BulkSeederConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
    }

    public async IAsyncEnumerable<T> StreamDataFromDatabaseAsync<T>(
        string query,
        Func<SqlDataReader, T> mapper,
        StreamingOptions options,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting streaming query: {Query}", query);

        var recordCount = 0;
        var stopwatch = Stopwatch.StartNew();

        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken).ConfigureAwait(false);

        using var command = new SqlCommand(query, connection)
        {
            CommandTimeout = options.CommandTimeoutSeconds,
            CommandType = CommandType.Text
        };

        // Apply streaming optimizations
        ApplyStreamingOptimizations(command, options);

        using var reader = await command.ExecuteReaderAsync(CommandBehavior.SequentialAccess, cancellationToken)
            .ConfigureAwait(false);

        try
        {
            while (await reader.ReadAsync(cancellationToken).ConfigureAwait(false))
            {
                T item;

                try
                {
                    item = mapper(reader);
                    recordCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error mapping record {RecordCount} from streaming query", recordCount);
                    if (options.StopOnMappingError)
                        throw;
                    continue;
                }

                yield return item;

                // Memory management for large streams
                if (options.EnableMemoryManagement && recordCount % options.GcInterval == 0)
                {
                    await OptimizeMemoryUsageAsync(options).ConfigureAwait(false);
                }

                // Progress reporting
                if (options.ProgressCallback != null && recordCount % options.ProgressReportInterval == 0)
                {
                    await options.ProgressCallback(recordCount, stopwatch.Elapsed).ConfigureAwait(false);
                }

                // Yield control periodically to prevent blocking
                if (recordCount % options.YieldInterval == 0)
                {
                    await Task.Yield();
                }
            }
        }
        finally
        {
            _logger.LogInformation("Streaming query completed: {RecordCount} records in {Duration}ms",
                recordCount, stopwatch.ElapsedMilliseconds);
        }
    }

    public async IAsyncEnumerable<TOutput> StreamTransformDataAsync<TInput, TOutput>(
        IAsyncEnumerable<TInput> inputStream,
        Func<TInput, CancellationToken, Task<TOutput>> transformer,
        StreamingTransformOptions options,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting streaming transformation with buffer size: {BufferSize}",
            options.BufferSize);

        var processedCount = 0;
        var buffer = new List<TInput>(options.BufferSize);
        var stopwatch = Stopwatch.StartNew();

        try
        {
            await foreach (var item in inputStream.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
                buffer.Add(item);

                // Process buffer when full or when forced
                if (buffer.Count >= options.BufferSize || options.ProcessImmediately)
                {
                    await foreach (var transformed in ProcessBufferStreamAsync(
                        buffer, transformer, options, cancellationToken).ConfigureAwait(false))
                    {
                        yield return transformed;
                        processedCount++;

                        // Memory management
                        if (options.EnableMemoryManagement && processedCount % options.GcInterval == 0)
                        {
                            await OptimizeMemoryUsageAsync(options).ConfigureAwait(false);
                        }
                    }

                    buffer.Clear();

                    // Yield control after processing buffer
                    await Task.Yield();
                }
            }

            // Process remaining items in buffer
            if (buffer.Count > 0)
            {
                await foreach (var transformed in ProcessBufferStreamAsync(
                    buffer, transformer, options, cancellationToken).ConfigureAwait(false))
                {
                    yield return transformed;
                    processedCount++;
                }
            }
        }
        finally
        {
            _logger.LogInformation("Streaming transformation completed: {ProcessedCount} items in {Duration}ms",
                processedCount, stopwatch.ElapsedMilliseconds);
        }
    }

    public async IAsyncEnumerable<T> StreamBatchProcessingAsync<T>(
        IAsyncEnumerable<T> inputStream,
        Func<IEnumerable<T>, CancellationToken, Task<IEnumerable<T>>> batchProcessor,
        int batchSize,
        StreamingBatchOptions options,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting streaming batch processing with batch size: {BatchSize}", batchSize);

        var batch = new List<T>(batchSize);
        var processedBatches = 0;
        var totalItems = 0;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            await foreach (var item in inputStream.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
                batch.Add(item);

                if (batch.Count >= batchSize)
                {
                    var processedBatch = await batchProcessor(batch, cancellationToken).ConfigureAwait(false);

                    foreach (var processedItem in processedBatch)
                    {
                        yield return processedItem;
                        totalItems++;
                    }

                    processedBatches++;
                    batch.Clear();

                    // Memory management after each batch
                    if (options.EnableMemoryManagement && processedBatches % options.GcInterval == 0)
                    {
                        await OptimizeMemoryUsageAsync(options).ConfigureAwait(false);
                    }

                    // Progress reporting
                    if (options.ProgressCallback != null)
                    {
                        await options.ProgressCallback(processedBatches, totalItems, stopwatch.Elapsed)
                            .ConfigureAwait(false);
                    }

                    // Yield control after each batch
                    await Task.Yield();
                }
            }

            // Process remaining items
            if (batch.Count > 0)
            {
                var finalBatch = await batchProcessor(batch, cancellationToken).ConfigureAwait(false);

                foreach (var processedItem in finalBatch)
                {
                    yield return processedItem;
                    totalItems++;
                }

                processedBatches++;
            }
        }
        finally
        {
            _logger.LogInformation("Streaming batch processing completed: {ProcessedBatches} batches, {TotalItems} items in {Duration}ms",
                processedBatches, totalItems, stopwatch.ElapsedMilliseconds);
        }
    }

    public async IAsyncEnumerable<T> StreamWithMemoryPressureMonitoringAsync<T>(
        IAsyncEnumerable<T> inputStream,
        MemoryPressureOptions options,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting streaming with memory pressure monitoring. Threshold: {ThresholdMB}MB",
            options.MemoryThresholdMB);

        var itemCount = 0;
        var lastGcTime = DateTime.UtcNow;

        await foreach (var item in inputStream.WithCancellation(cancellationToken).ConfigureAwait(false))
        {
            // Check memory pressure before yielding item
            var currentMemoryMB = GC.GetTotalMemory(false) / (1024 * 1024);

            if (currentMemoryMB > options.MemoryThresholdMB)
            {
                _logger.LogWarning("Memory pressure detected: {CurrentMemoryMB}MB > {ThresholdMB}MB, triggering GC",
                    currentMemoryMB, options.MemoryThresholdMB);

                await ForceMemoryOptimizationAsync().ConfigureAwait(false);
                lastGcTime = DateTime.UtcNow;

                // Check if memory is still too high after GC
                var memoryAfterGc = GC.GetTotalMemory(false) / (1024 * 1024);
                if (memoryAfterGc > options.MemoryThresholdMB * 0.8) // 80% of threshold
                {
                    _logger.LogWarning("Memory still high after GC: {MemoryAfterGcMB}MB", memoryAfterGc);

                    if (options.PauseOnHighMemory)
                    {
                        _logger.LogInformation("Pausing stream due to memory pressure for {DelayMs}ms",
                            options.MemoryPauseDelayMs);
                        await Task.Delay(options.MemoryPauseDelayMs, cancellationToken).ConfigureAwait(false);
                    }
                }
            }

            yield return item;
            itemCount++;

            // Periodic memory optimization
            if (DateTime.UtcNow - lastGcTime > TimeSpan.FromMilliseconds(options.PeriodicGcIntervalMs))
            {
                await OptimizeMemoryUsageAsync(options).ConfigureAwait(false);
                lastGcTime = DateTime.UtcNow;
            }

            // Yield control periodically
            if (itemCount % options.YieldInterval == 0)
            {
                await Task.Yield();
            }
        }

        _logger.LogInformation("Memory-monitored streaming completed: {ItemCount} items processed", itemCount);
    }

    public async Task<StreamingMetrics> GetStreamingMetricsAsync()
    {
        var process = Process.GetCurrentProcess();

        return new StreamingMetrics
        {
            CurrentMemoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024),
            WorkingSetMB = process.WorkingSet64 / (1024 * 1024),
            Gen0Collections = GC.CollectionCount(0),
            Gen1Collections = GC.CollectionCount(1),
            Gen2Collections = GC.CollectionCount(2),
            AvailableMemoryMB = GetAvailableMemoryMB(),
            ThreadCount = process.Threads.Count,
            Timestamp = DateTime.UtcNow
        };
    }

    private async IAsyncEnumerable<TOutput> ProcessBufferStreamAsync<TInput, TOutput>(
        IEnumerable<TInput> buffer,
        Func<TInput, CancellationToken, Task<TOutput>> transformer,
        StreamingTransformOptions options,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var bufferList = buffer.ToList();

        if (options.ProcessInParallel && bufferList.Count > 1)
        {
            // Process buffer items in parallel
            var tasks = bufferList.Select(item => transformer(item, cancellationToken));
            var results = await Task.WhenAll(tasks).ConfigureAwait(false);

            foreach (var result in results)
            {
                yield return result;
            }
        }
        else
        {
            // Process buffer items sequentially
            foreach (var item in bufferList)
            {
                var result = await transformer(item, cancellationToken).ConfigureAwait(false);
                yield return result;
            }
        }
    }

    private void ApplyStreamingOptimizations(SqlCommand command, StreamingOptions options)
    {
        // Add query hints for streaming optimization
        if (options.UseStreamingHints)
        {
            var originalQuery = command.CommandText;

            // Add OPTION clause with streaming hints
            if (!originalQuery.Contains("OPTION", StringComparison.OrdinalIgnoreCase))
            {
                command.CommandText = $"{originalQuery} OPTION (FAST {options.FastRowsHint})";
            }
        }

        // Set optimal command properties for streaming
        command.CommandTimeout = options.CommandTimeoutSeconds;
    }

    private async Task OptimizeMemoryUsageAsync(IMemoryOptimizable options)
    {
        if (!options.EnableMemoryManagement) return;

        try
        {
            await Task.Run(() =>
            {
                GC.Collect(1, GCCollectionMode.Optimized);
                GC.WaitForPendingFinalizers();
            }).ConfigureAwait(false);

            // Small delay to allow GC to complete
            await Task.Delay(1).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Memory optimization failed during streaming");
        }
    }

    private async Task ForceMemoryOptimizationAsync()
    {
        try
        {
            await Task.Run(() =>
            {
                GC.Collect(2, GCCollectionMode.Forced);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Forced);
            }).ConfigureAwait(false);

            // Longer delay for forced GC
            await Task.Delay(10).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Forced memory optimization failed");
        }
    }

    private long GetAvailableMemoryMB()
    {
        try
        {
            var gcMemoryInfo = GC.GetGCMemoryInfo();
            return gcMemoryInfo.TotalAvailableMemoryBytes / (1024 * 1024);
        }
        catch
        {
            // Fallback if GC memory info is not available
            return 0;
        }
    }
}

 
