using System.Collections;
using System.Data;
using System.Data.Common;

namespace XQ360.DataMigration.DataSeeder.Tests.Mocks
{
    /// <summary>
    /// Mock database connection for unit testing
    /// Inherits from DbConnection to support async operations
    /// </summary>
    public class MockDatabaseConnection : DbConnection
    {
        private ConnectionState _state = ConnectionState.Closed;
        
        public override string ConnectionString { get; set; } = "mock://test-connection";
        public override int ConnectionTimeout => 30;
        public override string Database => "TestDatabase";
        public override ConnectionState State => _state;
        public override string ServerVersion => "Mock Server 1.0";
        public override string DataSource => "MockDataSource";

        protected override DbTransaction BeginDbTransaction(IsolationLevel isolationLevel)
        {
            return new MockDbTransaction();
        }

        public override void ChangeDatabase(string databaseName)
        {
            // Mock implementation - no-op
        }

        public override void Close()
        {
            _state = ConnectionState.Closed;
        }

        protected override DbCommand CreateDbCommand()
        {
            return new MockDbCommand();
        }

        public override void Open()
        {
            _state = ConnectionState.Open;
        }

        public override async Task OpenAsync(CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            _state = ConnectionState.Open;
        }
    }

    /// <summary>
    /// Mock database transaction for unit testing
    /// </summary>
    public class MockDbTransaction : DbTransaction
    {
        public override IsolationLevel IsolationLevel => IsolationLevel.ReadCommitted;
        protected override DbConnection? DbConnection => null;

        public override void Commit()
        {
            // Mock implementation - no-op
        }

        public override async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            await Task.CompletedTask;
        }

        public override void Rollback()
        {
            // Mock implementation - no-op
        }

        public override async Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// Mock database command for unit testing
    /// </summary>
    public class MockDbCommand : DbCommand
    {
        public override string CommandText { get; set; } = string.Empty;
        public override int CommandTimeout { get; set; } = 30;
        public override CommandType CommandType { get; set; } = CommandType.Text;
        protected override DbConnection? DbConnection { get; set; }
        protected override DbParameterCollection DbParameterCollection { get; } = new MockDbParameterCollection();
        protected override DbTransaction? DbTransaction { get; set; }
        public override UpdateRowSource UpdatedRowSource { get; set; } = UpdateRowSource.None;
        public override bool DesignTimeVisible { get; set; } = false;

        public override void Cancel()
        {
            // Mock implementation - no-op
        }

        protected override DbParameter CreateDbParameter()
        {
            return new MockDbParameter();
        }

        protected override DbDataReader ExecuteDbDataReader(CommandBehavior behavior)
        {
            return new MockDbDataReader();
        }

        public override int ExecuteNonQuery()
        {
            // Mock successful execution
            return 1;
        }

        public override async Task<int> ExecuteNonQueryAsync(CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            return 1;
        }

        protected override async Task<DbDataReader> ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            return new MockDbDataReader();
        }

        public override object? ExecuteScalar()
        {
            // Mock successful scalar execution
            return 0;
        }

        public override async Task<object?> ExecuteScalarAsync(CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            return 0;
        }

        public override void Prepare()
        {
            // Mock implementation - no-op
        }
    }

    /// <summary>
    /// Mock data parameter for unit testing
    /// </summary>
    public class MockDbParameter : DbParameter
    {
        public override DbType DbType { get; set; } = DbType.String;
        public override ParameterDirection Direction { get; set; } = ParameterDirection.Input;
        public override bool IsNullable { get; set; } = true;
        public override string ParameterName { get; set; } = string.Empty;
        public override byte Precision { get; set; } = 0;
        public override byte Scale { get; set; } = 0;
        public override int Size { get; set; } = 0;
        public override string SourceColumn { get; set; } = string.Empty;
        public override DataRowVersion SourceVersion { get; set; } = DataRowVersion.Current;
        public override object? Value { get; set; }
        public override bool SourceColumnNullMapping { get; set; } = false;

        public override void ResetDbType()
        {
            DbType = DbType.String;
        }
    }

    /// <summary>
    /// Mock parameter collection for unit testing
    /// </summary>
    public class MockDbParameterCollection : DbParameterCollection
    {
        private readonly List<DbParameter> _parameters = new();

        public override int Count => _parameters.Count;
        public override object SyncRoot => this;
        public override bool IsFixedSize => false;
        public override bool IsReadOnly => false;
        public override bool IsSynchronized => false;

        public override int Add(object value)
        {
            if (value is DbParameter parameter)
            {
                _parameters.Add(parameter);
                return _parameters.Count - 1;
            }
            throw new ArgumentException("Value must be a DbParameter");
        }

        public override void AddRange(Array values)
        {
            foreach (var value in values)
            {
                Add(value);
            }
        }

        public override void Clear()
        {
            _parameters.Clear();
        }

        public override bool Contains(object value)
        {
            return value is DbParameter parameter && _parameters.Contains(parameter);
        }

        public override bool Contains(string value)
        {
            return _parameters.Any(p => p.ParameterName == value);
        }

        public override void CopyTo(Array array, int index)
        {
            ((ICollection)_parameters).CopyTo(array, index);
        }

        public override IEnumerator GetEnumerator()
        {
            return ((IEnumerable)_parameters).GetEnumerator();
        }

        protected override DbParameter GetParameter(int index)
        {
            return _parameters[index];
        }

        protected override DbParameter GetParameter(string parameterName)
        {
            return _parameters.First(p => p.ParameterName == parameterName);
        }

        public override int IndexOf(object value)
        {
            return value is DbParameter parameter ? _parameters.IndexOf(parameter) : -1;
        }

        public override int IndexOf(string parameterName)
        {
            return _parameters.FindIndex(p => p.ParameterName == parameterName);
        }

        public override void Insert(int index, object value)
        {
            if (value is DbParameter parameter)
            {
                _parameters.Insert(index, parameter);
            }
            else
            {
                throw new ArgumentException("Value must be a DbParameter");
            }
        }

        public override void Remove(object value)
        {
            if (value is DbParameter parameter)
            {
                _parameters.Remove(parameter);
            }
        }

        public override void RemoveAt(int index)
        {
            _parameters.RemoveAt(index);
        }

        public override void RemoveAt(string parameterName)
        {
            var index = IndexOf(parameterName);
            if (index >= 0)
            {
                RemoveAt(index);
            }
        }

        protected override void SetParameter(int index, DbParameter value)
        {
            _parameters[index] = value;
        }

        protected override void SetParameter(string parameterName, DbParameter value)
        {
            var index = IndexOf(parameterName);
            if (index >= 0)
            {
                _parameters[index] = value;
            }
            else
            {
                Add(value);
            }
        }
    }

    /// <summary>
    /// Mock data reader for unit testing
    /// </summary>
    public class MockDbDataReader : DbDataReader
    {
        private bool _hasRead = false;
        private bool _isClosed = false;

        public override object this[int i] => GetValue(i);
        public override object this[string name] => GetValue(GetOrdinal(name));

        public override int Depth => 0;
        public override bool IsClosed => _isClosed;
        public override int RecordsAffected => 0;
        public override int FieldCount => 4; // Mock field count
        public override bool HasRows => true;

        public override void Close()
        {
            _isClosed = true;
        }

        public override bool GetBoolean(int i) => false;
        public override byte GetByte(int i) => 0;
        public override long GetBytes(int i, long fieldOffset, byte[]? buffer, int bufferoffset, int length) => 0;
        public override char GetChar(int i) => '\0';
        public override long GetChars(int i, long fieldoffset, char[]? buffer, int bufferoffset, int length) => 0;
        public override string GetDataTypeName(int i) => "varchar";
        public override DateTime GetDateTime(int i) => DateTime.MinValue;
        public override decimal GetDecimal(int i) => 0;
        public override double GetDouble(int i) => 0;
        public override Type GetFieldType(int i) => typeof(string);
        public override float GetFloat(int i) => 0;
        public override Guid GetGuid(int i) => Guid.Empty;
        public override short GetInt16(int i) => 0;
        public override int GetInt32(int i) => 0;
        public int GetInt32(string name) => 0; // Additional overload for named access
        public override long GetInt64(int i) => 0;
        public override string GetName(int i) => $"Column{i}";
        public override int GetOrdinal(string name) => name switch
        {
            "DriverCount" => 0,
            "VehicleCount" => 1,
            "InvalidDrivers" => 2,
            "InvalidVehicles" => 3,
            _ => 0
        };

        public override DataTable? GetSchemaTable() => null;
        public override string GetString(int i) => string.Empty;
        public override object GetValue(int i) => i switch
        {
            0 => 0, // DriverCount
            1 => 0, // VehicleCount  
            2 => 0, // InvalidDrivers
            3 => 0, // InvalidVehicles
            _ => string.Empty
        };

        public override int GetValues(object[] values)
        {
            for (int i = 0; i < Math.Min(values.Length, FieldCount); i++)
            {
                values[i] = GetValue(i);
            }
            return Math.Min(values.Length, FieldCount);
        }

        public override bool IsDBNull(int i) => false;

        public override bool NextResult() => false;

        public override bool Read()
        {
            if (!_hasRead)
            {
                _hasRead = true;
                return true; // Return one row of mock data
            }
            return false;
        }

        public override async Task<bool> ReadAsync(CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            return Read();
        }

        public override IEnumerator GetEnumerator()
        {
            return ((IEnumerable)new object[0]).GetEnumerator();
        }
    }
}
