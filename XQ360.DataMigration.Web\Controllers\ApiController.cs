using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Hubs;
using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;

namespace XQ360.DataMigration.Web.Controllers;

/// <summary>
/// General API controller for supporting the seeder UI
/// Provides endpoints for dealers, customers, and validation
/// </summary>
[ApiController]
[Route("api")]
[Produces("application/json")]
public class ApiController : ControllerBase
{
    private readonly ILogger<ApiController> _logger;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly IBulkSeederService _bulkSeederService;
    private readonly IMigrationPatternSeederService _migrationPatternSeederService;
    private readonly IHubContext<MigrationHub> _hubContext;

    private static readonly ConcurrentDictionary<Guid, SeederSessionInfo> _activeSessions = new();
    private static readonly ConcurrentDictionary<Guid, CancellationTokenSource> _sessionCancellationTokens = new();

    public ApiController(
        ILogger<ApiController> logger,
        IEnvironmentConfigurationService environmentService,
        IBulkSeederService bulkSeederService,
        IMigrationPatternSeederService migrationPatternSeederService,
        IHubContext<MigrationHub> hubContext)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _bulkSeederService = bulkSeederService ?? throw new ArgumentNullException(nameof(bulkSeederService));
        _migrationPatternSeederService = migrationPatternSeederService ?? throw new ArgumentNullException(nameof(migrationPatternSeederService));
        _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
    }

    /// <summary>
    /// Get all active dealers
    /// </summary>
    /// <param name="query">Optional search query to filter dealers</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <param name="activeOnly">Whether to return only active dealers (default: true)</param>
    /// <returns>List of dealers</returns>
    [HttpGet("dealers")]
    [ProducesResponseType(typeof(DealerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<DealerListResponse>> GetDealers(
        [FromQuery] string? query = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // Validate parameters
            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting dealers with query: {Query}, page: {PageNumber}, size: {PageSize}, activeOnly: {ActiveOnly}",
                query, pageNumber, pageSize, activeOnly);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            // Build the SQL query with optional filtering
            var whereConditions = new List<string>();

            if (activeOnly)
            {
                whereConditions.Add("Active = @Active");
            }

            if (!string.IsNullOrWhiteSpace(query))
            {
                whereConditions.Add("(Name LIKE @Query OR PortalURL LIKE @Query)");
            }

            var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            // Get total count
            var countSql = $"SELECT COUNT(*) FROM dbo.Dealer {whereClause}";
            using var countCommand = new SqlCommand(countSql, connection);

            // Add parameters for count command
            if (activeOnly)
            {
                countCommand.Parameters.Add(new SqlParameter("@Active", true));
            }
            if (!string.IsNullOrWhiteSpace(query))
            {
                countCommand.Parameters.Add(new SqlParameter("@Query", $"%{query.Trim()}%"));
            }

            var totalCount = (int)await countCommand.ExecuteScalarAsync();

            // Get paginated results
            var sql = $@"
                SELECT Id, Name, PortalURL, Active
                FROM dbo.Dealer
                {whereClause}
                ORDER BY Name
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";

            using var command = new SqlCommand(sql, connection);

            // Add parameters for main command (create new instances)
            if (activeOnly)
            {
                command.Parameters.Add(new SqlParameter("@Active", true));
            }
            if (!string.IsNullOrWhiteSpace(query))
            {
                command.Parameters.Add(new SqlParameter("@Query", $"%{query.Trim()}%"));
            }
            command.Parameters.Add(new SqlParameter("@Offset", (pageNumber - 1) * pageSize));
            command.Parameters.Add(new SqlParameter("@PageSize", pageSize));

            var dealers = new List<DealerInfo>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                dealers.Add(new DealerInfo
                {
                    Id = reader.GetGuid(reader.GetOrdinal("Id")).ToString(),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Subdomain = reader.IsDBNull(reader.GetOrdinal("PortalURL")) ? "" : reader.GetString(reader.GetOrdinal("PortalURL")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("Active"))
                });
            }

            var response = new DealerListResponse
            {
                Dealers = dealers,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogDebug("Retrieved {Count} dealers out of {TotalCount} total", dealers.Count, totalCount);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dealers");
            return Problem(
                title: "Database Error",
                detail: "An error occurred while retrieving dealers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get customers for a specific dealer
    /// </summary>
    /// <param name="dealerId">Dealer ID to get customers for</param>
    /// <param name="query">Optional search query to filter customers</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <param name="activeOnly">Whether to return only active customers (default: true)</param>
    /// <returns>List of customers for the dealer</returns>
    [HttpGet("customers")]
    [ProducesResponseType(typeof(CustomerListResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<CustomerListResponse>> GetCustomers(
        [FromQuery] string dealerId,
        [FromQuery] string? query = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        [FromQuery] bool activeOnly = true)
    {
        try
        {
            // Validate parameters
            if (string.IsNullOrEmpty(dealerId) || !Guid.TryParse(dealerId, out var dealerGuid))
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid dealer ID",
                    Detail = "Dealer ID must be a valid GUID",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageNumber < 1)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page number",
                    Detail = "Page number must be greater than 0",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            if (pageSize < 1 || pageSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid page size",
                    Detail = "Page size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Getting customers for dealer: {DealerId}, query: {Query}, page: {PageNumber}, size: {PageSize}, activeOnly: {ActiveOnly}",
                dealerId, query, pageNumber, pageSize, activeOnly);

            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            // Build the SQL query with filtering
            var whereConditions = new List<string> { "DealerId = @DealerId" };

            if (activeOnly)
            {
                whereConditions.Add("Active = @Active");
            }

            if (!string.IsNullOrWhiteSpace(query))
            {
                whereConditions.Add("(CompanyName LIKE @Query OR ContactNumber LIKE @Query OR Email LIKE @Query)");
            }

            var whereClause = "WHERE " + string.Join(" AND ", whereConditions);

            // Get total count
            var countSql = $"SELECT COUNT(*) FROM dbo.Customer {whereClause}";
            using var countCommand = new SqlCommand(countSql, connection);

            // Add parameters for count command
            countCommand.Parameters.Add(new SqlParameter("@DealerId", dealerGuid));
            if (activeOnly)
            {
                countCommand.Parameters.Add(new SqlParameter("@Active", true));
            }
            if (!string.IsNullOrWhiteSpace(query))
            {
                countCommand.Parameters.Add(new SqlParameter("@Query", $"%{query.Trim()}%"));
            }

            var totalCount = (int)await countCommand.ExecuteScalarAsync();

            // Get paginated results
            var sql = $@"
                SELECT Id, CompanyName, ContactNumber, Email, Active
                FROM dbo.Customer
                {whereClause}
                ORDER BY CompanyName
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";

            using var command = new SqlCommand(sql, connection);

            // Add parameters for main command (create new instances)
            command.Parameters.Add(new SqlParameter("@DealerId", dealerGuid));
            if (activeOnly)
            {
                command.Parameters.Add(new SqlParameter("@Active", true));
            }
            if (!string.IsNullOrWhiteSpace(query))
            {
                command.Parameters.Add(new SqlParameter("@Query", $"%{query.Trim()}%"));
            }
            command.Parameters.Add(new SqlParameter("@Offset", (pageNumber - 1) * pageSize));
            command.Parameters.Add(new SqlParameter("@PageSize", pageSize));

            var customers = new List<CustomerInfo>();
            using var reader = await command.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                customers.Add(new CustomerInfo
                {
                    Id = reader.GetGuid(reader.GetOrdinal("Id")).ToString(),
                    Name = reader.GetString(reader.GetOrdinal("CompanyName")),
                    ContactName = reader.IsDBNull(reader.GetOrdinal("ContactNumber")) ? null : reader.GetString(reader.GetOrdinal("ContactNumber")),
                    ContactEmail = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email")),
                    DealerId = dealerId,
                    IsActive = reader.GetBoolean(reader.GetOrdinal("Active"))
                });
            }

            var response = new CustomerListResponse
            {
                Customers = customers,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                DealerId = dealerId
            };

            _logger.LogDebug("Retrieved {Count} customers out of {TotalCount} total for dealer {DealerId}", customers.Count, totalCount, dealerId);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customers for dealer: {DealerId}", dealerId);
            return Problem(
                title: "Database Error",
                detail: "An error occurred while retrieving customers",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Validate seeder configuration before starting
    /// </summary>
    /// <param name="request">Validation request</param>
    /// <returns>Validation result</returns>
    [HttpPost("bulk-seeder/validate")]
    public async Task<ActionResult<ValidationResult>> ValidateSeederConfiguration([FromBody] ValidateSeederRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var errors = new List<string>();

            // Validate dealer
            if (string.IsNullOrEmpty(request.DealerId))
            {
                errors.Add("Dealer selection is required");
            }

            // Validate customer
            if (string.IsNullOrEmpty(request.CustomerId))
            {
                errors.Add("Customer selection is required");
            }

            // Validate counts
            if (request.VehicleCount <= 0)
            {
                errors.Add("Vehicle count must be greater than 0");
            }

            if (request.DriverCount <= 0)
            {
                errors.Add("Driver count must be greater than 0");
            }

            // Check for reasonable limits
            if (request.VehicleCount > 100000)
            {
                errors.Add("Vehicle count exceeds maximum limit of 100,000");
            }

            if (request.DriverCount > 200000)
            {
                errors.Add("Driver count exceeds maximum limit of 200,000");
            }

            // Perform database connectivity validation
            await ValidateDatabaseConnectivity(errors);

            // Validate dealer and customer existence in database
            if (!string.IsNullOrEmpty(request.DealerId) && !string.IsNullOrEmpty(request.CustomerId))
            {
                await ValidateDealerAndCustomerExistence(request.DealerId, request.CustomerId, errors);
            }

            // Validate API connectivity if using migration patterns
            await ValidateApiConnectivity();

            var result = new ValidationResult
            {
                Success = errors.Count == 0,
                ValidationErrors = errors,
                Summary = errors.Count == 0 ? "Configuration is valid and ready for seeding" : $"Found {errors.Count} validation error(s)"
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating seeder configuration");
            return Problem(
                title: "Validation Error",
                detail: "An error occurred while validating the configuration",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Start a seeding operation
    /// </summary>
    /// <param name="request">Seeding start request</param>
    /// <returns>Session information</returns>
    [HttpPost("bulk-seeder/start")]
    public async Task<ActionResult<object>> StartSeeding([FromBody] CreateSeederSessionRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Validate the request data (basic validation)
            var validationErrors = new List<string>();
            ValidateSeederRequest(request, validationErrors);

            // Perform comprehensive validation (database connectivity, dealer/customer existence, API connectivity)
            await ValidateDatabaseConnectivity(validationErrors);

            if (!string.IsNullOrEmpty(request.DealerId) && !string.IsNullOrEmpty(request.CustomerId))
            {
                await ValidateDealerAndCustomerExistence(request.DealerId, request.CustomerId, validationErrors);
            }

            await ValidateApiConnectivity();

            if (validationErrors.Count > 0)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Validation Failed",
                    Detail = string.Join("; ", validationErrors),
                    Status = StatusCodes.Status400BadRequest
                });
            }

            var sessionId = Guid.NewGuid();
            var cancellationTokenSource = new CancellationTokenSource();

            // Create session information
            var sessionInfo = new SeederSessionInfo
            {
                Id = sessionId,
                DealerId = request.DealerId,
                CustomerName = request.CustomerId, // Using CustomerId as CustomerName for compatibility
                DriversCount = request.DriverCount,
                VehiclesCount = request.VehicleCount,
                Environment = _environmentService.CurrentEnvironmentKey,
                Status = "Started",
                CreatedAt = DateTime.UtcNow,
                StartedAt = DateTime.UtcNow,
                CreatedBy = GetCurrentUser()
            };

            // Store the session and cancellation token
            _activeSessions[sessionId] = sessionInfo;
            _sessionCancellationTokens[sessionId] = cancellationTokenSource;

            _logger.LogInformation("Starting seeding session {SessionId} for dealer {DealerId}, customer {CustomerId}, drivers: {DriversCount}, vehicles: {VehiclesCount}",
                sessionId, request.DealerId, request.CustomerId, request.DriverCount, request.VehicleCount);

            // Prepare seeding options
            var options = new SeederOptions
            {
                DriversCount = request.DriverCount,
                VehiclesCount = request.VehicleCount,
                DealerId = request.DealerId,
                CustomerName = request.CustomerId,
                DryRun = request.DryRun,
                GenerateData = request.GenerateData,
                Interactive = false
            };

            // Start the seeding operation in the background
            _ = Task.Run(async () => await ExecuteSeederSessionAsync(sessionId, options, cancellationTokenSource.Token));

            return Ok(new
            {
                sessionId = sessionId,
                status = "Started",
                message = "Seeding operation started successfully. Use the session ID to track progress."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting seeding operation");
            return Problem(
                title: "Start Error",
                detail: "An error occurred while starting the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Cancel a seeding operation
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Cancellation result</returns>
    [HttpPost("bulk-seeder/cancel/{sessionId}")]
    public async Task<ActionResult<object>> CancelSeeding(Guid sessionId)
    {
        try
        {
            if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Session Not Found",
                    Detail = $"Seeding session {sessionId} was not found",
                    Status = StatusCodes.Status404NotFound
                });
            }

            if (sessionInfo.Status == "Completed" || sessionInfo.Status == "Failed" || sessionInfo.Status == "Cancelled")
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Cannot Cancel Session",
                    Detail = $"Session {sessionId} is in state '{sessionInfo.Status}' and cannot be cancelled",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Cancel the operation
            if (_sessionCancellationTokens.TryGetValue(sessionId, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                _logger.LogInformation("Cancellation requested for seeding session {SessionId}", sessionId);
            }

            // Update session status
            sessionInfo.Status = "Cancelled";
            sessionInfo.CompletedAt = DateTime.UtcNow;
            sessionInfo.Summary = "Session cancelled by user request";

            // Send cancellation notification via SignalR
            await _hubContext.Clients.Group(sessionId.ToString())
                .SendAsync("SeederSessionCancelled", sessionInfo);

            _logger.LogInformation("Seeding session {SessionId} cancelled successfully", sessionId);

            return Ok(new
            {
                sessionId = sessionId,
                status = "Cancelled",
                message = "Seeding operation cancelled successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling seeding operation: {SessionId}", sessionId);
            return Problem(
                title: "Cancellation Error",
                detail: "An error occurred while cancelling the seeding operation",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get session status for tracking progress
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <returns>Session status information</returns>
    [HttpGet("bulk-seeder/status/{sessionId}")]
    public ActionResult<object> GetSeederStatus(Guid sessionId)
    {
        try
        {
            if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
            {
                return NotFound(new ProblemDetails
                {
                    Title = "Session Not Found",
                    Detail = $"Seeding session {sessionId} was not found",
                    Status = StatusCodes.Status404NotFound
                });
            }

            return Ok(new
            {
                sessionId = sessionInfo.Id,
                status = sessionInfo.Status,
                createdAt = sessionInfo.CreatedAt,
                startedAt = sessionInfo.StartedAt,
                completedAt = sessionInfo.CompletedAt,
                duration = sessionInfo.Duration,
                processedRows = sessionInfo.ProcessedRows,
                successfulRows = sessionInfo.SuccessfulRows,
                failedRows = sessionInfo.FailedRows,
                errors = sessionInfo.Errors,
                summary = sessionInfo.Summary
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving seeding session status: {SessionId}", sessionId);
            return Problem(
                title: "Status Error",
                detail: "An error occurred while retrieving the session status",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private async Task ValidateDatabaseConnectivity(List<string> errors)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            // Test basic connectivity with a simple query
            using var command = new SqlCommand("SELECT 1", connection);
            await command.ExecuteScalarAsync();

            _logger.LogDebug("Database connectivity validation passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connectivity validation failed");
            errors.Add("Database connectivity failed: " + ex.Message);
        }
    }

    private async Task ValidateDealerAndCustomerExistence(string dealerId, string customerId, List<string> errors)
    {
        try
        {
            using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
            await connection.OpenAsync();

            // Validate dealer exists and is active
            if (Guid.TryParse(dealerId, out var dealerGuid))
            {
                using var dealerCommand = new SqlCommand("SELECT COUNT(*) FROM dbo.Dealer WHERE Id = @DealerId AND Active = 1", connection);
                dealerCommand.Parameters.Add(new SqlParameter("@DealerId", dealerGuid));
                var dealerCount = (int)await dealerCommand.ExecuteScalarAsync();

                if (dealerCount == 0)
                {
                    errors.Add("Selected dealer does not exist or is not active");
                }
            }
            else
            {
                errors.Add("Invalid dealer ID format");
            }

            // Validate customer exists and is active
            if (Guid.TryParse(customerId, out var customerGuid))
            {
                using var customerCommand = new SqlCommand("SELECT COUNT(*) FROM dbo.Customer WHERE Id = @CustomerId AND Active = 1", connection);
                customerCommand.Parameters.Add(new SqlParameter("@CustomerId", customerGuid));
                var customerCount = (int)await customerCommand.ExecuteScalarAsync();

                if (customerCount == 0)
                {
                    errors.Add("Selected customer does not exist or is not active");
                }
            }
            else
            {
                errors.Add("Invalid customer ID format");
            }

            _logger.LogDebug("Dealer and customer existence validation completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dealer and customer validation failed");
            errors.Add("Failed to validate dealer and customer existence: " + ex.Message);
        }
    }

    private async Task ValidateApiConnectivity()
    {
        try
        {
            var validationResult = await _migrationPatternSeederService.ValidateMigrationPatternPrerequisitesAsync();

            if (!validationResult.IsValid)
            {
                // Log API connectivity issues but don't fail the seeding operation
                // API features are optional for basic data seeding
                _logger.LogWarning("API connectivity validation failed: {Errors}",
                    string.Join(", ", validationResult.Errors));

                // Only add warnings, not errors, so seeding can continue
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogWarning("API validation issue: {Error}", error);
                }

                // Add warnings to help user understand API features won't be available
                if (validationResult.Errors.Any(e => e.Contains("API connectivity")))
                {
                    _logger.LogInformation("Seeding will continue without API-dependent features");
                }
            }
            else
            {
                _logger.LogDebug("API connectivity validation passed");
            }

            _logger.LogDebug("API connectivity validation completed");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "API connectivity validation failed - continuing without API features");
            // Don't add to errors as API features are optional
        }
    }

    private void ValidateSeederRequest(CreateSeederSessionRequest request, List<string> errors)
    {
        if (string.IsNullOrEmpty(request.DealerId))
            errors.Add("Dealer ID is required");

        if (string.IsNullOrEmpty(request.CustomerId))
            errors.Add("Customer ID is required");

        if (request.DriverCount <= 0)
            errors.Add("Driver count must be greater than 0");

        if (request.VehicleCount <= 0)
            errors.Add("Vehicle count must be greater than 0");

        if (request.DriverCount > 200000)
            errors.Add("Driver count exceeds maximum limit of 200,000");

        if (request.VehicleCount > 100000)
            errors.Add("Vehicle count exceeds maximum limit of 100,000");
    }

    private async Task ExecuteSeederSessionAsync(Guid sessionId, SeederOptions options, CancellationToken cancellationToken)
    {
        try
        {
            if (!_activeSessions.TryGetValue(sessionId, out var sessionInfo))
            {
                _logger.LogWarning("Session {SessionId} not found during execution", sessionId);
                return;
            }

            sessionInfo.Status = "Running";

            // Send progress notification via SignalR
            await _hubContext.Clients.Group(sessionId.ToString())
                .SendAsync("UpdateSeederProgress", new
                {
                    SessionId = sessionId.ToString(),
                    Status = "Running",
                    Progress = 0,
                    Message = "Starting seeding operation...",
                    Timestamp = DateTime.UtcNow
                }, cancellationToken);

            // Execute the seeding operation with the session ID for SignalR messaging
            var result = await _bulkSeederService.ExecuteSeederAsync(options, sessionId.ToString(), cancellationToken);

            // Update session with results
            sessionInfo.Status = result.Success ? "Completed" : "Failed";
            sessionInfo.CompletedAt = DateTime.UtcNow;
            sessionInfo.Duration = result.Duration;
            sessionInfo.ProcessedRows = result.ProcessedRows;
            sessionInfo.SuccessfulRows = result.SuccessfulRows;
            sessionInfo.FailedRows = result.FailedRows;
            sessionInfo.Errors = result.Errors;
            sessionInfo.Summary = result.Summary;

            // Clean up cancellation token
            if (_sessionCancellationTokens.TryRemove(sessionId, out var tokenSource))
            {
                tokenSource.Dispose();
            }

            // Send completion notification
            await _hubContext.Clients.Group(sessionId.ToString())
                .SendAsync("SeederSessionCompleted", sessionInfo, CancellationToken.None);

            _logger.LogInformation("Seeding session {SessionId} completed with status {Status}", sessionId, sessionInfo.Status);
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation("Seeding session {SessionId} was cancelled", sessionId);

            if (_activeSessions.TryGetValue(sessionId, out var sessionInfo))
            {
                sessionInfo.Status = "Cancelled";
                sessionInfo.CompletedAt = DateTime.UtcNow;
                sessionInfo.Summary = "Operation was cancelled";

                await _hubContext.Clients.Group(sessionId.ToString())
                    .SendAsync("SeederSessionCancelled", sessionInfo, CancellationToken.None);
            }

            // Clean up cancellation token
            if (_sessionCancellationTokens.TryRemove(sessionId, out var tokenSource))
            {
                tokenSource.Dispose();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Seeding session {SessionId} failed with exception", sessionId);

            if (_activeSessions.TryGetValue(sessionId, out var sessionInfo))
            {
                sessionInfo.Status = "Failed";
                sessionInfo.CompletedAt = DateTime.UtcNow;
                sessionInfo.Errors = new List<string> { ex.Message };
                sessionInfo.Summary = $"Seeding failed: {ex.Message}";

                await _hubContext.Clients.Group(sessionId.ToString())
                    .SendAsync("SeederSessionFailed", sessionInfo, CancellationToken.None);
            }

            // Clean up cancellation token
            if (_sessionCancellationTokens.TryRemove(sessionId, out var tokenSource))
            {
                tokenSource.Dispose();
            }
        }
    }

    private string GetCurrentUser()
    {
        return $"{Environment.UserName}@{Environment.MachineName}";
    }
}

/// <summary>
/// Request model for validating seeder configuration
/// </summary>
public class ValidateSeederRequest
{
    public string? DealerId { get; set; }
    public string? CustomerId { get; set; }
    public int VehicleCount { get; set; }
    public int DriverCount { get; set; }
}


