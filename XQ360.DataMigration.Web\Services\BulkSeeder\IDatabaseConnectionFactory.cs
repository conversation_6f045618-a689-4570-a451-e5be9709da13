using System.Data;
using System.Data.Common;

namespace XQ360.DataMigration.Web.Services.BulkSeeder
{
    /// <summary>
    /// Abstraction for creating database connections to enable testability
    /// Uses DbConnection which supports async operations
    /// </summary>
    public interface IDatabaseConnectionFactory
    {
        /// <summary>
        /// Creates a new database connection using the current environment configuration
        /// </summary>
        /// <returns>A database connection that implements DbConnection for async support</returns>
        DbConnection CreateConnection();

        /// <summary>
        /// Creates a new database connection using the specified connection string
        /// </summary>
        /// <param name="connectionString">The connection string to use</param>
        /// <returns>A database connection that implements DbConnection for async support</returns>
        DbConnection CreateConnection(string connectionString);
    }
}
