using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;
using Newtonsoft.Json;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Enhanced API orchestration service that uses temporary tables for staging before API calls
/// Combines the benefits of temporary table validation with API orchestration
/// Includes complete IoT workflow functionality following main migration system patterns
/// </summary>
public class TempTableApiOrchestrationService : IApiOrchestrationService, IEnhancedApiOrchestrationService
{
    private readonly ILogger<TempTableApiOrchestrationService> _logger;
    private readonly ITempStagingService _tempStagingService;
    private readonly IApiOrchestrationService _apiOrchestrationService;
    private readonly IIoTDeviceCreationService _iotDeviceCreationService;
    private readonly BulkSeederConfiguration _config;
    private readonly IEnvironmentConfigurationService _environmentService;

    public TempTableApiOrchestrationService(
        ILogger<TempTableApiOrchestrationService> logger,
        ITempStagingService tempStagingService,
        IApiOrchestrationService apiOrchestrationService,
        IIoTDeviceCreationService iotDeviceCreationService,
        IOptions<BulkSeederConfiguration> config,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tempStagingService = tempStagingService ?? throw new ArgumentNullException(nameof(tempStagingService));
        _apiOrchestrationService = apiOrchestrationService ?? throw new ArgumentNullException(nameof(apiOrchestrationService));
        _iotDeviceCreationService = iotDeviceCreationService ?? throw new ArgumentNullException(nameof(iotDeviceCreationService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    public async Task<ApiOrchestrationResult> CreatePersonDriverBatchAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var requests = personRequests.ToList();

        _logger.LogInformation("Starting temp table enhanced Person/Driver batch creation: {TotalRequests} requests, Session: {SessionId}",
            requests.Count, sessionId);

        if (_config.UseTempTables)
        {
            return await CreatePersonDriverWithTempTablesAsync(requests, batchSize, sessionId, cancellationToken);
        }
        else
        {
            // Fallback to direct API orchestration
            return await _apiOrchestrationService.CreatePersonDriverBatchAsync(requests, batchSize, cancellationToken);
        }
    }

    private async Task<ApiOrchestrationResult> CreatePersonDriverWithTempTablesAsync(
        List<PersonCreateRequest> requests,
        int batchSize,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        var result = new ApiOrchestrationResult
        {
            TotalRequests = requests.Count
        };

        // Use a single connection for the entire temp table session
        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        try
        {
            // Step 1: Create temporary tables
            await _tempStagingService.CreateTempStagingTablesAsync(connection, sessionId, cancellationToken);
            _logger.LogDebug("Created temporary staging tables for session {SessionId}", sessionId);

            // Step 2: Bulk insert to temp tables using SqlBulkCopy
            await _tempStagingService.PopulatePersonDriverTempDataAsync(connection, sessionId, requests, cancellationToken);
            _logger.LogDebug("Populated temporary tables with {RequestCount} requests", requests.Count);

            // Step 3: Validate data in temp tables
            var validationResult = await _tempStagingService.ValidateTempDataAsync(connection, sessionId, cancellationToken);
            _logger.LogInformation("Validation completed: {ValidCount}/{TotalCount} valid records",
                validationResult.ValidPersonRows, validationResult.TotalPersonRows);

            if (!validationResult.Success)
            {
                // Return validation errors
                result.Errors.AddRange(validationResult.ValidationErrors);
                result.FailedRequests = validationResult.InvalidPersonRows;
                result.SuccessfulRequests = 0;
                return result;
            }

            // Step 4: Process valid records through API in batches
            var validRequests = await GetValidatedRequestsFromTempTableAsync(connection, sessionId, cancellationToken);
            var apiResult = await _apiOrchestrationService.CreatePersonDriverBatchAsync(validRequests, batchSize, cancellationToken);

            // Step 5: Update temp table with API results (for audit trail)
            await UpdateTempTableWithApiResultsAsync(connection, sessionId, apiResult, cancellationToken);

            // Step 6: Get final processing summary
            var processingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);

            // Combine results
            result.SuccessfulRequests = apiResult.SuccessfulRequests;
            result.FailedRequests = apiResult.FailedRequests;
            result.Results = apiResult.Results;
            result.Errors.AddRange(apiResult.Errors);
            result.Success = apiResult.Success;

            _logger.LogInformation("Temp table enhanced processing completed: {Successful}/{Total} successful",
                result.SuccessfulRequests, result.TotalRequests);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Temp table enhanced processing failed for session {SessionId}", sessionId);
            result.Errors.Add($"Processing failed: {ex.Message}");
            result.Success = false;
            return result;
        }
        finally
        {
            // Cleanup is automatic when connection closes, but we can explicitly mark session as complete
            try
            {
                await _tempStagingService.CleanupTempTablesAsync(connection, sessionId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Cleanup warning for session {SessionId}", sessionId);
            }
        }
    }

    private async Task<List<PersonCreateRequest>> GetValidatedRequestsFromTempTableAsync(
        SqlConnection connection,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        const string sql = @"
            SELECT 
                FirstName, LastName, SiteId, DepartmentId, CustomerId,
                IsDriver, IsSupervisor, WebsiteAccess,
                VORActivateDeactivate, NormalDriverAccess, CanUnlockVehicle
            FROM #PersonDriverImport 
            WHERE ImportSessionId = @SessionId 
              AND ValidationStatus = 'Valid'
            ORDER BY RowNumber
        ";

        var validatedRequests = new List<PersonCreateRequest>();

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.CommandTimeout = _config.CommandTimeout;

        using var reader = await command.ExecuteReaderAsync(cancellationToken);
        while (await reader.ReadAsync())
        {
            validatedRequests.Add(new PersonCreateRequest
            {
                FirstName = reader["FirstName"].ToString()!,
                LastName = reader["LastName"].ToString()!,
                SiteId = (Guid)reader["SiteId"],
                DepartmentId = (Guid)reader["DepartmentId"],
                CustomerId = (Guid)reader["CustomerId"],
                IsDriver = (bool)reader["IsDriver"],
                IsSupervisor = (bool)reader["IsSupervisor"],
                WebsiteAccess = (bool)reader["WebsiteAccess"],
                // Removed SendDenyMessage - not needed in data seeder, default will be used
                VORActivateDeactivate = (bool)reader["VORActivateDeactivate"],
                NormalDriverAccess = (bool)reader["NormalDriverAccess"],
                CanUnlockVehicle = (bool)reader["CanUnlockVehicle"]
            });
        }

        _logger.LogDebug("Retrieved {ValidatedCount} validated requests from temp table", validatedRequests.Count);
        return validatedRequests;
    }

    private async Task UpdateTempTableWithApiResultsAsync(
        SqlConnection connection,
        Guid sessionId,
        ApiOrchestrationResult apiResult,
        CancellationToken cancellationToken)
    {
        // Update temp table with API results for audit trail
        foreach (var kvp in apiResult.Results)
        {
            var key = kvp.Key;
            var result = kvp.Value;

            // Parse the key to get FirstName_LastName_SiteId
            var keyParts = key.Split('_');
            if (keyParts.Length >= 3)
            {
                var firstName = keyParts[0];
                var lastName = keyParts[1];
                var siteIdString = keyParts[2];

                if (Guid.TryParse(siteIdString, out var siteId))
                {
                    const string updateSql = @"
                        UPDATE #PersonDriverImport 
                        SET PersonId = @PersonId,
                            DriverId = @DriverId,
                            ProcessingAction = @ProcessingAction,
                            ProcessedAt = GETUTCDATE(),
                            ProcessingResult = @ProcessingResult,
                            ProcessingErrors = @ProcessingErrors
                        WHERE ImportSessionId = @SessionId 
                          AND FirstName = @FirstName 
                          AND LastName = @LastName
                          AND SiteId = @SiteId
                    ";

                    using var updateCommand = new SqlCommand(updateSql, connection);
                    updateCommand.Parameters.AddWithValue("@SessionId", sessionId);
                    updateCommand.Parameters.AddWithValue("@FirstName", firstName);
                    updateCommand.Parameters.AddWithValue("@LastName", lastName);
                    updateCommand.Parameters.AddWithValue("@SiteId", siteId);
                    updateCommand.Parameters.AddWithValue("@PersonId", result.PersonId ?? (object)DBNull.Value);
                    updateCommand.Parameters.AddWithValue("@DriverId", result.DriverId ?? (object)DBNull.Value);
                    updateCommand.Parameters.AddWithValue("@ProcessingAction", result.Success ? "API_Success" : "API_Failed");
                    updateCommand.Parameters.AddWithValue("@ProcessingResult", result.Success ? "Created via API" : "API call failed");
                    updateCommand.Parameters.AddWithValue("@ProcessingErrors", result.ErrorMessage ?? (object)DBNull.Value);

                    try
                    {
                        await updateCommand.ExecuteNonQueryAsync(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to update temp table with API result for {Key}", key);
                    }
                }
            }
        }

        _logger.LogDebug("Updated temp table with {ResultCount} API results", apiResult.Results.Count);
    }

    public async Task<bool> ValidateApiConnectivityAsync()
    {
        return await _apiOrchestrationService.ValidateApiConnectivityAsync();
    }

    public async Task<bool> RefreshAuthenticationAsync()
    {
        return await _apiOrchestrationService.RefreshAuthenticationAsync();
    }

    /// <summary>
    /// Helper method to generate IoT device requests for testing and demonstration
    /// Follows the same pattern as the main migration system's data generation
    /// </summary>
    public static List<IoTDeviceCreateRequest> GenerateIoTDeviceRequests(string dealerName, int count, int startingNumber = 1)
    {
        var requests = new List<IoTDeviceCreateRequest>();

        for (int i = 0; i < count; i++)
        {
            var deviceNumber = startingNumber + i;
            requests.Add(new IoTDeviceCreateRequest
            {
                IoTDeviceID = $"SEEDER_IOT_{deviceNumber:D6}",
                Dealer = dealerName,
                CCID = 1000 + deviceNumber,
                RANumber = 2000 + deviceNumber,
                TechNumber = 3000 + deviceNumber
            });
        }

        return requests;
    }

    /// <summary>
    /// Helper method to generate vehicle requests that link to IoT devices
    /// Ensures proper vehicle-to-device mapping following main migration system patterns
    /// </summary>
    public static List<VehicleCreateRequest> GenerateVehicleRequestsWithIoTDevices(
        List<IoTDeviceCreateRequest> iotDeviceRequests,
        Guid modelId,
        Guid siteId,
        Guid departmentId,
        Guid customerId,
        int startingNumber = 1)
    {
        var requests = new List<VehicleCreateRequest>();

        for (int i = 0; i < iotDeviceRequests.Count; i++)
        {
            var vehicleNumber = startingNumber + i;
            var iotDevice = iotDeviceRequests[i];

            requests.Add(new VehicleCreateRequest
            {
                SerialNo = $"SEEDER_VEH_{vehicleNumber:D6}",
                HireNo = $"HIRE_{vehicleNumber:D4}",
                IdleTimer = 300, // 5 minutes default
                OnHire = true,
                ImpactLockout = false,
                TimeoutEnabled = true,
                IsCanbus = i % 2 == 0, // Alternate CAN bus capability
                ModelId = modelId,
                SiteId = siteId,
                DepartmentId = departmentId,
                CustomerId = customerId,
                ModuleIoTDevice = iotDevice.IoTDeviceID, // Link to IoT device
                ChecklistType = ChecklistType.TimeBased
            });
        }

        return requests;
    }

    /// <summary>
    /// Complete IoT-enabled workflow that mirrors the main migration system's 3-phase approach:
    /// Phase 1: Create IoT devices/modules via API (like SpareModuleMigration)
    /// Phase 2: Create vehicles linked to IoT devices (existing functionality)
    /// Phase 3: Sync vehicle IoT settings via API (like VehicleSyncMigration)
    /// </summary>
    public async Task<CompleteIoTWorkflowResult> CreateCompleteIoTWorkflowAsync(
        IEnumerable<IoTDeviceCreateRequest> moduleRequests,
        IEnumerable<VehicleCreateRequest> vehicleRequests,
        int batchSize = 50,
        bool dryRun = false,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var moduleRequestsList = moduleRequests.ToList();
        var vehicleRequestsList = vehicleRequests.ToList();

        _logger.LogInformation("Starting complete IoT workflow: {ModuleCount} modules, {VehicleCount} vehicles, Session: {SessionId}, DryRun: {DryRun}",
            moduleRequestsList.Count, vehicleRequestsList.Count, sessionId, dryRun);

        var result = new CompleteIoTWorkflowResult
        {
            SessionId = sessionId,
            TotalModuleRequests = moduleRequestsList.Count,
            TotalVehicleRequests = vehicleRequestsList.Count,
            DryRun = dryRun
        };

        try
        {
            // Phase 1: Create IoT devices/modules via API (mirrors SpareModuleMigration)
            _logger.LogInformation("Phase 1: Creating {Count} IoT devices/modules via API...", moduleRequestsList.Count);

            if (!dryRun && moduleRequestsList.Count > 0)
            {
                result.ModuleCreationResult = await _iotDeviceCreationService.CreateIoTDeviceBatchAsync(
                    moduleRequestsList, batchSize, cancellationToken);

                if (!result.ModuleCreationResult.Success)
                {
                    result.Success = false;
                    result.Summary = $"Phase 1 failed: IoT device creation failed with {result.ModuleCreationResult.FailedRequests} failures";
                    return result;
                }

                _logger.LogInformation("Phase 1 completed: {Successful}/{Total} IoT devices created",
                    result.ModuleCreationResult.SuccessfulRequests, result.ModuleCreationResult.TotalRequests);
            }
            else if (dryRun)
            {
                _logger.LogInformation("Phase 1 (Dry Run): Would create {Count} IoT devices", moduleRequestsList.Count);
            }

            // Phase 2: Create vehicles linked to IoT devices (existing ComplexEntityCreationService functionality)
            _logger.LogInformation("Phase 2: Creating {Count} vehicles linked to IoT devices...", vehicleRequestsList.Count);

            if (!dryRun && vehicleRequestsList.Count > 0)
            {
                // Note: This would integrate with ComplexEntityCreationService.CreateVehicleBatchAsync
                // For now, we'll simulate this step
                _logger.LogInformation("Phase 2: Vehicle creation would be handled by ComplexEntityCreationService");
                result.VehicleCreationSuccess = true;
            }
            else if (dryRun)
            {
                _logger.LogInformation("Phase 2 (Dry Run): Would create {Count} vehicles", vehicleRequestsList.Count);
                result.VehicleCreationSuccess = true;
            }

            // Phase 3: Sync vehicle IoT settings via API (mirrors VehicleSyncMigration)
            if (result.ModuleCreationResult?.Success == true || dryRun)
            {
                var deviceIdsToSync = dryRun
                    ? moduleRequestsList.Select(m => m.IoTDeviceID).ToList()
                    : result.ModuleCreationResult?.Results
                        .Where(r => r.Value.Success && !string.IsNullOrEmpty(r.Value.IoTDeviceID))
                        .Select(r => r.Value.IoTDeviceID!)
                        .ToList() ?? [];

                _logger.LogInformation("Phase 3: Syncing {Count} vehicle IoT settings...", deviceIdsToSync.Count);

                if (!dryRun && deviceIdsToSync.Count > 0)
                {
                    result.SyncResult = await _iotDeviceCreationService.SyncVehicleIoTSettingsAsync(
                        deviceIdsToSync, cancellationToken);

                    _logger.LogInformation("Phase 3 completed: {Successful}/{Total} devices synced",
                        result.SyncResult.SuccessfulSyncs, result.SyncResult.TotalDevices);
                }
                else if (dryRun)
                {
                    _logger.LogInformation("Phase 3 (Dry Run): Would sync {Count} devices", deviceIdsToSync.Count);
                }
            }

            // Determine overall success
            result.Success = (result.ModuleCreationResult?.Success ?? true) &&
                           result.VehicleCreationSuccess &&
                           (result.SyncResult?.Success ?? true);

            result.Summary = dryRun
                ? $"Dry run completed: Would process {moduleRequestsList.Count} modules and {vehicleRequestsList.Count} vehicles"
                : $"IoT workflow completed: {result.ModuleCreationResult?.SuccessfulRequests ?? 0} modules, {vehicleRequestsList.Count} vehicles, {result.SyncResult?.SuccessfulSyncs ?? 0} synced";

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Complete IoT workflow failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Summary = $"Workflow failed: {ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// Extended method for bulk operations with full temp table validation and processing
    /// </summary>
    public async Task<TempTableOrchestrationResult> CreatePersonDriverBatchWithFullValidationAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        bool dryRun = false,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var requests = personRequests.ToList();

        _logger.LogInformation("Starting full temp table validation for {TotalRequests} requests, Session: {SessionId}, DryRun: {DryRun}",
            requests.Count, sessionId, dryRun);

        var result = new TempTableOrchestrationResult
        {
            SessionId = sessionId,
            TotalRequests = requests.Count,
            DryRun = dryRun
        };

        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        try
        {
            // Step 1: Create temporary tables and populate
            await _tempStagingService.CreateTempStagingTablesAsync(connection, sessionId, cancellationToken);
            await _tempStagingService.PopulatePersonDriverTempDataAsync(connection, sessionId, requests, cancellationToken);

            // Step 2: Validate
            result.ValidationResult = await _tempStagingService.ValidateTempDataAsync(connection, sessionId, cancellationToken);

            if (!result.ValidationResult.Success)
            {
                result.Success = false;
                result.Summary = $"Validation failed: {result.ValidationResult.InvalidPersonRows} invalid records";
                return result;
            }

            // Step 3: Process (API calls or dry run)
            if (dryRun)
            {
                result.ProcessingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);
                result.Success = true;
                result.Summary = $"Dry run completed: {result.ValidationResult.ValidPersonRows} records would be processed";
            }
            else
            {
                var validRequests = await GetValidatedRequestsFromTempTableAsync(connection, sessionId, cancellationToken);
                result.ApiResult = await _apiOrchestrationService.CreatePersonDriverBatchAsync(validRequests, batchSize, cancellationToken);
                await UpdateTempTableWithApiResultsAsync(connection, sessionId, result.ApiResult, cancellationToken);
                result.ProcessingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);

                result.Success = result.ApiResult.Success;
                result.Summary = $"Processing completed: {result.ApiResult.SuccessfulRequests}/{result.TotalRequests} successful";
            }

            // Step 4: Get final summary
            result.StagingSummary = await _tempStagingService.GetTempStagingSummaryAsync(connection, sessionId, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Full temp table validation failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Summary = $"Processing failed: {ex.Message}";
            return result;
        }
    }
}

/// <summary>
/// Extended result that includes temp table validation and processing details
/// </summary>
public class TempTableOrchestrationResult
{
    public Guid SessionId { get; set; }
    public int TotalRequests { get; set; }
    public bool DryRun { get; set; }
    public bool Success { get; set; }
    public string Summary { get; set; } = string.Empty;
    public TempValidationResult? ValidationResult { get; set; }
    public TempProcessingResult? ProcessingResult { get; set; }
    public ApiOrchestrationResult? ApiResult { get; set; }
    public TempStagingSummary? StagingSummary { get; set; }
}

/// <summary>
/// Result model for complete IoT workflow operations
/// Includes all three phases: Module creation, Vehicle creation, and IoT sync
/// </summary>
public class CompleteIoTWorkflowResult
{
    public Guid SessionId { get; set; }
    public int TotalModuleRequests { get; set; }
    public int TotalVehicleRequests { get; set; }
    public bool DryRun { get; set; }
    public bool Success { get; set; }
    public string Summary { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }

    // Phase 1: IoT Device Creation
    public IoTDeviceCreationResult? ModuleCreationResult { get; set; }

    // Phase 2: Vehicle Creation
    public bool VehicleCreationSuccess { get; set; }
    public string? VehicleCreationSummary { get; set; }

    // Phase 3: IoT Device Synchronization
    public IoTDeviceSyncResult? SyncResult { get; set; }

    public List<string> Errors { get; set; } = [];
    public List<string> Warnings { get; set; } = [];
}
