﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="ee39f70d-9454-42f9-9a29-ee1c44e46c00" name="redmi@DESKTOP-LJEH13I 2025-08-19 15:56:07" runUser="DESKTOP-LJEH13I\redmi" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-08-19T15:56:07.3942914+08:00" queuing="2025-08-19T15:56:07.3942919+08:00" start="2025-08-19T15:56:05.8191409+08:00" finish="2025-08-19T15:57:35.2143512+08:00" />
  <TestSettings name="default" id="5a3a9352-914c-40e5-8771-a67dd21209b0">
    <Deployment runDeploymentRoot="redmi_DESKTOP-LJEH13I_2025-08-19_15_56_07" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="c697f00d-00e9-4f6a-a052-b1a1b0808025" testId="cca9f9ca-c236-de25-010f-3c4126cf6217" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithValidParameters_ShouldCreateInstance" computerName="DESKTOP-LJEH13I" duration="00:00:00.0011637" startTime="2025-08-19T15:57:07.7093439+08:00" endTime="2025-08-19T15:57:07.7093441+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c697f00d-00e9-4f6a-a052-b1a1b0808025" />
    <UnitTestResult executionId="5a290f23-15ca-48a2-9660-2770de06b6a8" testId="b8a95959-4bee-a7e9-6c11-3b07491d71d6" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithDisabledSteps_ShouldSkipThoseSteps" computerName="DESKTOP-LJEH13I" duration="00:00:00.0215754" startTime="2025-08-19T15:56:07.3714096+08:00" endTime="2025-08-19T15:56:07.3714097+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5a290f23-15ca-48a2-9660-2770de06b6a8" />
    <UnitTestResult executionId="1ec10977-ea90-4516-baac-e7c9762c098c" testId="9c54f0d5-b9d8-56fb-45bb-d91a6dfc2e1d" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.Services_ShouldUseCorrectConfiguration" computerName="DESKTOP-LJEH13I" duration="00:00:00.0295522" startTime="2025-08-19T15:56:39.8731789+08:00" endTime="2025-08-19T15:56:39.8731793+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1ec10977-ea90-4516-baac-e7c9762c098c" />
    <UnitTestResult executionId="0e973a81-28dc-4e41-b30b-5c72b9971d6a" testId="dba81b2c-ec26-a155-9a9e-9086ac66e31d" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.ServiceProvider_ShouldCreateSingletonServices" computerName="DESKTOP-LJEH13I" duration="00:00:00.0007563" startTime="2025-08-19T15:56:39.7954562+08:00" endTime="2025-08-19T15:56:39.7954564+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0e973a81-28dc-4e41-b30b-5c72b9971d6a" />
    <UnitTestResult executionId="f90742f9-90a5-4b8f-b440-759f15060578" testId="73082ec6-b33c-3ff2-242f-48536536d825" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithNullEnvironmentService_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0007600" startTime="2025-08-19T15:57:35.1270632+08:00" endTime="2025-08-19T15:57:35.1270636+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f90742f9-90a5-4b8f-b440-759f15060578" />
    <UnitTestResult executionId="8602d428-90a9-4173-808a-fee2c2884414" testId="3e484da7-2b24-cba3-38e5-1a6054f7a210" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync" computerName="DESKTOP-LJEH13I" duration="00:00:00.0131150" startTime="2025-08-19T15:57:07.7409222+08:00" endTime="2025-08-19T15:57:07.7409228+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8602d428-90a9-4173-808a-fee2c2884414">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 182&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="08c7f665-e3e8-43c6-adca-e490971f53ef" testId="6689876c-74fe-b212-fe96-a34eec5f0b56" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.9815712" startTime="2025-08-19T15:57:16.8116128+08:00" endTime="2025-08-19T15:57:16.8116134+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="08c7f665-e3e8-43c6-adca-e490971f53ef">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 101&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="a68bae05-8904-451e-b8aa-a694879fafa7" testId="9f2f3e4e-bb0c-df30-bfb6-cd84bf546c86" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_RepeatedValidations_ShouldMaintainPerformance" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.3318268+08:00" endTime="2025-08-19T15:56:07.3318270+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a68bae05-8904-451e-b8aa-a694879fafa7">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="b026381b-8f19-4542-b049-6d32972cd1a3" testId="cf04b253-bce9-e0cf-7d8b-cef8b46e1913" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithNullOptions_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0003672" startTime="2025-08-19T15:56:29.7860200+08:00" endTime="2025-08-19T15:56:29.7860202+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b026381b-8f19-4542-b049-6d32972cd1a3" />
    <UnitTestResult executionId="e690c57c-8048-4e1e-a3f2-5aab89ff48a0" testId="bcbd6ceb-50cd-57b4-fff5-75c4520f6d90" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.EnvironmentConfigurationService_ShouldProvideCorrectConfiguration" computerName="DESKTOP-LJEH13I" duration="00:00:00.1488462" startTime="2025-08-19T15:56:07.2469041+08:00" endTime="2025-08-19T15:56:07.2469042+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e690c57c-8048-4e1e-a3f2-5aab89ff48a0" />
    <UnitTestResult executionId="6ce3c801-4054-42ac-b533-6cedae9af507" testId="9a52e156-ddcf-a899-8fad-0a1b967ffe71" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WithInvalidApi_ShouldReturnInvalidResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.1568653" startTime="2025-08-19T15:56:07.2467095+08:00" endTime="2025-08-19T15:56:07.2467098+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6ce3c801-4054-42ac-b533-6cedae9af507" />
    <UnitTestResult executionId="0cb53361-bf9c-440c-83ef-d7ca1400d19c" testId="1d9890c2-c4b6-1d38-4853-a0af1c67d765" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0124087" startTime="2025-08-19T15:57:07.7234222+08:00" endTime="2025-08-19T15:57:07.7234225+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0cb53361-bf9c-440c-83ef-d7ca1400d19c">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 256&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="c4e2c15b-a6ad-4fe6-a584-ac489cf8b633" testId="6a2a6ed1-807d-4ade-6e78-24c6ba29ce23" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly" computerName="DESKTOP-LJEH13I" duration="00:00:00.0045522" startTime="2025-08-19T15:57:07.7088056+08:00" endTime="2025-08-19T15:57:07.7088062+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c4e2c15b-a6ad-4fe6-a584-ac489cf8b633">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 360&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="e8fa3852-5368-4679-a15b-3411252684a2" testId="bb3baddf-798b-d8f3-50f9-1974bb37a637" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithValidParameters_ShouldCreateInstance" computerName="DESKTOP-LJEH13I" duration="00:00:00.0002788" startTime="2025-08-19T15:56:07.3345916+08:00" endTime="2025-08-19T15:56:07.3345917+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e8fa3852-5368-4679-a15b-3411252684a2" />
    <UnitTestResult executionId="f7562514-d423-495d-b287-456c04ae3c30" testId="f1a49d28-8732-54db-270c-0cff0464b0d7" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithValidParameters_ShouldCreateInstance" computerName="DESKTOP-LJEH13I" duration="00:00:00.0003183" startTime="2025-08-19T15:56:29.7854651+08:00" endTime="2025-08-19T15:56:29.7854653+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f7562514-d423-495d-b287-456c04ae3c30" />
    <UnitTestResult executionId="2222a1f5-e700-46bf-be35-73ec61131759" testId="59283c7c-ad10-179e-17c7-88f1143fa25f" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync" computerName="DESKTOP-LJEH13I" duration="00:00:00.0073936" startTime="2025-08-19T15:57:07.6884741+08:00" endTime="2025-08-19T15:57:07.6884743+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2222a1f5-e700-46bf-be35-73ec61131759">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 201&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="c712aeaa-7767-4f74-9c25-dbb59f2b1cb8" testId="78b99b4e-f2e7-b487-890f-7d96ffdc2d03" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall" computerName="DESKTOP-LJEH13I" duration="00:00:00.0103806" startTime="2025-08-19T15:56:07.5343322+08:00" endTime="2025-08-19T15:56:07.5343326+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c712aeaa-7767-4f74-9c25-dbb59f2b1cb8">
      <Output>
        <ErrorInfo>
          <Message>Assert.True() Failure&#xD;
Expected: True&#xD;
Actual:   False</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 176&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="72c43326-4211-4261-a5da-722f293bb0f8" testId="0bf4ce2a-e887-fcb9-feb7-9cbdcf1a6254" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0008172" startTime="2025-08-19T15:57:15.8294598+08:00" endTime="2025-08-19T15:57:15.8294600+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="72c43326-4211-4261-a5da-722f293bb0f8">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.ArgumentException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 205&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="24441366-e2d5-496f-8527-e85e056c0a12" testId="0c95a68f-eb5c-e5ee-d93c-99aa0f66aab5" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0008560" startTime="2025-08-19T15:57:35.1322917+08:00" endTime="2025-08-19T15:57:35.1322921+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="24441366-e2d5-496f-8527-e85e056c0a12">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.ArgumentException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 137&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="fa91c93c-d8d6-4261-aba8-ba98691b972e" testId="cd262b36-9e57-616e-0f2b-f96ca8170c7b" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithNullSqlDataService_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0006250" startTime="2025-08-19T15:57:07.7101069+08:00" endTime="2025-08-19T15:57:07.7101071+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fa91c93c-d8d6-4261-aba8-ba98691b972e" />
    <UnitTestResult executionId="66aea12f-c2ce-455f-a9cd-a4e3a24ac817" testId="5b5f0260-c999-9d44-4255-86ed147cd1a4" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteCardAccessSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutCreation" computerName="DESKTOP-LJEH13I" duration="00:00:00.0268561" startTime="2025-08-19T15:56:07.3990048+08:00" endTime="2025-08-19T15:56:07.3990052+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="66aea12f-c2ce-455f-a9cd-a4e3a24ac817" />
    <UnitTestResult executionId="04adbb24-4d7e-4280-aee1-109c36872851" testId="17c90c4b-9c96-54e6-4777-88b8d8560cdc" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithZeroCounts_ShouldReturnSuccessWithoutGeneration" computerName="DESKTOP-LJEH13I" duration="00:00:00.0051005" startTime="2025-08-19T15:57:07.7032547+08:00" endTime="2025-08-19T15:57:07.7032549+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="04adbb24-4d7e-4280-aee1-109c36872851">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Drivers count must be positive (Parameter 'DriversCount')</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ValidateSeederOptions(SeederOptions options) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 124&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 74&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithZeroCounts_ShouldReturnSuccessWithoutGeneration() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 276&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="e4f57106-9558-49b9-b0cb-b88888d88733" testId="e92355f6-7aa2-92f9-f5f5-2a14225fa627" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithNullLogger_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0004086" startTime="2025-08-19T15:57:15.8280320+08:00" endTime="2025-08-19T15:57:15.8280322+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e4f57106-9558-49b9-b0cb-b88888d88733" />
    <UnitTestResult executionId="b8882ede-945c-4502-af72-ee98bb37b9a2" testId="f9c9fcf0-5ef5-b76e-0f16-da18767ae088" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_ConcurrentApiCalls_ShouldHandleCorrectly" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.2418024+08:00" endTime="2025-08-19T15:56:07.2418025+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8882ede-945c-4502-af72-ee98bb37b9a2">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="790bbd42-e85b-4d37-a619-7d6394b626be" testId="03c22203-bbdb-5426-17b8-de4d477d6416" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_RepeatedOperations_ShouldMaintainPerformance" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.2424144+08:00" endTime="2025-08-19T15:56:07.2424146+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="790bbd42-e85b-4d37-a619-7d6394b626be">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="69532133-e3e9-4c61-90b6-a0c3774689fa" testId="90c69325-cd22-6f48-bbac-02bf30c3cd68" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithNullComplexEntityService_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0007402" startTime="2025-08-19T15:56:07.4000023+08:00" endTime="2025-08-19T15:56:07.4000026+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69532133-e3e9-4c61-90b6-a0c3774689fa" />
    <UnitTestResult executionId="35cee18d-cc65-401d-99f8-d50b72ecd0ae" testId="470d0e63-4256-53f3-23d2-acbde8f9cbef" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithNullLogger_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0006363" startTime="2025-08-19T15:57:07.6754416+08:00" endTime="2025-08-19T15:57:07.6754419+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="35cee18d-cc65-401d-99f8-d50b72ecd0ae" />
    <UnitTestResult executionId="c6fbc537-9725-4511-b5f0-645151a50660" testId="6b2931ea-6abf-5e24-6915-f9ef4a3ec54c" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_ApiCalls_ShouldRespectRateLimit" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.2367784+08:00" endTime="2025-08-19T15:56:07.2368025+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c6fbc537-9725-4511-b5f0-645151a50660">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="bd57c5e8-50f5-454d-b2b0-cff87110cf1c" testId="76133b50-a240-76bc-1084-d0253cb510bd" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithNullLogger_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0005210" startTime="2025-08-19T15:56:07.3831954+08:00" endTime="2025-08-19T15:56:07.3831958+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bd57c5e8-50f5-454d-b2b0-cff87110cf1c" />
    <UnitTestResult executionId="c6535fba-2cb0-44de-a5e1-b672e500f675" testId="59d33cdc-771a-4c8b-2dd4-691dd0e63ba9" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WithCancellation_ShouldStopGracefully" computerName="DESKTOP-LJEH13I" duration="00:00:00.3941707" startTime="2025-08-19T15:56:07.6660655+08:00" endTime="2025-08-19T15:56:07.6660658+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c6535fba-2cb0-44de-a5e1-b672e500f675">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: Exception type was not an exact match&#xD;
Expected: typeof(System.OperationCanceledException)&#xD;
Actual:   typeof(System.InvalidOperationException)&#xD;
---- System.InvalidOperationException : Data validation failed: A task was canceled.</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WithCancellation_ShouldStopGracefully() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 263&#xD;
--- End of stack trace from previous location ---&#xD;
----- Inner Stack Trace -----&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 213&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="2e64af49-7878-4724-aff0-333025c3151c" testId="58069d41-1e47-e610-9c8a-f765fc1baab1" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully" computerName="DESKTOP-LJEH13I" duration="00:00:32.1115552" startTime="2025-08-19T15:56:39.7940592+08:00" endTime="2025-08-19T15:56:39.7940594+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2e64af49-7878-4724-aff0-333025c3151c">
      <Output>
        <ErrorInfo>
          <Message>Assert.False() Failure&#xD;
Expected: False&#xD;
Actual:   True</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 219&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="f1fb55a7-6da5-4223-9a93-345d23f1e918" testId="c0608df9-0d01-d669-4783-192fb8c7ade2" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException" computerName="DESKTOP-LJEH13I" duration="00:00:21.1944103" startTime="2025-08-19T15:57:15.8276766+08:00" endTime="2025-08-19T15:57:15.8276769+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f1fb55a7-6da5-4223-9a93-345d23f1e918">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.ArgumentException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 272&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="7440b95a-4408-4da7-8818-a20cb3d476e1" testId="4a47ff44-57fa-c61a-115a-822a69921b85" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly" computerName="DESKTOP-LJEH13I" duration="00:00:18.3117142" startTime="2025-08-19T15:57:35.1244741+08:00" endTime="2025-08-19T15:57:35.1244745+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7440b95a-4408-4da7-8818-a20cb3d476e1">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 304&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="0b5ab02a-f870-475c-95f4-60d6c503e2e5" testId="d8967d53-9423-6c4b-bc12-c3d200055288" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure" computerName="DESKTOP-LJEH13I" duration="00:00:00.0578035" startTime="2025-08-19T15:56:07.3344706+08:00" endTime="2025-08-19T15:56:07.3344708+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0b5ab02a-f870-475c-95f4-60d6c503e2e5">
      <Output>
        <ErrorInfo>
          <Message>Assert.Contains() Failure: Item not found in collection&#xD;
Collection: ["Person/Driver creation failed - stopping migration"···]&#xD;
Not found:  "Person/Driver creation failed"</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 334&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="f3763080-747c-4939-a6da-9f12c87d5531" testId="aea2325a-5eda-6357-aea9-11524413e94a" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_LargeLoad_ShouldNotExceedMemoryThreshold" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.2533203+08:00" endTime="2025-08-19T15:56:07.2533205+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f3763080-747c-4939-a6da-9f12c87d5531">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="6913a223-db7f-488f-afc0-04d50192344c" testId="dbc0ea9c-4730-fff9-f4e6-920d04da1cab" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_ShouldSendProgressNotifications" computerName="DESKTOP-LJEH13I" duration="00:00:00.0044920" startTime="2025-08-19T15:57:07.6932811+08:00" endTime="2025-08-19T15:57:07.6932813+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6913a223-db7f-488f-afc0-04d50192344c">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_ShouldSendProgressNotifications() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 389&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="d112b053-d30e-43c1-9467-5fa399bc6f54" testId="00ab8bff-d948-51f4-df1c-8dba87f291b1" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithNullOptions_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0008317" startTime="2025-08-19T15:57:07.7238959+08:00" endTime="2025-08-19T15:57:07.7238962+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d112b053-d30e-43c1-9467-5fa399bc6f54" />
    <UnitTestResult executionId="804ccfd1-acaf-44c2-b8f3-37b556aaaf21" testId="1eb5be61-77d5-a6a7-67a9-a10af71763a7" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0022131" startTime="2025-08-19T15:57:07.7272185+08:00" endTime="2025-08-19T15:57:07.7272187+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="804ccfd1-acaf-44c2-b8f3-37b556aaaf21">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 125&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="55a8479f-e9e3-47a6-ae63-f869fbc7b45b" testId="3c1a1e97-992a-fa7c-e475-75a6271f32ca" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithNullCounts_ShouldReturnSuccessWithoutGeneration" computerName="DESKTOP-LJEH13I" duration="00:00:00.0046594" startTime="2025-08-19T15:56:37.6008781+08:00" endTime="2025-08-19T15:56:37.6008783+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="55a8479f-e9e3-47a6-ae63-f869fbc7b45b">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 173&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithNullCounts_ShouldReturnSuccessWithoutGeneration() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 299&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="fb9184f3-8785-4ecc-95c5-ba4687a4dab6" testId="da280ef2-fb3b-29de-978c-cdd0f6b142d9" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0081894" startTime="2025-08-19T15:56:07.3713219+08:00" endTime="2025-08-19T15:56:07.3713221+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fb9184f3-8785-4ecc-95c5-ba4687a4dab6">
      <Output>
        <ErrorInfo>
          <Message>Assert.Contains() Failure: Item not found in collection&#xD;
Collection: ["Validation failed: API validation failed"]&#xD;
Not found:  "API validation failed"</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 415&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="71d0618c-59f8-4bb3-8082-63a1273888ec" testId="bf85b4b1-f9cf-3a75-b2a9-b0df938dd17a" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_ConcurrentRequests_ShouldHandleCorrectly" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.2409170+08:00" endTime="2025-08-19T15:56:07.2409172+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="71d0618c-59f8-4bb3-8082-63a1273888ec">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="683b5fe1-f779-4463-a293-ae79c1982d52" testId="90a2fd0f-f60f-b4fe-70e7-623354cc4fa3" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0155499" startTime="2025-08-19T15:56:07.5233941+08:00" endTime="2025-08-19T15:56:07.5233943+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="683b5fe1-f779-4463-a293-ae79c1982d52">
      <Output>
        <ErrorInfo>
          <Message>Assert.True() Failure&#xD;
Expected: True&#xD;
Actual:   False</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 310&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="db3ea9ae-8d0c-49c5-acd0-a20ac79770f0" testId="6d3b8ec7-609b-a343-2e1d-8c74a43386b9" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult" computerName="DESKTOP-LJEH13I" duration="00:00:07.1197603" startTime="2025-08-19T15:56:22.6531607+08:00" endTime="2025-08-19T15:56:22.6531609+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="db3ea9ae-8d0c-49c5-acd0-a20ac79770f0">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 256&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="ff512129-cd8d-4ffa-a0f7-e43d6233f1c8" testId="a7960952-f3c0-0a2f-c41d-994ad12d7eea" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException" computerName="DESKTOP-LJEH13I" duration="00:00:05.8664933" startTime="2025-08-19T15:56:28.5274390+08:00" endTime="2025-08-19T15:56:28.5274393+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ff512129-cd8d-4ffa-a0f7-e43d6233f1c8">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.ArgumentException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 238&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="596312b1-0ee5-4aca-a80e-6a9021a3725c" testId="75029862-8fda-8053-867f-d148a3662fb9" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation" computerName="DESKTOP-LJEH13I" duration="00:00:00.0042055" startTime="2025-08-19T15:56:22.6582683+08:00" endTime="2025-08-19T15:56:22.6582685+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="596312b1-0ee5-4aca-a80e-6a9021a3725c">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.OperationCanceledException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 152&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="c4504d01-ffac-4701-a9ce-675b90a88f7c" testId="c68dbc4c-a4a9-1930-4389-1b67c8381c5d" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows" computerName="DESKTOP-LJEH13I" duration="00:00:00.0049491" startTime="2025-08-19T15:56:29.7848689+08:00" endTime="2025-08-19T15:56:29.7848692+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c4504d01-ffac-4701-a9ce-675b90a88f7c" />
    <UnitTestResult executionId="7f9e1e54-549d-426f-94c9-0fc069d325f8" testId="9da640db-aeb4-1572-a340-2fd028e77f46" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:01.2390998" startTime="2025-08-19T15:56:29.7809510+08:00" endTime="2025-08-19T15:56:29.7809513+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7f9e1e54-549d-426f-94c9-0fc069d325f8">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 170&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="7946ea53-981c-4e6e-91cb-7255ad205520" testId="01de2f0c-4dbb-c0ce-1c6f-805fa3e3511b" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall" computerName="DESKTOP-LJEH13I" duration="00:00:00.0012571" startTime="2025-08-19T15:56:07.4737297+08:00" endTime="2025-08-19T15:56:07.4737300+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7946ea53-981c-4e6e-91cb-7255ad205520">
      <Output>
        <ErrorInfo>
          <Message>Assert.True() Failure&#xD;
Expected: True&#xD;
Actual:   False</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 434&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="cd417418-9c85-45f2-93cd-03eb0a1ea3fd" testId="c1817665-cceb-4543-a94b-53771d135741" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration" computerName="DESKTOP-LJEH13I" duration="00:00:00.0200556" startTime="2025-08-19T15:56:07.5134384+08:00" endTime="2025-08-19T15:56:07.5134386+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cd417418-9c85-45f2-93cd-03eb0a1ea3fd">
      <Output>
        <ErrorInfo>
          <Message>Moq.MockException : &#xD;
Expected invocation on the mock once, but was 0 times: x =&gt; x.CreatePersonDriverBatchAsync(It.Is&lt;IEnumerable&lt;PersonCreateRequest&gt;&gt;(requests =&gt; requests.Count&lt;PersonCreateRequest&gt;() == 25), It.IsAny&lt;int&gt;(), It.IsAny&lt;CancellationToken&gt;())&#xD;
&#xD;
Performed invocations:&#xD;
&#xD;
   Mock&lt;IApiOrchestrationService:30&gt; (x):&#xD;
&#xD;
      IApiOrchestrationService.ValidateApiConnectivityAsync()&#xD;
</Message>
          <StackTrace>   at Moq.Mock.Verify(Mock mock, LambdaExpression expression, Times times, String failMessage) in /_/src/Moq/Mock.cs:line 332&#xD;
   at Moq.Mock`1.Verify[TResult](Expression`1 expression, Func`1 times) in /_/src/Moq/Mock`1.cs:line 840&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 158&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="58ba88f0-48c2-4dfa-a34a-227ce67298d7" testId="ba19c163-36db-83d4-322c-3599a9e8b8e3" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether" computerName="DESKTOP-LJEH13I" duration="00:00:00.0064893" startTime="2025-08-19T15:57:15.8471201+08:00" endTime="2025-08-19T15:57:15.8471203+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="58ba88f0-48c2-4dfa-a34a-227ce67298d7">
      <Output>
        <ErrorInfo>
          <Message>Assert.True() Failure&#xD;
Expected: True&#xD;
Actual:   False</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 117&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="7c19e753-129c-4130-9ace-ae952dc5ec80" testId="fd69b7f9-bd08-ae50-6803-545ffe7a4a03" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_LargeLoad_ShouldNotExceedMemoryThreshold" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.3335998+08:00" endTime="2025-08-19T15:56:07.3335999+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7c19e753-129c-4130-9ace-ae952dc5ec80">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="4a724b2c-0dd5-44ea-99c5-d9b61308d86c" testId="006ac670-2165-fcad-82a6-603152ba0046" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully" computerName="DESKTOP-LJEH13I" duration="00:00:00.0155139" startTime="2025-08-19T15:56:39.8270876+08:00" endTime="2025-08-19T15:56:39.8270878+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4a724b2c-0dd5-44ea-99c5-d9b61308d86c">
      <Output>
        <ErrorInfo>
          <Message>Assert.Contains() Failure: Sub-string not found&#xD;
String:    ""&#xD;
Not found: "API service failed"</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 244&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="43863307-3960-45ec-ade3-ee77a88dda02" testId="6fe8b7ec-1d17-d467-bb0f-52ebf3965f7c" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows" computerName="DESKTOP-LJEH13I" duration="00:00:00.0006954" startTime="2025-08-19T15:56:29.7868044+08:00" endTime="2025-08-19T15:56:29.7868045+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="43863307-3960-45ec-ade3-ee77a88dda02" />
    <UnitTestResult executionId="b51ecbd0-2246-43a1-9fec-6885551807de" testId="1d32c3e4-680b-3ee9-a18a-7d4863c6bc67" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WithValidSetup_ShouldReturnValidResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010528" startTime="2025-08-19T15:56:07.4237929+08:00" endTime="2025-08-19T15:56:07.4237933+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b51ecbd0-2246-43a1-9fec-6885551807de" />
    <UnitTestResult executionId="1bfa5ccc-6f05-4a35-b081-cc39a380ce1b" testId="2b48b1d9-2a70-af56-1900-37baa9747c9e" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WithCancellation_ShouldStopGracefully" computerName="DESKTOP-LJEH13I" duration="00:00:00.0042565" startTime="2025-08-19T15:57:15.8402814+08:00" endTime="2025-08-19T15:57:15.8402820+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1bfa5ccc-6f05-4a35-b081-cc39a380ce1b">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.OperationCanceledException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WithCancellation_ShouldStopGracefully() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 277&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="2bef8343-0112-4ce7-9f72-e305bee2c60c" testId="5707ecc5-fffb-c2e4-1fb3-bf1ec0e6fb0f" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteVehicleSeederAsync_WithZeroVehiclesCount_ShouldReturnSuccessWithoutCreation" computerName="DESKTOP-LJEH13I" duration="00:00:00.0008285" startTime="2025-08-19T15:56:07.4716214+08:00" endTime="2025-08-19T15:56:07.4716215+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2bef8343-0112-4ce7-9f72-e305bee2c60c" />
    <UnitTestResult executionId="d2ebe025-84e8-47cb-ad4e-9a019129f856" testId="9f223ca4-b1f1-e7d9-d489-65c2d75bdd12" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult" computerName="DESKTOP-LJEH13I" duration="00:00:30.0713844" startTime="2025-08-19T15:56:37.5958786+08:00" endTime="2025-08-19T15:56:37.5958791+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d2ebe025-84e8-47cb-ad4e-9a019129f856">
      <Output>
        <ErrorInfo>
          <Message>System.InvalidOperationException : Database connection failed</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 171&#xD;
--- End of stack trace from previous location ---&#xD;
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 171&#xD;
--- End of stack trace from previous location ---&#xD;
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 171&#xD;
--- End of stack trace from previous location ---&#xD;
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 171&#xD;
--- End of stack trace from previous location ---&#xD;
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
   at Polly.AsyncPolicy.ExecuteAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteWithRetry[T](Func`1 operation) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 276&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 170&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 228&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="b7466448-52f0-433e-8848-122f7d6273b9" testId="444edcb7-ef42-2182-5358-a8500aa52c94" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_ShouldSendSignalRNotifications" computerName="DESKTOP-LJEH13I" duration="00:00:00.0143367" startTime="2025-08-19T15:56:39.8425661+08:00" endTime="2025-08-19T15:56:39.8425663+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b7466448-52f0-433e-8848-122f7d6273b9">
      <Output>
        <ErrorInfo>
          <Message>Moq.MockException : &#xD;
Expected invocation on the mock at least once, but was never performed: x =&gt; x.SendCoreAsync("ProgressUpdate", It.IsAny&lt;object[]&gt;(), It.IsAny&lt;CancellationToken&gt;())&#xD;
&#xD;
Performed invocations:&#xD;
&#xD;
   Mock&lt;IClientProxy:41&gt; (x):&#xD;
   No invocations performed.&#xD;
</Message>
          <StackTrace>   at Moq.Mock.Verify(Mock mock, LambdaExpression expression, Times times, String failMessage) in /_/src/Moq/Mock.cs:line 332&#xD;
   at Moq.Mock`1.Verify[TResult](Expression`1 expression, Func`1 times) in /_/src/Moq/Mock`1.cs:line 840&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_ShouldSendSignalRNotifications() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 162&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="1e6d086a-d2bc-4d10-84cc-04208b231dae" testId="6619b0e6-03af-c16c-6adc-710a6e9813a1" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.ServiceProvider_ShouldResolveAllRequiredServices" computerName="DESKTOP-LJEH13I" duration="00:00:00.0070322" startTime="2025-08-19T15:57:15.8540714+08:00" endTime="2025-08-19T15:57:15.8540717+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1e6d086a-d2bc-4d10-84cc-04208b231dae" />
    <UnitTestResult executionId="904a2e5c-8fbd-4cd1-bad4-2bd69436516d" testId="0292f93a-fcbd-9803-4f38-6fd7cd563804" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteVehicleSeederAsync_WithVehiclesCount_ShouldCallComplexEntityService" computerName="DESKTOP-LJEH13I" duration="00:00:00.0132604" startTime="2025-08-19T15:56:07.4866041+08:00" endTime="2025-08-19T15:56:07.4866048+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="904a2e5c-8fbd-4cd1-bad4-2bd69436516d" />
    <UnitTestResult executionId="d2819465-7abb-4df8-800d-5c3a477dad25" testId="a299c426-06e4-7c23-192f-fcb680715a3b" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenCancelled_ShouldThrowOperationCancelledException" computerName="DESKTOP-LJEH13I" duration="00:00:30.0313475" startTime="2025-08-19T15:57:07.6746794+08:00" endTime="2025-08-19T15:57:07.6746801+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d2819465-7abb-4df8-800d-5c3a477dad25" />
    <UnitTestResult executionId="9ec657e2-1273-4021-82ec-faed71c55a97" testId="5c955ef4-15b8-7fbe-326e-a00b2edc2985" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteCardAccessSeederAsync_WithDriversCount_ShouldCallComplexEntityService" computerName="DESKTOP-LJEH13I" duration="00:00:00.0321079" startTime="2025-08-19T15:56:07.4707090+08:00" endTime="2025-08-19T15:56:07.4707093+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9ec657e2-1273-4021-82ec-faed71c55a97" />
    <UnitTestResult executionId="be027ca7-3511-4280-8a5f-224fdb8d104f" testId="b76a5fb1-5673-1d33-0f17-2647871784f4" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices" computerName="DESKTOP-LJEH13I" duration="00:00:00.0140288" startTime="2025-08-19T15:56:37.6158489+08:00" endTime="2025-08-19T15:56:37.6158492+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="be027ca7-3511-4280-8a5f-224fdb8d104f">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 322&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="3dfb45ce-9854-431f-9a21-90645ec69e9a" testId="befbebd1-3fbb-92cf-b910-9c503e86bf33" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult" computerName="DESKTOP-LJEH13I" duration="00:00:08.2596721" startTime="2025-08-19T15:56:15.5320379+08:00" endTime="2025-08-19T15:56:15.5320382+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3dfb45ce-9854-431f-9a21-90645ec69e9a">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 222&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="38b3aee7-74ef-4d82-8d1f-076a7c6d2673" testId="6b7ccf7b-5c3b-da27-afa5-fd982be6f28a" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_ShouldSendSignalRNotifications" computerName="DESKTOP-LJEH13I" duration="00:00:00.0149485" startTime="2025-08-19T15:56:39.8114361+08:00" endTime="2025-08-19T15:56:39.8114369+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="38b3aee7-74ef-4d82-8d1f-076a7c6d2673">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Drivers count must be positive (Parameter 'DriversCount')</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ValidateSeederOptions(SeederOptions options) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 124&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 74&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_ShouldSendSignalRNotifications() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 138&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="0f85331f-**************-e4e229330ae3" testId="449f32d4-b783-d2b5-4654-e385890cd0f3" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync" computerName="DESKTOP-LJEH13I" duration="00:00:00.0032198" startTime="2025-08-19T15:57:07.6797896+08:00" endTime="2025-08-19T15:57:07.6797899+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0f85331f-**************-e4e229330ae3">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 163&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="feab1e08-39f6-4fe3-a2ed-e488c8054e49" testId="fde2aa6b-0b94-e629-7ffa-8fa1ad3c545e" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_MediumLoad_ShouldCompleteWithinTimeLimit" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.3331380+08:00" endTime="2025-08-19T15:56:07.3331382+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="feab1e08-39f6-4fe3-a2ed-e488c8054e49">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="db83dc73-abbd-41cc-b394-65025185c56a" testId="7480e947-de55-64f0-069c-d70ba5bad2b7" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether" computerName="DESKTOP-LJEH13I" duration="00:00:35.9539229" startTime="2025-08-19T15:57:15.8355280+08:00" endTime="2025-08-19T15:57:15.8355283+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="db83dc73-abbd-41cc-b394-65025185c56a">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs:line 84&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="3eab7b32-dda3-470d-b769-c33c62202f94" testId="5c663689-c3c3-6f72-69ee-07aec4d44dc0" testName="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync" computerName="DESKTOP-LJEH13I" duration="00:00:00.0034956" startTime="2025-08-19T15:57:07.6971714+08:00" endTime="2025-08-19T15:57:07.6971715+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3eab7b32-dda3-470d-b769-c33c62202f94">
      <Output>
        <ErrorInfo>
          <Message>System.NullReferenceException : Object reference not set to an instance of an object.</Message>
          <StackTrace>   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 210&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 117&#xD;
   at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 49&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs:line 144&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="72597d1e-0b96-4e34-96ed-ddcbf8c9dc65" testId="b428c926-f38d-80e7-d0e8-5681496a3a05" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0031920" startTime="2025-08-19T15:56:07.4220524+08:00" endTime="2025-08-19T15:56:07.4220527+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="72597d1e-0b96-4e34-96ed-ddcbf8c9dc65">
      <Output>
        <ErrorInfo>
          <Message>Assert.Contains() Failure: Sub-string not found&#xD;
String:    ""&#xD;
Not found: "API connection failed"</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 203&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="7285be4d-580c-41ad-bc9f-4f864fa62704" testId="d38344ff-7f62-68f4-bba4-a4a0b75ff1ec" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation" computerName="DESKTOP-LJEH13I" duration="00:00:00.0017804" startTime="2025-08-19T15:57:35.1264241+08:00" endTime="2025-08-19T15:57:35.1264243+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7285be4d-580c-41ad-bc9f-4f864fa62704">
      <Output>
        <ErrorInfo>
          <Message>Assert.Throws() Failure: No exception was thrown&#xD;
Expected: typeof(System.OperationCanceledException)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 286&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="f6b94aae-d319-4c6c-953f-a470fce1dc95" testId="f341a477-60ec-48e2-7e46-d552c7a5ce8f" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteVehicleSeederAsync_WithValidOptions_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0247746" startTime="2025-08-19T15:56:07.3396772+08:00" endTime="2025-08-19T15:56:07.3396775+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f6b94aae-d319-4c6c-953f-a470fce1dc95" />
    <UnitTestResult executionId="a755425d-1741-4441-9d0f-ae4043f4c0e3" testId="bcf0e5cb-b5d8-7a36-c94f-22204481bf40" testName="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly" computerName="DESKTOP-LJEH13I" duration="00:00:24.8440283" startTime="2025-08-19T15:56:54.6326580+08:00" endTime="2025-08-19T15:56:54.6326582+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a755425d-1741-4441-9d0f-ae4043f4c0e3">
      <Output>
        <ErrorInfo>
          <Message>Assert.ThrowsAny() Failure: No exception was thrown&#xD;
Expected: typeof(System.Exception)</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs:line 323&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="72c00a1a-08e7-43a0-ad75-e80221e67a92" testId="edf6dc2b-88d3-885a-0482-3667044112b2" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteCardAccessSeederAsync_WithValidOptions_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0035237" startTime="2025-08-19T15:56:07.4039198+08:00" endTime="2025-08-19T15:56:07.4039200+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="72c00a1a-08e7-43a0-ad75-e80221e67a92" />
    <UnitTestResult executionId="ce3d4c7d-b59f-40c9-893b-1441f39d73e9" testId="df543b20-eeab-6dfa-cdc5-850d15136ee7" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithNullApiOrchestrationService_ShouldThrowArgumentNullException" computerName="DESKTOP-LJEH13I" duration="00:00:00.0009092" startTime="2025-08-19T15:56:07.3345398+08:00" endTime="2025-08-19T15:56:07.3345399+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ce3d4c7d-b59f-40c9-893b-1441f39d73e9" />
    <UnitTestResult executionId="b534de2f-2253-4fa3-9bd2-7a188e2551a0" testId="91e60e79-89e3-de36-c9a2-3639f5a9ace7" testName="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_SmallLoad_ShouldCompleteWithinTimeLimit" computerName="DESKTOP-LJEH13I" duration="00:00:00.0010000" startTime="2025-08-19T15:56:07.3340495+08:00" endTime="2025-08-19T15:56:07.3340496+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b534de2f-2253-4fa3-9bd2-7a188e2551a0">
      <Output>
        <ErrorInfo>
          <Message>System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).</Message>
          <StackTrace>   at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod) in /_/src/Moq/MethodCall.cs:line 396&#xD;
   at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback) in /_/src/Moq/MethodCall.cs:line 289&#xD;
   at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory) in /_/src/Moq/MethodCall.cs:line 259&#xD;
   at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression) in /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs:line 280&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 404&#xD;
   at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs:line 52&#xD;
   at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
    <UnitTestResult executionId="0b2fb4fd-e907-4719-9f1e-c27eb38c592c" testId="62c74643-25d8-c1e1-2327-3d88f05777f0" testName="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult" computerName="DESKTOP-LJEH13I" duration="00:00:00.0128688" startTime="2025-08-19T15:56:07.4375788+08:00" endTime="2025-08-19T15:56:07.4375791+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Failed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0b2fb4fd-e907-4719-9f1e-c27eb38c592c">
      <Output>
        <ErrorInfo>
          <Message>Assert.True() Failure&#xD;
Expected: True&#xD;
Actual:   False</Message>
          <StackTrace>   at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult() in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs:line 139&#xD;
--- End of stack trace from previous location ---</StackTrace>
        </ErrorInfo>
      </Output>
    </UnitTestResult>
  </Results>
  <TestDefinitions>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithNullLogger_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="76133b50-a240-76bc-1084-d0253cb510bd">
      <Execution id="bd57c5e8-50f5-454d-b2b0-cff87110cf1c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="Constructor_WithNullLogger_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="ba19c163-36db-83d4-322c-3599a9e8b8e3">
      <Execution id="58ba88f0-48c2-4dfa-a34a-227ce67298d7" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WithInvalidApi_ShouldReturnInvalidResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="9a52e156-ddcf-a899-8fad-0a1b967ffe71">
      <Execution id="6ce3c801-4054-42ac-b533-6cedae9af507" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ValidateMigrationPatternPrerequisitesAsync_WithInvalidApi_ShouldReturnInvalidResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WithCancellation_ShouldStopGracefully" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="59d33cdc-771a-4c8b-2dd4-691dd0e63ba9">
      <Execution id="c6535fba-2cb0-44de-a5e1-b672e500f675" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="BulkSeederService_WithCancellation_ShouldStopGracefully" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="7480e947-de55-64f0-069c-d70ba5bad2b7">
      <Execution id="db83dc73-abbd-41cc-b394-65025185c56a" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="da280ef2-fb3b-29de-978c-cdd0f6b142d9">
      <Execution id="fb9184f3-8785-4ecc-95c5-ba4687a4dab6" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_LargeLoad_ShouldNotExceedMemoryThreshold" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="fd69b7f9-bd08-ae50-6803-545ffe7a4a03">
      <Execution id="7c19e753-129c-4130-9ace-ae952dc5ec80" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="BulkSeederService_LargeLoad_ShouldNotExceedMemoryThreshold" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="a7960952-f3c0-0a2f-c41d-994ad12d7eea">
      <Execution id="ff512129-cd8d-4ffa-a0f7-e43d6233f1c8" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="ValidateStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="c0608df9-0d01-d669-4783-192fb8c7ade2">
      <Execution id="f1fb55a7-6da5-4223-9a93-345d23f1e918" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="ProcessStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="449f32d4-b783-d2b5-4654-e385890cd0f3">
      <Execution id="0f85331f-**************-e4e229330ae3" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_ConcurrentRequests_ShouldHandleCorrectly" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="bf85b4b1-f9cf-3a75-b2a9-b0df938dd17a">
      <Execution id="71d0618c-59f8-4bb3-8082-63a1273888ec" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="BulkSeederService_ConcurrentRequests_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="62c74643-25d8-c1e1-2327-3d88f05777f0">
      <Execution id="0b2fb4fd-e907-4719-9f1e-c27eb38c592c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="006ac670-2165-fcad-82a6-603152ba0046">
      <Execution id="4a724b2c-0dd5-44ea-99c5-d9b61308d86c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.ServiceProvider_ShouldResolveAllRequiredServices" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6619b0e6-03af-c16c-6adc-710a6e9813a1">
      <Execution id="1e6d086a-d2bc-4d10-84cc-04208b231dae" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="ServiceProvider_ShouldResolveAllRequiredServices" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenCancelled_ShouldThrowOperationCancelledException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="a299c426-06e4-7c23-192f-fcb680715a3b">
      <Execution id="d2819465-7abb-4df8-800d-5c3a477dad25" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WhenCancelled_ShouldThrowOperationCancelledException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithNullOptions_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="cf04b253-bce9-e0cf-7d8b-cef8b46e1913">
      <Execution id="b026381b-8f19-4542-b049-6d32972cd1a3" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="Constructor_WithNullOptions_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.EnvironmentConfigurationService_ShouldProvideCorrectConfiguration" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="bcbd6ceb-50cd-57b4-fff5-75c4520f6d90">
      <Execution id="e690c57c-8048-4e1e-a3f2-5aab89ff48a0" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="EnvironmentConfigurationService_ShouldProvideCorrectConfiguration" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithDisabledSteps_ShouldSkipThoseSteps" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="b8a95959-4bee-a7e9-6c11-3b07491d71d6">
      <Execution id="5a290f23-15ca-48a2-9660-2770de06b6a8" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteFullMigrationPatternAsync_WithDisabledSteps_ShouldSkipThoseSteps" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WithValidSetup_ShouldReturnValidResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="1d32c3e4-680b-3ee9-a18a-7d4863c6bc67">
      <Execution id="b51ecbd0-2246-43a1-9fec-6885551807de" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ValidateMigrationPatternPrerequisitesAsync_WithValidSetup_ShouldReturnValidResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithValidParameters_ShouldCreateInstance" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="cca9f9ca-c236-de25-010f-3c4126cf6217">
      <Execution id="c697f00d-00e9-4f6a-a052-b1a1b0808025" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="Constructor_WithValidParameters_ShouldCreateInstance" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithValidParameters_ShouldCreateInstance" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="f1a49d28-8732-54db-270c-0cff0464b0d7">
      <Execution id="f7562514-d423-495d-b287-456c04ae3c30" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="Constructor_WithValidParameters_ShouldCreateInstance" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithNullComplexEntityService_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="90c69325-cd22-6f48-bbac-02bf30c3cd68">
      <Execution id="69532133-e3e9-4c61-90b6-a0c3774689fa" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="Constructor_WithNullComplexEntityService_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="0c95a68f-eb5c-e5ee-d93c-99aa0f66aab5">
      <Execution id="24441366-e2d5-496f-8527-e85e056c0a12" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_MediumLoad_ShouldCompleteWithinTimeLimit" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="fde2aa6b-0b94-e629-7ffa-8fa1ad3c545e">
      <Execution id="feab1e08-39f6-4fe3-a2ed-e488c8054e49" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="BulkSeederService_MediumLoad_ShouldCompleteWithinTimeLimit" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="9f223ca4-b1f1-e7d9-d489-65c2d75bdd12">
      <Execution id="d2ebe025-84e8-47cb-ad4e-9a019129f856" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithNullCounts_ShouldReturnSuccessWithoutGeneration" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="3c1a1e97-992a-fa7c-e475-75a6271f32ca">
      <Execution id="55a8479f-e9e3-47a6-ae63-f869fbc7b45b" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithNullCounts_ShouldReturnSuccessWithoutGeneration" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithNullSqlDataService_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="cd262b36-9e57-616e-0f2b-f96ca8170c7b">
      <Execution id="fa91c93c-d8d6-4261-aba8-ba98691b972e" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="Constructor_WithNullSqlDataService_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="0bf4ce2a-e887-fcb9-feb7-9cbdcf1a6254">
      <Execution id="72c43326-4211-4261-a5da-722f293bb0f8" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteVehicleSeederAsync_WithZeroVehiclesCount_ShouldReturnSuccessWithoutCreation" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="5707ecc5-fffb-c2e4-1fb3-bf1ec0e6fb0f">
      <Execution id="2bef8343-0112-4ce7-9f72-e305bee2c60c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteVehicleSeederAsync_WithZeroVehiclesCount_ShouldReturnSuccessWithoutCreation" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithNullOptions_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="00ab8bff-d948-51f4-df1c-8dba87f291b1">
      <Execution id="d112b053-d30e-43c1-9467-5fa399bc6f54" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="Constructor_WithNullOptions_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="75029862-8fda-8053-867f-d148a3662fb9">
      <Execution id="596312b1-0ee5-4aca-a80e-6a9021a3725c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="1eb5be61-77d5-a6a7-67a9-a10af71763a7">
      <Execution id="804ccfd1-acaf-44c2-b8f3-37b556aaaf21" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="b428c926-f38d-80e7-d0e8-5681496a3a05">
      <Execution id="72597d1e-0b96-4e34-96ed-ddcbf8c9dc65" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteCardAccessSeederAsync_WithValidOptions_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="edf6dc2b-88d3-885a-0482-3667044112b2">
      <Execution id="72c00a1a-08e7-43a0-ad75-e80221e67a92" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteCardAccessSeederAsync_WithValidOptions_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithNullEnvironmentService_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="73082ec6-b33c-3ff2-242f-48536536d825">
      <Execution id="f90742f9-90a5-4b8f-b440-759f15060578" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="Constructor_WithNullEnvironmentService_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="90a2fd0f-f60f-b4fe-70e7-623354cc4fa3">
      <Execution id="683b5fe1-f779-4463-a293-ae79c1982d52" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_ShouldSendSignalRNotifications" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6b7ccf7b-5c3b-da27-afa5-fd982be6f28a">
      <Execution id="38b3aee7-74ef-4d82-8d1f-076a7c6d2673" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="BulkSeederService_ShouldSendSignalRNotifications" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="3e484da7-2b24-cba3-38e5-1a6054f7a210">
      <Execution id="8602d428-90a9-4173-808a-fee2c2884414" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="9da640db-aeb4-1572-a340-2fd028e77f46">
      <Execution id="7f9e1e54-549d-426f-94c9-0fc069d325f8" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithNullApiOrchestrationService_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="df543b20-eeab-6dfa-cdc5-850d15136ee7">
      <Execution id="ce3d4c7d-b59f-40c9-893b-1441f39d73e9" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="Constructor_WithNullApiOrchestrationService_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="befbebd1-3fbb-92cf-b910-9c503e86bf33">
      <Execution id="3dfb45ce-9854-431f-9a21-90645ec69e9a" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.Constructor_WithNullLogger_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="e92355f6-7aa2-92f9-f5f5-2a14225fa627">
      <Execution id="e4f57106-9558-49b9-b0cb-b88888d88733" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="Constructor_WithNullLogger_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteVehicleSeederAsync_WithValidOptions_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="f341a477-60ec-48e2-7e46-d552c7a5ce8f">
      <Execution id="f6b94aae-d319-4c6c-953f-a470fce1dc95" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteVehicleSeederAsync_WithValidOptions_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="c1817665-cceb-4543-a94b-53771d135741">
      <Execution id="cd417418-9c85-45f2-93cd-03eb0a1ea3fd" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="01de2f0c-4dbb-c0ce-1c6f-805fa3e3511b">
      <Execution id="7946ea53-981c-4e6e-91cb-7255ad205520" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.Constructor_WithValidParameters_ShouldCreateInstance" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="bb3baddf-798b-d8f3-50f9-1974bb37a637">
      <Execution id="e8fa3852-5368-4679-a15b-3411252684a2" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="Constructor_WithValidParameters_ShouldCreateInstance" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6d3b8ec7-609b-a343-2e1d-8c74a43386b9">
      <Execution id="db3ea9ae-8d0c-49c5-acd0-a20ac79770f0" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="bcf0e5cb-b5d8-7a36-c94f-22204481bf40">
      <Execution id="a755425d-1741-4441-9d0f-ae4043f4c0e3" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="b76a5fb1-5673-1d33-0f17-2647871784f4">
      <Execution id="be027ca7-3511-4280-8a5f-224fdb8d104f" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.Services_ShouldUseCorrectConfiguration" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="9c54f0d5-b9d8-56fb-45bb-d91a6dfc2e1d">
      <Execution id="1ec10977-ea90-4516-baac-e7c9762c098c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="Services_ShouldUseCorrectConfiguration" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_ShouldSendSignalRNotifications" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="444edcb7-ef42-2182-5358-a8500aa52c94">
      <Execution id="b7466448-52f0-433e-8848-122f7d6273b9" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="MigrationPatternSeederService_ShouldSendSignalRNotifications" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_RepeatedOperations_ShouldMaintainPerformance" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="03c22203-bbdb-5426-17b8-de4d477d6416">
      <Execution id="790bbd42-e85b-4d37-a619-7d6394b626be" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="BulkSeederService_RepeatedOperations_ShouldMaintainPerformance" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="1d9890c2-c4b6-1d38-4853-a0af1c67d765">
      <Execution id="0cb53361-bf9c-440c-83ef-d7ca1400d19c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_SmallLoad_ShouldCompleteWithinTimeLimit" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="91e60e79-89e3-de36-c9a2-3639f5a9ace7">
      <Execution id="b534de2f-2253-4fa3-9bd2-7a188e2551a0" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="BulkSeederService_SmallLoad_ShouldCompleteWithinTimeLimit" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteVehicleSeederAsync_WithVehiclesCount_ShouldCallComplexEntityService" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="0292f93a-fcbd-9803-4f38-6fd7cd563804">
      <Execution id="904a2e5c-8fbd-4cd1-bad4-2bd69436516d" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteVehicleSeederAsync_WithVehiclesCount_ShouldCallComplexEntityService" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="58069d41-1e47-e610-9c8a-f765fc1baab1">
      <Execution id="2e64af49-7878-4724-aff0-333025c3151c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="d38344ff-7f62-68f4-bba4-a4a0b75ff1ec">
      <Execution id="7285be4d-580c-41ad-bc9f-4f864fa62704" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_RepeatedValidations_ShouldMaintainPerformance" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="9f2f3e4e-bb0c-df30-bfb6-cd84bf546c86">
      <Execution id="a68bae05-8904-451e-b8aa-a694879fafa7" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="MigrationPatternSeederService_RepeatedValidations_ShouldMaintainPerformance" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithZeroCounts_ShouldReturnSuccessWithoutGeneration" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="17c90c4b-9c96-54e6-4777-88b8d8560cdc">
      <Execution id="04adbb24-4d7e-4280-aee1-109c36872851" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithZeroCounts_ShouldReturnSuccessWithoutGeneration" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteCardAccessSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutCreation" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="5b5f0260-c999-9d44-4255-86ed147cd1a4">
      <Execution id="66aea12f-c2ce-455f-a9cd-a4e3a24ac817" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteCardAccessSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutCreation" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="5c663689-c3c3-6f72-69ee-07aec4d44dc0">
      <Execution id="3eab7b32-dda3-470d-b769-c33c62202f94" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteCardAccessSeederAsync_WithDriversCount_ShouldCallComplexEntityService" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="5c955ef4-15b8-7fbe-326e-a00b2edc2985">
      <Execution id="9ec657e2-1273-4021-82ec-faed71c55a97" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteCardAccessSeederAsync_WithDriversCount_ShouldCallComplexEntityService" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="4a47ff44-57fa-c61a-115a-822a69921b85">
      <Execution id="7440b95a-4408-4da7-8818-a20cb3d476e1" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6fe8b7ec-1d17-d467-bb0f-52ebf3965f7c">
      <Execution id="43863307-3960-45ec-ade3-ee77a88dda02" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateVehicleDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_ShouldSendProgressNotifications" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="dbc0ea9c-4730-fff9-f4e6-920d04da1cab">
      <Execution id="6913a223-db7f-488f-afc0-04d50192344c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_ShouldSendProgressNotifications" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_ConcurrentApiCalls_ShouldHandleCorrectly" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="f9c9fcf0-5ef5-b76e-0f16-da18767ae088">
      <Execution id="b8882ede-945c-4502-af72-ee98bb37b9a2" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="MigrationPatternSeederService_ConcurrentApiCalls_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="c68dbc4c-a4a9-1930-4389-1b67c8381c5d">
      <Execution id="c4504d01-ffac-4701-a9ce-675b90a88f7c" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateDriverDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_LargeLoad_ShouldNotExceedMemoryThreshold" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="aea2325a-5eda-6357-aea9-11524413e94a">
      <Execution id="f3763080-747c-4939-a6da-9f12c87d5531" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="MigrationPatternSeederService_LargeLoad_ShouldNotExceedMemoryThreshold" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.ServiceProvider_ShouldCreateSingletonServices" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="dba81b2c-ec26-a155-9a9e-9086ac66e31d">
      <Execution id="0e973a81-28dc-4e41-b30b-5c72b9971d6a" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="ServiceProvider_ShouldCreateSingletonServices" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="d8967d53-9423-6c4b-bc12-c3d200055288">
      <Execution id="0b5ab02a-f870-475c-95f4-60d6c503e2e5" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6a2a6ed1-807d-4ade-6e78-24c6ba29ce23">
      <Execution id="c4e2c15b-a6ad-4fe6-a584-ac489cf8b633" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6689876c-74fe-b212-fe96-a34eec5f0b56">
      <Execution id="08c7f665-e3e8-43c6-adca-e490971f53ef" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests" name="GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_ApiCalls_ShouldRespectRateLimit" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="6b2931ea-6abf-5e24-6915-f9ef4a3ec54c">
      <Execution id="c6fbc537-9725-4511-b5f0-645151a50660" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests" name="MigrationPatternSeederService_ApiCalls_ShouldRespectRateLimit" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="59283c7c-ad10-179e-17c7-88f1143fa25f">
      <Execution id="2222a1f5-e700-46bf-be35-73ec61131759" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WithCancellation_ShouldStopGracefully" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="2b48b1d9-2a70-af56-1900-37baa9747c9e">
      <Execution id="1bfa5ccc-6f05-4a35-b081-cc39a380ce1b" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests" name="MigrationPatternSeederService_WithCancellation_ShouldStopGracefully" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.Constructor_WithNullLogger_ShouldThrowArgumentNullException" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="470d0e63-4256-53f3-23d2-acbde8f9cbef">
      <Execution id="35cee18d-cc65-401d-99f8-d50b72ecd0ae" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests" name="Constructor_WithNullLogger_ShouldThrowArgumentNullException" />
    </UnitTest>
    <UnitTest name="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall" storage="d:\codez\workz\xq360datamigration\xq360.datamigration.dataseeder.tests\bin\debug\net9.0\xq360.datamigration.dataseeder.tests.dll" id="78b99b4e-f2e7-b487-890f-7d96ffdc2d03">
      <Execution id="c712aeaa-7767-4f74-9c25-dbb59f2b1cb8" />
      <TestMethod codeBase="D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\bin\Debug\net9.0\XQ360.DataMigration.DataSeeder.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests" name="ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="cca9f9ca-c236-de25-010f-3c4126cf6217" executionId="c697f00d-00e9-4f6a-a052-b1a1b0808025" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b8a95959-4bee-a7e9-6c11-3b07491d71d6" executionId="5a290f23-15ca-48a2-9660-2770de06b6a8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9c54f0d5-b9d8-56fb-45bb-d91a6dfc2e1d" executionId="1ec10977-ea90-4516-baac-e7c9762c098c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dba81b2c-ec26-a155-9a9e-9086ac66e31d" executionId="0e973a81-28dc-4e41-b30b-5c72b9971d6a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="73082ec6-b33c-3ff2-242f-48536536d825" executionId="f90742f9-90a5-4b8f-b440-759f15060578" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3e484da7-2b24-cba3-38e5-1a6054f7a210" executionId="8602d428-90a9-4173-808a-fee2c2884414" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6689876c-74fe-b212-fe96-a34eec5f0b56" executionId="08c7f665-e3e8-43c6-adca-e490971f53ef" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9f2f3e4e-bb0c-df30-bfb6-cd84bf546c86" executionId="a68bae05-8904-451e-b8aa-a694879fafa7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cf04b253-bce9-e0cf-7d8b-cef8b46e1913" executionId="b026381b-8f19-4542-b049-6d32972cd1a3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bcbd6ceb-50cd-57b4-fff5-75c4520f6d90" executionId="e690c57c-8048-4e1e-a3f2-5aab89ff48a0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9a52e156-ddcf-a899-8fad-0a1b967ffe71" executionId="6ce3c801-4054-42ac-b533-6cedae9af507" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1d9890c2-c4b6-1d38-4853-a0af1c67d765" executionId="0cb53361-bf9c-440c-83ef-d7ca1400d19c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6a2a6ed1-807d-4ade-6e78-24c6ba29ce23" executionId="c4e2c15b-a6ad-4fe6-a584-ac489cf8b633" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bb3baddf-798b-d8f3-50f9-1974bb37a637" executionId="e8fa3852-5368-4679-a15b-3411252684a2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f1a49d28-8732-54db-270c-0cff0464b0d7" executionId="f7562514-d423-495d-b287-456c04ae3c30" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="59283c7c-ad10-179e-17c7-88f1143fa25f" executionId="2222a1f5-e700-46bf-be35-73ec61131759" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="78b99b4e-f2e7-b487-890f-7d96ffdc2d03" executionId="c712aeaa-7767-4f74-9c25-dbb59f2b1cb8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0bf4ce2a-e887-fcb9-feb7-9cbdcf1a6254" executionId="72c43326-4211-4261-a5da-722f293bb0f8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0c95a68f-eb5c-e5ee-d93c-99aa0f66aab5" executionId="24441366-e2d5-496f-8527-e85e056c0a12" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cd262b36-9e57-616e-0f2b-f96ca8170c7b" executionId="fa91c93c-d8d6-4261-aba8-ba98691b972e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5b5f0260-c999-9d44-4255-86ed147cd1a4" executionId="66aea12f-c2ce-455f-a9cd-a4e3a24ac817" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="17c90c4b-9c96-54e6-4777-88b8d8560cdc" executionId="04adbb24-4d7e-4280-aee1-109c36872851" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e92355f6-7aa2-92f9-f5f5-2a14225fa627" executionId="e4f57106-9558-49b9-b0cb-b88888d88733" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f9c9fcf0-5ef5-b76e-0f16-da18767ae088" executionId="b8882ede-945c-4502-af72-ee98bb37b9a2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03c22203-bbdb-5426-17b8-de4d477d6416" executionId="790bbd42-e85b-4d37-a619-7d6394b626be" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="90c69325-cd22-6f48-bbac-02bf30c3cd68" executionId="69532133-e3e9-4c61-90b6-a0c3774689fa" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="470d0e63-4256-53f3-23d2-acbde8f9cbef" executionId="35cee18d-cc65-401d-99f8-d50b72ecd0ae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6b2931ea-6abf-5e24-6915-f9ef4a3ec54c" executionId="c6fbc537-9725-4511-b5f0-645151a50660" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="76133b50-a240-76bc-1084-d0253cb510bd" executionId="bd57c5e8-50f5-454d-b2b0-cff87110cf1c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="59d33cdc-771a-4c8b-2dd4-691dd0e63ba9" executionId="c6535fba-2cb0-44de-a5e1-b672e500f675" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="58069d41-1e47-e610-9c8a-f765fc1baab1" executionId="2e64af49-7878-4724-aff0-333025c3151c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c0608df9-0d01-d669-4783-192fb8c7ade2" executionId="f1fb55a7-6da5-4223-9a93-345d23f1e918" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4a47ff44-57fa-c61a-115a-822a69921b85" executionId="7440b95a-4408-4da7-8818-a20cb3d476e1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d8967d53-9423-6c4b-bc12-c3d200055288" executionId="0b5ab02a-f870-475c-95f4-60d6c503e2e5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="aea2325a-5eda-6357-aea9-11524413e94a" executionId="f3763080-747c-4939-a6da-9f12c87d5531" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dbc0ea9c-4730-fff9-f4e6-920d04da1cab" executionId="6913a223-db7f-488f-afc0-04d50192344c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="00ab8bff-d948-51f4-df1c-8dba87f291b1" executionId="d112b053-d30e-43c1-9467-5fa399bc6f54" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1eb5be61-77d5-a6a7-67a9-a10af71763a7" executionId="804ccfd1-acaf-44c2-b8f3-37b556aaaf21" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3c1a1e97-992a-fa7c-e475-75a6271f32ca" executionId="55a8479f-e9e3-47a6-ae63-f869fbc7b45b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="da280ef2-fb3b-29de-978c-cdd0f6b142d9" executionId="fb9184f3-8785-4ecc-95c5-ba4687a4dab6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bf85b4b1-f9cf-3a75-b2a9-b0df938dd17a" executionId="71d0618c-59f8-4bb3-8082-63a1273888ec" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="90a2fd0f-f60f-b4fe-70e7-623354cc4fa3" executionId="683b5fe1-f779-4463-a293-ae79c1982d52" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6d3b8ec7-609b-a343-2e1d-8c74a43386b9" executionId="db3ea9ae-8d0c-49c5-acd0-a20ac79770f0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a7960952-f3c0-0a2f-c41d-994ad12d7eea" executionId="ff512129-cd8d-4ffa-a0f7-e43d6233f1c8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="75029862-8fda-8053-867f-d148a3662fb9" executionId="596312b1-0ee5-4aca-a80e-6a9021a3725c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c68dbc4c-a4a9-1930-4389-1b67c8381c5d" executionId="c4504d01-ffac-4701-a9ce-675b90a88f7c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9da640db-aeb4-1572-a340-2fd028e77f46" executionId="7f9e1e54-549d-426f-94c9-0fc069d325f8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="01de2f0c-4dbb-c0ce-1c6f-805fa3e3511b" executionId="7946ea53-981c-4e6e-91cb-7255ad205520" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c1817665-cceb-4543-a94b-53771d135741" executionId="cd417418-9c85-45f2-93cd-03eb0a1ea3fd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ba19c163-36db-83d4-322c-3599a9e8b8e3" executionId="58ba88f0-48c2-4dfa-a34a-227ce67298d7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fd69b7f9-bd08-ae50-6803-545ffe7a4a03" executionId="7c19e753-129c-4130-9ace-ae952dc5ec80" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="006ac670-2165-fcad-82a6-603152ba0046" executionId="4a724b2c-0dd5-44ea-99c5-d9b61308d86c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6fe8b7ec-1d17-d467-bb0f-52ebf3965f7c" executionId="43863307-3960-45ec-ade3-ee77a88dda02" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1d32c3e4-680b-3ee9-a18a-7d4863c6bc67" executionId="b51ecbd0-2246-43a1-9fec-6885551807de" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b48b1d9-2a70-af56-1900-37baa9747c9e" executionId="1bfa5ccc-6f05-4a35-b081-cc39a380ce1b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5707ecc5-fffb-c2e4-1fb3-bf1ec0e6fb0f" executionId="2bef8343-0112-4ce7-9f72-e305bee2c60c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9f223ca4-b1f1-e7d9-d489-65c2d75bdd12" executionId="d2ebe025-84e8-47cb-ad4e-9a019129f856" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="444edcb7-ef42-2182-5358-a8500aa52c94" executionId="b7466448-52f0-433e-8848-122f7d6273b9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6619b0e6-03af-c16c-6adc-710a6e9813a1" executionId="1e6d086a-d2bc-4d10-84cc-04208b231dae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0292f93a-fcbd-9803-4f38-6fd7cd563804" executionId="904a2e5c-8fbd-4cd1-bad4-2bd69436516d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a299c426-06e4-7c23-192f-fcb680715a3b" executionId="d2819465-7abb-4df8-800d-5c3a477dad25" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5c955ef4-15b8-7fbe-326e-a00b2edc2985" executionId="9ec657e2-1273-4021-82ec-faed71c55a97" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b76a5fb1-5673-1d33-0f17-2647871784f4" executionId="be027ca7-3511-4280-8a5f-224fdb8d104f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="befbebd1-3fbb-92cf-b910-9c503e86bf33" executionId="3dfb45ce-9854-431f-9a21-90645ec69e9a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6b7ccf7b-5c3b-da27-afa5-fd982be6f28a" executionId="38b3aee7-74ef-4d82-8d1f-076a7c6d2673" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="449f32d4-b783-d2b5-4654-e385890cd0f3" executionId="0f85331f-**************-e4e229330ae3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fde2aa6b-0b94-e629-7ffa-8fa1ad3c545e" executionId="feab1e08-39f6-4fe3-a2ed-e488c8054e49" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7480e947-de55-64f0-069c-d70ba5bad2b7" executionId="db83dc73-abbd-41cc-b394-65025185c56a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5c663689-c3c3-6f72-69ee-07aec4d44dc0" executionId="3eab7b32-dda3-470d-b769-c33c62202f94" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b428c926-f38d-80e7-d0e8-5681496a3a05" executionId="72597d1e-0b96-4e34-96ed-ddcbf8c9dc65" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d38344ff-7f62-68f4-bba4-a4a0b75ff1ec" executionId="7285be4d-580c-41ad-bc9f-4f864fa62704" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f341a477-60ec-48e2-7e46-d552c7a5ce8f" executionId="f6b94aae-d319-4c6c-953f-a470fce1dc95" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bcf0e5cb-b5d8-7a36-c94f-22204481bf40" executionId="a755425d-1741-4441-9d0f-ae4043f4c0e3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="edf6dc2b-88d3-885a-0482-3667044112b2" executionId="72c00a1a-08e7-43a0-ad75-e80221e67a92" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="df543b20-eeab-6dfa-cdc5-850d15136ee7" executionId="ce3d4c7d-b59f-40c9-893b-1441f39d73e9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="91e60e79-89e3-de36-c9a2-3639f5a9ace7" executionId="b534de2f-2253-4fa3-9bd2-7a188e2551a0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="62c74643-25d8-c1e1-2327-3d88f05777f0" executionId="0b2fb4fd-e907-4719-9f1e-c27eb38c592c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="Results Not in a List" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="All Loaded Results" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Failed">
    <Counters total="77" executed="77" passed="28" failed="49" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.8.2+699d445a1a (64-bit .NET 9.0.7)&#xD;
[xUnit.net 00:00:00.16]   Discovering: XQ360.DataMigration.DataSeeder.Tests&#xD;
[xUnit.net 00:00:00.22]   Discovered:  XQ360.DataMigration.DataSeeder.Tests&#xD;
[xUnit.net 00:00:00.23]   Starting:    XQ360.DataMigration.DataSeeder.Tests&#xD;
[xUnit.net 00:00:00.62]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.62]       Stack Trace:&#xD;
[xUnit.net 00:00:00.62]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.62]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.62]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.62]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.62]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.62]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.62]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.63]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.63]       Stack Trace:&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.63]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.63]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.63]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.63]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.63]       Stack Trace:&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.63]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.63]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.63]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.63]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.63]       Stack Trace:&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.63]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.63]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.63]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.63]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.64]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.64]       Stack Trace:&#xD;
[xUnit.net 00:00:00.64]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.64]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.64]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.64]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.64]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.64]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.64]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.72]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.72]       Stack Trace:&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.72]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.72]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.72]       Stack Trace:&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.72]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.72]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.72]       Stack Trace:&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.72]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.72]       System.ArgumentException : Invalid callback. Setup on method with 0 parameter(s) cannot invoke callback with different number of parameters (1).&#xD;
[xUnit.net 00:00:00.72]       Stack Trace:&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(396,0): at Moq.MethodCall.ValidateNumberOfCallbackParameters(Delegate callback, MethodInfo callbackMethod)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(289,0): at Moq.MethodCall.&lt;&gt;c__DisplayClass23_0.&lt;SetReturnComputedValueBehavior&gt;g__ValidateCallback|4(Delegate callback)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/MethodCall.cs(259,0): at Moq.MethodCall.SetReturnComputedValueBehavior(Delegate valueFactory)&#xD;
[xUnit.net 00:00:00.72]         /_/src/Moq/Language/Flow/NonVoidSetupPhrase.cs(280,0): at Moq.Language.Flow.NonVoidSetupPhrase`2.Returns[T1](Func`2 valueExpression)&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(404,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.SetupMockServices()&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederPerformanceTests.cs(52,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests..ctor()&#xD;
[xUnit.net 00:00:00.72]            at System.RuntimeType.CreateInstanceDefaultCtor(Boolean publicOnly, Boolean wrapExceptions)&#xD;
[xUnit.net 00:00:00.72]       Assert.Contains() Failure: Item not found in collection&#xD;
[xUnit.net 00:00:00.72]       Collection: ["Person/Driver creation failed - stopping migration"···]&#xD;
[xUnit.net 00:00:00.72]       Not found:  "Person/Driver creation failed"&#xD;
[xUnit.net 00:00:00.72]       Stack Trace:&#xD;
[xUnit.net 00:00:00.72]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(334,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure()&#xD;
[xUnit.net 00:00:00.72]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:00.76]       Assert.Contains() Failure: Item not found in collection&#xD;
[xUnit.net 00:00:00.76]       Collection: ["Validation failed: API validation failed"]&#xD;
[xUnit.net 00:00:00.76]       Not found:  "API validation failed"&#xD;
[xUnit.net 00:00:00.76]       Stack Trace:&#xD;
[xUnit.net 00:00:00.76]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(415,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult()&#xD;
[xUnit.net 00:00:00.76]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:00.81]       Assert.Contains() Failure: Sub-string not found&#xD;
[xUnit.net 00:00:00.81]       String:    ""&#xD;
[xUnit.net 00:00:00.81]       Not found: "API connection failed"&#xD;
[xUnit.net 00:00:00.81]       Stack Trace:&#xD;
[xUnit.net 00:00:00.81]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(203,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult()&#xD;
[xUnit.net 00:00:00.81]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:00.83]       Assert.True() Failure&#xD;
[xUnit.net 00:00:00.83]       Expected: True&#xD;
[xUnit.net 00:00:00.83]       Actual:   False&#xD;
[xUnit.net 00:00:00.83]       Stack Trace:&#xD;
[xUnit.net 00:00:00.83]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(139,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult()&#xD;
[xUnit.net 00:00:00.83]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Starting bulk seeding operation 01f88f4e&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Seeding options: Drivers=1000, Vehicles=5, BatchSize=100, DryRun=True, Generate=True&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      DRY RUN MODE - No data will be modified&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Created seeding session f32ddcb6-6177-408b-9bdc-63ebc80e2e04 with name 'BulkSeeder_01f88f4e' in environment Development&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generating 1000 driver records for session f32ddcb6-6177-408b-9bdc-63ebc80e2e04&#xD;
[xUnit.net 00:00:00.86]       Assert.True() Failure&#xD;
[xUnit.net 00:00:00.86]       Expected: True&#xD;
[xUnit.net 00:00:00.86]       Actual:   False&#xD;
[xUnit.net 00:00:00.86]       Stack Trace:&#xD;
[xUnit.net 00:00:00.86]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(434,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall()&#xD;
[xUnit.net 00:00:00.86]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:00.90]       Moq.MockException : &#xD;
[xUnit.net 00:00:00.90]       Expected invocation on the mock once, but was 0 times: x =&gt; x.CreatePersonDriverBatchAsync(It.Is&lt;IEnumerable&lt;PersonCreateRequest&gt;&gt;(requests =&gt; requests.Count&lt;PersonCreateRequest&gt;() == 25), It.IsAny&lt;int&gt;(), It.IsAny&lt;CancellationToken&gt;())&#xD;
[xUnit.net 00:00:00.90]       &#xD;
[xUnit.net 00:00:00.90]       Performed invocations:&#xD;
[xUnit.net 00:00:00.90]       &#xD;
[xUnit.net 00:00:00.90]          Mock&lt;IApiOrchestrationService:30&gt; (x):&#xD;
[xUnit.net 00:00:00.90]       &#xD;
[xUnit.net 00:00:00.90]             IApiOrchestrationService.ValidateApiConnectivityAsync()&#xD;
[xUnit.net 00:00:00.90]       &#xD;
[xUnit.net 00:00:00.90]       Stack Trace:&#xD;
[xUnit.net 00:00:00.90]         /_/src/Moq/Mock.cs(332,0): at Moq.Mock.Verify(Mock mock, LambdaExpression expression, Times times, String failMessage)&#xD;
[xUnit.net 00:00:00.90]         /_/src/Moq/Mock`1.cs(840,0): at Moq.Mock`1.Verify[TResult](Expression`1 expression, Func`1 times)&#xD;
[xUnit.net 00:00:00.90]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(158,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration()&#xD;
[xUnit.net 00:00:00.90]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:00.91]       Assert.True() Failure&#xD;
[xUnit.net 00:00:00.91]       Expected: True&#xD;
[xUnit.net 00:00:00.91]       Actual:   False&#xD;
[xUnit.net 00:00:00.91]       Stack Trace:&#xD;
[xUnit.net 00:00:00.91]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(310,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult()&#xD;
[xUnit.net 00:00:00.91]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:00.92]       Assert.True() Failure&#xD;
[xUnit.net 00:00:00.92]       Expected: True&#xD;
[xUnit.net 00:00:00.92]       Actual:   False&#xD;
[xUnit.net 00:00:00.92]       Stack Trace:&#xD;
[xUnit.net 00:00:00.92]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\MigrationPatternSeederServiceTests.cs(176,0): at XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall()&#xD;
[xUnit.net 00:00:00.92]         --- End of stack trace from previous location ---&#xD;
fail: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Driver data generation failed for session f32ddcb6-6177-408b-9bdc-63ebc80e2e04&#xD;
      System.Threading.Tasks.TaskCanceledException: A task was canceled.&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService.GenerateDriverDataAsync(Guid sessionId, Int32 count, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\SqlDataGenerationService.cs:line 62&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generating 5 vehicle records for session f32ddcb6-6177-408b-9bdc-63ebc80e2e04&#xD;
fail: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Vehicle data generation failed for session f32ddcb6-6177-408b-9bdc-63ebc80e2e04&#xD;
      System.Threading.Tasks.TaskCanceledException: A task was canceled.&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService.GenerateVehicleDataAsync(Guid sessionId, Int32 count, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\SqlDataGenerationService.cs:line 124&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
fail: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Validation failed for session f32ddcb6-6177-408b-9bdc-63ebc80e2e04&#xD;
      System.Threading.Tasks.TaskCanceledException: A task was canceled.&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService.ValidateStagedDataAsync(Guid sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\SqlDataGenerationService.cs:line 177&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Updated seeding session f32ddcb6-6177-408b-9bdc-63ebc80e2e04 status to Failed. Total: 0, Successful: 0, Failed: 0&#xD;
fail: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Bulk seeding failed 01f88f4e&#xD;
      System.InvalidOperationException: Data validation failed: A task was canceled.&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 213&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 254&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 91&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 01f88f4e&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
[xUnit.net 00:00:01.05]       Assert.Throws() Failure: Exception type was not an exact match&#xD;
[xUnit.net 00:00:01.05]       Expected: typeof(System.OperationCanceledException)&#xD;
[xUnit.net 00:00:01.05]       Actual:   typeof(System.InvalidOperationException)&#xD;
[xUnit.net 00:00:01.05]       ---- System.InvalidOperationException : Data validation failed: A task was canceled.&#xD;
[xUnit.net 00:00:01.05]       Stack Trace:&#xD;
[xUnit.net 00:00:01.05]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(263,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WithCancellation_ShouldStopGracefully()&#xD;
[xUnit.net 00:00:01.05]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:01.05]         ----- Inner Stack Trace -----&#xD;
[xUnit.net 00:00:01.05]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(213,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:01.05]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:01.05]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:01.06]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:01.06]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Starting bulk seeding operation ffcf3f6b&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Seeding options: Drivers=1, Vehicles=5, BatchSize=100, DryRun=True, Generate=True&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      DRY RUN MODE - No data will be modified&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Created seeding session a7238cba-e18a-44d5-a2d9-5b212cfd4543 with name 'BulkSeeder_ffcf3f6b' in environment Development&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generating 1 driver records for session a7238cba-e18a-44d5-a2d9-5b212cfd4543&#xD;
[xUnit.net 00:00:08.92]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:00:08.92]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:00:08.92]       Stack Trace:&#xD;
[xUnit.net 00:00:08.92]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(222,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult()&#xD;
[xUnit.net 00:00:08.92]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generated 1/1 driver records&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Driver data generation completed for session a7238cba-e18a-44d5-a2d9-5b212cfd4543. Generated 1 records in 00:00:14.4851421&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generating 5 vehicle records for session a7238cba-e18a-44d5-a2d9-5b212cfd4543&#xD;
[xUnit.net 00:00:16.04]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:00:16.04]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:00:16.04]       Stack Trace:&#xD;
[xUnit.net 00:00:16.04]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(256,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult()&#xD;
[xUnit.net 00:00:16.04]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:16.05]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:00:16.05]       Expected: typeof(System.OperationCanceledException)&#xD;
[xUnit.net 00:00:16.05]       Stack Trace:&#xD;
[xUnit.net 00:00:16.05]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(152,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation()&#xD;
[xUnit.net 00:00:16.05]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generated 5/5 vehicle records&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Vehicle data generation completed for session a7238cba-e18a-44d5-a2d9-5b212cfd4543. Generated 5 records in 00:00:01.3765841&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
[xUnit.net 00:00:21.92]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:00:21.92]       Expected: typeof(System.ArgumentException)&#xD;
[xUnit.net 00:00:21.92]       Stack Trace:&#xD;
[xUnit.net 00:00:21.92]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(238,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException()&#xD;
[xUnit.net 00:00:21.92]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:23.17]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:00:23.17]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:00:23.17]       Stack Trace:&#xD;
[xUnit.net 00:00:23.17]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(170,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult()&#xD;
[xUnit.net 00:00:23.17]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:30.98]       System.InvalidOperationException : Database connection failed&#xD;
[xUnit.net 00:00:30.98]       Stack Trace:&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(171,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext()&#xD;
[xUnit.net 00:00:30.98]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:30.98]            at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(171,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext()&#xD;
[xUnit.net 00:00:30.98]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:30.98]            at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(171,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext()&#xD;
[xUnit.net 00:00:30.98]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:30.98]            at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(171,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.&lt;&gt;c__DisplayClass12_0.&lt;&lt;ExecuteSqlSeederAsync&gt;b__0&gt;d.MoveNext()&#xD;
[xUnit.net 00:00:30.98]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:30.98]            at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)&#xD;
[xUnit.net 00:00:30.98]            at Polly.AsyncPolicy.ExecuteAsync[TResult](Func`3 action, Context context, CancellationToken cancellationToken, Boolean continueOnCapturedContext)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(276,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteWithRetry[T](Func`1 operation)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(170,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.98]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(228,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult()&#xD;
[xUnit.net 00:00:30.98]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:30.99]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:00:30.99]       Stack Trace:&#xD;
[xUnit.net 00:00:30.99]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(173,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.99]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.99]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.99]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.99]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:30.99]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(299,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithNullCounts_ShouldReturnSuccessWithoutGeneration()&#xD;
[xUnit.net 00:00:30.99]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:00:31.00]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:00:31.00]       Stack Trace:&#xD;
[xUnit.net 00:00:31.00]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:31.00]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:31.00]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:31.00]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:31.00]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:31.00]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(322,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices()&#xD;
[xUnit.net 00:00:31.00]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Validation completed for session a7238cba-e18a-44d5-a2d9-5b212cfd4543: 70286 valid, 0 invalid&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Updated seeding session a7238cba-e18a-44d5-a2d9-5b212cfd4543 status to Completed. Total: 6, Successful: 6, Failed: 0&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Bulk seeding completed successfully ffcf3f6b. Duration: 00:00:32.0369583&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session ffcf3f6b&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
[xUnit.net 00:00:33.18]       Assert.False() Failure&#xD;
[xUnit.net 00:00:33.18]       Expected: False&#xD;
[xUnit.net 00:00:33.18]       Actual:   True&#xD;
[xUnit.net 00:00:33.18]       Stack Trace:&#xD;
[xUnit.net 00:00:33.18]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(219,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully()&#xD;
[xUnit.net 00:00:33.18]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Starting bulk seeding operation e51f9849&#xD;
fail: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Bulk seeding failed e51f9849&#xD;
      System.ArgumentException: Drivers count must be positive (Parameter 'DriversCount')&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ValidateSeederOptions(SeederOptions options) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 124&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 74&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session e51f9849&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
[xUnit.net 00:00:33.20]       System.ArgumentException : Drivers count must be positive (Parameter 'DriversCount')&#xD;
[xUnit.net 00:00:33.20]       Stack Trace:&#xD;
[xUnit.net 00:00:33.20]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(124,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ValidateSeederOptions(SeederOptions options)&#xD;
[xUnit.net 00:00:33.20]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(74,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:33.20]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:33.20]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:00:33.20]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(138,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_ShouldSendSignalRNotifications()&#xD;
[xUnit.net 00:00:33.20]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Starting Person/Driver seeding using migration patterns 689690b2&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Validating migration pattern prerequisites...&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Migration pattern validation completed: False (API: False, DB: True, Data: True)&#xD;
[xUnit.net 00:00:33.22]       Assert.Contains() Failure: Sub-string not found&#xD;
[xUnit.net 00:00:33.22]       String:    ""&#xD;
[xUnit.net 00:00:33.22]       Not found: "API service failed"&#xD;
[xUnit.net 00:00:33.22]       Stack Trace:&#xD;
[xUnit.net 00:00:33.22]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(244,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully()&#xD;
[xUnit.net 00:00:33.22]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Starting Person/Driver seeding using migration patterns 35de7322&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Validating migration pattern prerequisites...&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Migration pattern validation completed: False (API: False, DB: True, Data: True)&#xD;
[xUnit.net 00:00:33.23]       Moq.MockException : &#xD;
[xUnit.net 00:00:33.23]       Expected invocation on the mock at least once, but was never performed: x =&gt; x.SendCoreAsync("ProgressUpdate", It.IsAny&lt;object[]&gt;(), It.IsAny&lt;CancellationToken&gt;())&#xD;
[xUnit.net 00:00:33.23]       &#xD;
[xUnit.net 00:00:33.23]       Performed invocations:&#xD;
[xUnit.net 00:00:33.23]       &#xD;
[xUnit.net 00:00:33.23]          Mock&lt;IClientProxy:41&gt; (x):&#xD;
[xUnit.net 00:00:33.23]          No invocations performed.&#xD;
[xUnit.net 00:00:33.23]       &#xD;
[xUnit.net 00:00:33.23]       Stack Trace:&#xD;
[xUnit.net 00:00:33.23]         /_/src/Moq/Mock.cs(332,0): at Moq.Mock.Verify(Mock mock, LambdaExpression expression, Times times, String failMessage)&#xD;
[xUnit.net 00:00:33.23]         /_/src/Moq/Mock`1.cs(840,0): at Moq.Mock`1.Verify[TResult](Expression`1 expression, Func`1 times)&#xD;
[xUnit.net 00:00:33.23]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(162,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_ShouldSendSignalRNotifications()&#xD;
[xUnit.net 00:00:33.23]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Starting bulk seeding operation 50100569&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Seeding options: Drivers=5, Vehicles=3, BatchSize=100, DryRun=True, Generate=True&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      DRY RUN MODE - No data will be modified&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Created seeding session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a with name 'BulkSeeder_50100569' in environment Development&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generating 5 driver records for session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generated 5/5 driver records&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Driver data generation completed for session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a. Generated 5 records in 00:00:01.5783950&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generating 3 vehicle records for session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a&#xD;
[xUnit.net 00:00:48.02]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:00:48.02]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:00:48.02]       Stack Trace:&#xD;
[xUnit.net 00:00:48.02]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(323,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly()&#xD;
[xUnit.net 00:00:48.02]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Generated 3/3 vehicle records&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Vehicle data generation completed for session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a. Generated 3 records in 00:00:13.3134713&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
[xUnit.net 00:01:01.07]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.07]       Stack Trace:&#xD;
[xUnit.net 00:01:01.07]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.07]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.07]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.07]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.07]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.07]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(163,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync()&#xD;
[xUnit.net 00:01:01.07]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.08]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.08]       Stack Trace:&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(201,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync()&#xD;
[xUnit.net 00:01:01.08]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.08]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.08]       Stack Trace:&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.08]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(389,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_ShouldSendProgressNotifications()&#xD;
[xUnit.net 00:01:01.08]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.09]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.09]       Stack Trace:&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(144,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync()&#xD;
[xUnit.net 00:01:01.09]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.09]       System.ArgumentException : Drivers count must be positive (Parameter 'DriversCount')&#xD;
[xUnit.net 00:01:01.09]       Stack Trace:&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(124,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ValidateSeederOptions(SeederOptions options)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(74,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.09]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(276,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithZeroCounts_ShouldReturnSuccessWithoutGeneration()&#xD;
[xUnit.net 00:01:01.09]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.10]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.10]       Stack Trace:&#xD;
[xUnit.net 00:01:01.10]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.10]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.10]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.10]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.10]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.10]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(360,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly()&#xD;
[xUnit.net 00:01:01.10]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.11]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.11]       Stack Trace:&#xD;
[xUnit.net 00:01:01.11]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.11]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.11]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.11]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.11]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.11]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(256,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult()&#xD;
[xUnit.net 00:01:01.11]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.12]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.12]       Stack Trace:&#xD;
[xUnit.net 00:01:01.12]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.12]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.12]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.12]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.12]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.12]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(125,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult()&#xD;
[xUnit.net 00:01:01.12]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:01.13]       System.NullReferenceException : Object reference not set to an instance of an object.&#xD;
[xUnit.net 00:01:01.13]       Stack Trace:&#xD;
[xUnit.net 00:01:01.13]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(210,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.13]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(254,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.13]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(91,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.13]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(117,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, String sessionId, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.13]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs(49,0): at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken)&#xD;
[xUnit.net 00:01:01.13]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\BulkSeederServiceTests.cs(182,0): at XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync()&#xD;
[xUnit.net 00:01:01.13]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Validation completed for session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a: 75344 valid, 0 invalid&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.SqlDataGenerationService[0]&#xD;
      Updated seeding session aeaa3ea8-4c1f-4bf6-9719-f12c2a48e11a status to Completed. Total: 8, Successful: 8, Failed: 0&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Bulk seeding completed successfully 50100569. Duration: 00:00:35.9257627&#xD;
warn: XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService[0]&#xD;
      Failed to send progress notification for session 50100569&#xD;
      System.NullReferenceException: Object reference not set to an instance of an object.&#xD;
         at Microsoft.AspNetCore.SignalR.ClientProxyExtensions.SendAsync(IClientProxy clientProxy, String method, Object arg1, CancellationToken cancellationToken)&#xD;
         at XQ360.DataMigration.Web.Services.BulkSeeder.BulkSeederService.NotifyProgress(String sessionId, String status, Int32 progressPercentage, String message) in D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.Web\Services\BulkSeeder\BulkSeederService.cs:line 293&#xD;
[xUnit.net 00:01:09.22]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:09.22]       Expected: typeof(System.ArgumentException)&#xD;
[xUnit.net 00:01:09.22]       Stack Trace:&#xD;
[xUnit.net 00:01:09.22]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(272,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException()&#xD;
[xUnit.net 00:01:09.22]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:09.22]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:09.22]       Expected: typeof(System.ArgumentException)&#xD;
[xUnit.net 00:01:09.22]       Stack Trace:&#xD;
[xUnit.net 00:01:09.22]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(205,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException()&#xD;
[xUnit.net 00:01:09.22]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:09.22]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:09.22]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:01:09.22]       Stack Trace:&#xD;
[xUnit.net 00:01:09.22]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(84,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether()&#xD;
[xUnit.net 00:01:09.22]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Starting Person/Driver seeding using migration patterns 74b55a1f&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Validating migration pattern prerequisites...&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Migration pattern validation completed: False (API: False, DB: True, Data: True)&#xD;
[xUnit.net 00:01:09.23]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:09.23]       Expected: typeof(System.OperationCanceledException)&#xD;
[xUnit.net 00:01:09.23]       Stack Trace:&#xD;
[xUnit.net 00:01:09.23]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(277,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WithCancellation_ShouldStopGracefully()&#xD;
[xUnit.net 00:01:09.23]         --- End of stack trace from previous location ---&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Starting Person/Driver seeding using migration patterns 81776d8f&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Validating migration pattern prerequisites...&#xD;
info: XQ360.DataMigration.Web.Services.BulkSeeder.MigrationPatternSeederService[0]&#xD;
      Migration pattern validation completed: False (API: False, DB: True, Data: True)&#xD;
[xUnit.net 00:01:09.24]       Assert.True() Failure&#xD;
[xUnit.net 00:01:09.24]       Expected: True&#xD;
[xUnit.net 00:01:09.24]       Actual:   False&#xD;
[xUnit.net 00:01:09.24]       Stack Trace:&#xD;
[xUnit.net 00:01:09.24]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\DataSeederIntegrationTests.cs(117,0): at XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether()&#xD;
[xUnit.net 00:01:09.24]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:10.20]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:10.20]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:01:10.20]       Stack Trace:&#xD;
[xUnit.net 00:01:10.20]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(101,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult()&#xD;
[xUnit.net 00:01:10.20]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:28.51]       Assert.ThrowsAny() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:28.51]       Expected: typeof(System.Exception)&#xD;
[xUnit.net 00:01:28.51]       Stack Trace:&#xD;
[xUnit.net 00:01:28.51]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(304,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly()&#xD;
[xUnit.net 00:01:28.51]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:28.52]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:28.52]       Expected: typeof(System.OperationCanceledException)&#xD;
[xUnit.net 00:01:28.52]       Stack Trace:&#xD;
[xUnit.net 00:01:28.52]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(286,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation()&#xD;
[xUnit.net 00:01:28.52]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:28.52]       Assert.Throws() Failure: No exception was thrown&#xD;
[xUnit.net 00:01:28.52]       Expected: typeof(System.ArgumentException)&#xD;
[xUnit.net 00:01:28.52]       Stack Trace:&#xD;
[xUnit.net 00:01:28.52]         D:\CODEZ\workz\XQ360DataMigration\XQ360.DataMigration.DataSeeder.Tests\SqlDataGenerationServiceTests.cs(137,0): at XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException()&#xD;
[xUnit.net 00:01:28.52]         --- End of stack trace from previous location ---&#xD;
[xUnit.net 00:01:28.53]   Finished:    XQ360.DataMigration.DataSeeder.Tests&#xD;
</StdOut>
    </Output>
    <RunInfos>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.2343111+08:00">
        <Text>[xUnit.net 00:00:00.62]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_ApiCalls_ShouldRespectRateLimit [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.2407590+08:00">
        <Text>[xUnit.net 00:00:00.63]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_ConcurrentRequests_ShouldHandleCorrectly [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.2418737+08:00">
        <Text>[xUnit.net 00:00:00.63]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_ConcurrentApiCalls_ShouldHandleCorrectly [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.2422260+08:00">
        <Text>[xUnit.net 00:00:00.63]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_RepeatedOperations_ShouldMaintainPerformance [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.3064137+08:00">
        <Text>[xUnit.net 00:00:00.64]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_LargeLoad_ShouldNotExceedMemoryThreshold [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4620221+08:00">
        <Text>[xUnit.net 00:00:00.72]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.MigrationPatternSeederService_RepeatedValidations_ShouldMaintainPerformance [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4625387+08:00">
        <Text>[xUnit.net 00:00:00.72]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_MediumLoad_ShouldCompleteWithinTimeLimit [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4631848+08:00">
        <Text>[xUnit.net 00:00:00.72]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_LargeLoad_ShouldNotExceedMemoryThreshold [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4635142+08:00">
        <Text>[xUnit.net 00:00:00.72]     XQ360.DataMigration.DataSeeder.Tests.DataSeederPerformanceTests.BulkSeederService_SmallLoad_ShouldCompleteWithinTimeLimit [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4638197+08:00">
        <Text>[xUnit.net 00:00:00.72]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WhenPersonCreationFails_ShouldStopAndReturnFailure [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4648963+08:00">
        <Text>[xUnit.net 00:00:00.76]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ValidateMigrationPatternPrerequisitesAsync_WhenApiThrows_ShouldReturnInvalidResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4651751+08:00">
        <Text>[xUnit.net 00:00:00.81]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WhenApiOrchestrationFails_ShouldReturnFailureResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4653976+08:00">
        <Text>[xUnit.net 00:00:00.83]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithValidOptions_ShouldReturnSuccessResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.4735541+08:00">
        <Text>[xUnit.net 00:00:00.86]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithNullDriversCount_ShouldReturnSuccessWithoutApiCall [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.5174876+08:00">
        <Text>[xUnit.net 00:00:00.90]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithDriversCount_ShouldCallApiOrchestration [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.5234614+08:00">
        <Text>[xUnit.net 00:00:00.91]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecuteFullMigrationPatternAsync_WithValidOptions_ShouldReturnSuccessResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.5343799+08:00">
        <Text>[xUnit.net 00:00:00.92]     XQ360.DataMigration.DataSeeder.Tests.MigrationPatternSeederServiceTests.ExecutePersonDriverSeederAsync_WithZeroDriversCount_ShouldReturnSuccessWithoutApiCall [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:07.6653939+08:00">
        <Text>[xUnit.net 00:00:01.05]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WithCancellation_ShouldStopGracefully [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:15.5319761+08:00">
        <Text>[xUnit.net 00:00:08.92]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:22.6529364+08:00">
        <Text>[xUnit.net 00:00:16.04]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:22.6579831+08:00">
        <Text>[xUnit.net 00:00:16.05]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:28.5273194+08:00">
        <Text>[xUnit.net 00:00:21.92]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ValidateStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:29.7801110+08:00">
        <Text>[xUnit.net 00:00:23.17]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:37.5947081+08:00">
        <Text>[xUnit.net 00:00:30.98]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenSqlDataServiceThrows_ShouldReturnFailureResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:37.6004344+08:00">
        <Text>[xUnit.net 00:00:30.99]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithNullCounts_ShouldReturnSuccessWithoutGeneration [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:37.6155681+08:00">
        <Text>[xUnit.net 00:00:31.00]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithCancellationToken_ShouldPassTokenToServices [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:39.7940969+08:00">
        <Text>[xUnit.net 00:00:33.18]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_WhenSqlServiceFails_ShouldHandleGracefully [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:39.8110570+08:00">
        <Text>[xUnit.net 00:00:33.20]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_ShouldSendSignalRNotifications [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:39.8270825+08:00">
        <Text>[xUnit.net 00:00:33.22]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WhenApiServiceFails_ShouldHandleGracefully [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:39.8423470+08:00">
        <Text>[xUnit.net 00:00:33.23]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_ShouldSendSignalRNotifications [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:56:54.6325101+08:00">
        <Text>[xUnit.net 00:00:48.02]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.6796327+08:00">
        <Text>[xUnit.net 00:01:01.07]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithVehiclesCount_ShouldCallGenerateVehicleDataAsync [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.6878554+08:00">
        <Text>[xUnit.net 00:01:01.08]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunTrue_ShouldNotCallProcessStagedDataAsync [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.6929049+08:00">
        <Text>[xUnit.net 00:01:01.08]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_ShouldSendProgressNotifications [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.6967828+08:00">
        <Text>[xUnit.net 00:01:01.09]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDriversCount_ShouldCallGenerateDriverDataAsync [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.7028527+08:00">
        <Text>[xUnit.net 00:01:01.09]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithZeroCounts_ShouldReturnSuccessWithoutGeneration [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.7082613+08:00">
        <Text>[xUnit.net 00:01:01.10]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithLargeCounts_ShouldHandleCorrectly [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.7231360+08:00">
        <Text>[xUnit.net 00:01:01.11]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WhenProcessingFails_ShouldReturnFailureResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.7267353+08:00">
        <Text>[xUnit.net 00:01:01.12]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithValidOptions_ShouldReturnSuccessResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:07.7405706+08:00">
        <Text>[xUnit.net 00:01:01.13]     XQ360.DataMigration.DataSeeder.Tests.BulkSeederServiceTests.ExecuteSeederAsync_WithDryRunFalse_ShouldCallProcessStagedDataAsync [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:15.8276488+08:00">
        <Text>[xUnit.net 00:01:09.22]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithEmptyGuid_ShouldThrowArgumentException [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:15.8293640+08:00">
        <Text>[xUnit.net 00:01:09.22]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:15.8355010+08:00">
        <Text>[xUnit.net 00:01:09.22]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.BulkSeederService_IntegratedWithSqlDataService_ShouldWorkTogether [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:15.8402579+08:00">
        <Text>[xUnit.net 00:01:09.23]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_WithCancellation_ShouldStopGracefully [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:15.8470742+08:00">
        <Text>[xUnit.net 00:01:09.24]     XQ360.DataMigration.DataSeeder.Tests.DataSeederIntegrationTests.MigrationPatternSeederService_IntegratedWithDependencies_ShouldWorkTogether [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:16.8115924+08:00">
        <Text>[xUnit.net 00:01:10.20]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:35.1243746+08:00">
        <Text>[xUnit.net 00:01:28.51]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:35.1263960+08:00">
        <Text>[xUnit.net 00:01:28.52]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation [FAIL]</Text>
      </RunInfo>
      <RunInfo computerName="DESKTOP-LJEH13I" outcome="Error" timestamp="2025-08-19T15:57:35.1323417+08:00">
        <Text>[xUnit.net 00:01:28.52]     XQ360.DataMigration.DataSeeder.Tests.SqlDataGenerationServiceTests.GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException [FAIL]</Text>
      </RunInfo>
    </RunInfos>
  </ResultSummary>
</TestRun>