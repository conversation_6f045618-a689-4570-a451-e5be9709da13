# PowerShell script to run IoT-related unit tests
# Usage: .\RunIoTTests.ps1 [-Coverage] [-Verbose] [-Filter <pattern>]

param(
    [switch]$Coverage,
    [switch]$Verbose,
    [string]$Filter = "IoT"
)

Write-Host "🚀 Running IoT Device Creation and Vehicle Linking Tests" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green

# Set test project path
$TestProject = "XQ360.DataMigration.Tests.csproj"
$TestDirectory = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to test directory
Push-Location $TestDirectory

try {
    # Build the test project first
    Write-Host "📦 Building test project..." -ForegroundColor Yellow
    dotnet build $TestProject --configuration Release --verbosity minimal
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Build successful!" -ForegroundColor Green
    Write-Host ""

    # Prepare test command
    $TestCommand = @("test", $TestProject, "--configuration", "Release", "--no-build")
    
    # Add filter if specified
    if ($Filter) {
        $TestCommand += "--filter"
        $TestCommand += $Filter
        Write-Host "🔍 Running tests with filter: $Filter" -ForegroundColor Cyan
    }
    
    # Add coverage collection if requested
    if ($Coverage) {
        $TestCommand += "--collect:XPlat Code Coverage"
        $TestCommand += "--results-directory"
        $TestCommand += "TestResults"
        Write-Host "📊 Code coverage collection enabled" -ForegroundColor Cyan
    }
    
    # Add verbosity if requested
    if ($Verbose) {
        $TestCommand += "--verbosity"
        $TestCommand += "detailed"
        Write-Host "📝 Verbose output enabled" -ForegroundColor Cyan
    } else {
        $TestCommand += "--verbosity"
        $TestCommand += "normal"
    }

    Write-Host ""
    Write-Host "🧪 Executing tests..." -ForegroundColor Yellow
    Write-Host "Command: dotnet $($TestCommand -join ' ')" -ForegroundColor Gray
    Write-Host ""

    # Execute tests
    & dotnet @TestCommand

    $TestExitCode = $LASTEXITCODE

    Write-Host ""
    
    if ($TestExitCode -eq 0) {
        Write-Host "✅ All tests passed!" -ForegroundColor Green
        
        # Display coverage report if generated
        if ($Coverage) {
            Write-Host ""
            Write-Host "📊 Code coverage report generated in TestResults directory" -ForegroundColor Cyan
            
            # Try to find and display coverage summary
            $CoverageFiles = Get-ChildItem -Path "TestResults" -Filter "coverage.cobertura.xml" -Recurse -ErrorAction SilentlyContinue
            if ($CoverageFiles) {
                Write-Host "Coverage files found:" -ForegroundColor Cyan
                $CoverageFiles | ForEach-Object { Write-Host "  - $($_.FullName)" -ForegroundColor Gray }
            }
        }
    } else {
        Write-Host "❌ Some tests failed!" -ForegroundColor Red
        Write-Host "Exit code: $TestExitCode" -ForegroundColor Red
    }

    # Display test summary
    Write-Host ""
    Write-Host "📋 Test Summary:" -ForegroundColor Yellow
    Write-Host "  Filter: $Filter" -ForegroundColor Gray
    Write-Host "  Coverage: $($Coverage ? 'Enabled' : 'Disabled')" -ForegroundColor Gray
    Write-Host "  Verbose: $($Verbose ? 'Enabled' : 'Disabled')" -ForegroundColor Gray
    Write-Host "  Exit Code: $TestExitCode" -ForegroundColor Gray

} catch {
    Write-Host "❌ Error occurred during test execution:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    $TestExitCode = 1
} finally {
    # Return to original directory
    Pop-Location
}

Write-Host ""
Write-Host "============================================================" -ForegroundColor Green

# Exit with the same code as the test execution
exit $TestExitCode
