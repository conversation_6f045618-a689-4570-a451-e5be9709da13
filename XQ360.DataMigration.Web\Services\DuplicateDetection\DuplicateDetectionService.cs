using System.Diagnostics;
using System.Reflection;

namespace XQ360.DataMigration.Web.Services.DuplicateDetection
{
    /// <summary>
    /// Implementation of duplicate detection service
    /// </summary>
    public class DuplicateDetectionService : IDuplicateDetectionService
    {
        private readonly ILogger<DuplicateDetectionService> _logger;
        private readonly Dictionary<string, string[]> _commonNicknames;

        public DuplicateDetectionService(ILogger<DuplicateDetectionService> logger)
        {
            _logger = logger;
            _commonNicknames = InitializeNicknameDatabase();
        }

        public async Task<DuplicateDetectionResult> DetectDuplicatesAsync<T>(IEnumerable<T> entities, DuplicateDetectionConfig config, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var entityList = entities.ToList();
            
            _logger.LogInformation("Starting duplicate detection for {Count} entities of type {Type}", 
                entityList.Count, typeof(T).Name);

            try
            {
                var result = new DuplicateDetectionResult
                {
                    DetectedAt = DateTime.UtcNow,
                    ConfigUsed = config
                };

                var duplicateGroups = new List<DuplicateGroup<object>>();
                var processedIndices = new HashSet<int>();

                // Compare each entity with all others
                for (int i = 0; i < entityList.Count; i++)
                {
                    if (processedIndices.Contains(i) || cancellationToken.IsCancellationRequested)
                        continue;

                    var currentGroup = new List<DuplicateCandidate<object>>
                    {
                        new DuplicateCandidate<object>
                        {
                            Entity = entityList[i]!,
                            OriginalIndex = i,
                            EntityIdentifier = GetEntityIdentifier(entityList[i]),
                            SimilarityScore = 1.0,
                            IsPrimaryCandidate = true
                        }
                    };

                    // Find similar entities
                    for (int j = i + 1; j < entityList.Count; j++)
                    {
                        if (processedIndices.Contains(j))
                            continue;

                        var similarity = await CalculateSimilarityScoreAsync(entityList[i], entityList[j], 
                            new SimilarityConfig 
                            { 
                                FieldWeights = config.FieldWeights,
                                Algorithm = config.Algorithm 
                            }, cancellationToken);

                        if (similarity >= config.SimilarityThreshold)
                        {
                            currentGroup.Add(new DuplicateCandidate<object>
                            {
                                Entity = entityList[j]!,
                                OriginalIndex = j,
                                EntityIdentifier = GetEntityIdentifier(entityList[j]),
                                SimilarityScore = similarity,
                                IsPrimaryCandidate = false
                            });
                            processedIndices.Add(j);
                        }
                    }

                    // If we found duplicates, create a group
                    if (currentGroup.Count > 1)
                    {
                        var group = new DuplicateGroup<object>
                        {
                            GroupId = Guid.NewGuid().ToString(),
                            Candidates = currentGroup,
                            AverageSimilarity = currentGroup.Average(c => c.SimilarityScore),
                            MaxSimilarity = currentGroup.Max(c => c.SimilarityScore),
                            MinSimilarity = currentGroup.Min(c => c.SimilarityScore),
                            ConfidenceLevel = DetermineConfidenceLevel(currentGroup.Average(c => c.SimilarityScore)),
                            ReasonCode = DetermineReasonCode(currentGroup, config)
                        };

                        duplicateGroups.Add(group);
                        processedIndices.Add(i);
                    }
                }

                result.DuplicateGroups = duplicateGroups;
                result.Statistics = CalculateStatistics(entityList, duplicateGroups);
                result.DetectionDuration = stopwatch.Elapsed;

                _logger.LogInformation("Duplicate detection completed: {GroupCount} groups found with {DuplicateCount} duplicates in {Duration}ms",
                    duplicateGroups.Count, result.Statistics.DuplicateEntities, result.DetectionDuration.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during duplicate detection for type {Type}", typeof(T).Name);
                throw;
            }
        }

        public async Task<DuplicateDetectionResult> DetectPersonDuplicatesAsync(IEnumerable<object> persons, CancellationToken cancellationToken = default)
        {
            var config = new PersonDuplicateConfig
            {
                ExactMatchFields = new[] { "FirstName", "LastName", "DepartmentName" },
                FuzzyMatchFields = new[] { "FirstName", "LastName" },
                SimilarityThreshold = 0.85,
                EnableFuzzyMatching = true,
                CaseSensitive = false,
                Algorithm = FuzzyMatchingAlgorithm.JaroWinkler,
                FieldWeights = new Dictionary<string, double>
                {
                    ["FirstName"] = 0.4,
                    ["LastName"] = 0.4,
                    ["DepartmentName"] = 0.2
                },
                NameSimilarityThreshold = 0.85,
                MatchNicknames = true,
                KnownNicknames = _commonNicknames
            };

            return await DetectDuplicatesAsync(persons, config, cancellationToken);
        }

        public async Task<DuplicateDetectionResult> DetectVehicleDuplicatesAsync(IEnumerable<object> vehicles, CancellationToken cancellationToken = default)
        {
            var config = new VehicleDuplicateConfig
            {
                ExactMatchFields = new[] { "SerialNumber" },
                FuzzyMatchFields = new[] { "SerialNumber", "ModelName" },
                SimilarityThreshold = 0.95,
                EnableFuzzyMatching = true,
                CaseSensitive = false,
                Algorithm = FuzzyMatchingAlgorithm.Levenshtein,
                FieldWeights = new Dictionary<string, double>
                {
                    ["SerialNumber"] = 0.8,
                    ["ModelName"] = 0.2
                },
                MatchOnSerialOnly = true,
                NormalizeSerialNumbers = true
            };

            return await DetectDuplicatesAsync(vehicles, config, cancellationToken);
        }

        public async Task<DuplicateResolutionResult> ResolveDuplicatesAsync<T>(DuplicateDetectionResult duplicates, DuplicateResolutionStrategy strategy, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            
            _logger.LogInformation("Resolving {GroupCount} duplicate groups using {Strategy} strategy", 
                duplicates.DuplicateGroups.Count, strategy);

            try
            {
                var result = new DuplicateResolutionResult
                {
                    ResolvedAt = DateTime.UtcNow,
                    StrategyUsed = strategy
                };

                foreach (var group in duplicates.DuplicateGroups)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        var resolvedGroup = await ResolveGroupAsync(group, strategy, cancellationToken);
                        result.ResolvedGroups.Add(resolvedGroup);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error resolving duplicate group {GroupId}", group.GroupId);
                        result.Errors.Add(new ResolutionError
                        {
                            GroupId = group.GroupId,
                            ErrorMessage = ex.Message,
                            Exception = ex
                        });
                    }
                }

                result.Statistics = CalculateResolutionStatistics(result);
                result.ResolutionDuration = stopwatch.Elapsed;

                _logger.LogInformation("Duplicate resolution completed: {ResolvedCount}/{TotalCount} groups resolved in {Duration}ms",
                    result.Statistics.ResolvedGroups, result.Statistics.TotalGroups, result.ResolutionDuration.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during duplicate resolution");
                throw;
            }
        }

        public async Task<double> CalculateSimilarityScoreAsync<T>(T entity1, T entity2, SimilarityConfig config, CancellationToken cancellationToken = default)
        {
            if (entity1 == null || entity2 == null)
                return 0.0;

            try
            {
                var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
                var totalWeight = 0.0;
                var weightedScore = 0.0;

                foreach (var property in properties)
                {
                    var fieldName = property.Name;
                    var weight = config.FieldWeights.GetValueOrDefault(fieldName, 1.0);
                    
                    if (weight <= 0) continue;

                    var value1 = property.GetValue(entity1)?.ToString() ?? "";
                    var value2 = property.GetValue(entity2)?.ToString() ?? "";

                    var fieldSimilarity = CalculateFieldSimilarity(value1, value2, config.Algorithm);
                    
                    weightedScore += fieldSimilarity * weight;
                    totalWeight += weight;
                }

                var finalScore = totalWeight > 0 ? weightedScore / totalWeight : 0.0;
                
                // Normalize to configured range
                return Math.Max(config.MinimumScore, Math.Min(config.MaximumScore, finalScore));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calculating similarity score between entities");
                return 0.0;
            }
        }

        public DuplicateDetectionConfig GetDefaultConfig<T>()
        {
            var entityType = typeof(T).Name;
            
            return entityType switch
            {
                "Person" or "PersonImportModel" => new PersonDuplicateConfig
                {
                    ExactMatchFields = new[] { "FirstName", "LastName", "DepartmentName" },
                    FuzzyMatchFields = new[] { "FirstName", "LastName" },
                    SimilarityThreshold = 0.85,
                    EnableFuzzyMatching = true,
                    Algorithm = FuzzyMatchingAlgorithm.JaroWinkler
                },
                "Vehicle" or "VehicleImportModel" => new VehicleDuplicateConfig
                {
                    ExactMatchFields = new[] { "SerialNumber" },
                    FuzzyMatchFields = new[] { "SerialNumber" },
                    SimilarityThreshold = 0.95,
                    EnableFuzzyMatching = false,
                    Algorithm = FuzzyMatchingAlgorithm.Levenshtein
                },
                _ => new DuplicateDetectionConfig
                {
                    SimilarityThreshold = 0.8,
                    EnableFuzzyMatching = true,
                    Algorithm = FuzzyMatchingAlgorithm.Levenshtein
                }
            };
        }

        public async Task<ValidationResult> ValidateResolutionAsync<T>(DuplicateGroup<T> duplicateGroup, DuplicateResolutionStrategy strategy, CancellationToken cancellationToken = default)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // Validate group has candidates
                if (!duplicateGroup.Candidates.Any())
                {
                    result.Issues.Add(new ValidationIssue
                    {
                        IssueType = "EmptyGroup",
                        Message = "Duplicate group has no candidates",
                        Suggestion = "Ensure group contains at least one candidate"
                    });
                    result.IsValid = false;
                }

                // Validate strategy compatibility
                if (strategy == DuplicateResolutionStrategy.Merge && duplicateGroup.Candidates.Count < 2)
                {
                    result.Issues.Add(new ValidationIssue
                    {
                        IssueType = "InsufficientCandidates",
                        Message = "Merge strategy requires at least 2 candidates",
                        Suggestion = "Use a different resolution strategy or ensure group has multiple candidates"
                    });
                    result.IsValid = false;
                }

                // Add warnings for low confidence groups
                if (duplicateGroup.ConfidenceLevel == DuplicateConfidenceLevel.Low)
                {
                    result.Warnings.Add(new ValidationWarning
                    {
                        WarningType = "LowConfidence",
                        Message = "Duplicate group has low confidence level",
                        Recommendation = "Consider manual review before resolution"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating resolution for group {GroupId}", duplicateGroup.GroupId);
                result.IsValid = false;
                result.Issues.Add(new ValidationIssue
                {
                    IssueType = "ValidationError",
                    Message = $"Validation failed: {ex.Message}",
                    Suggestion = "Contact system administrator"
                });
                return result;
            }
        }

        // Helper methods continue in next part due to length constraints
        private string GetEntityIdentifier(object? entity)
        {
            if (entity == null) return "null";
            
            var type = entity.GetType();
            
            // Try common identifier properties
            var idProperty = type.GetProperty("Id") ?? type.GetProperty("ID");
            if (idProperty != null)
            {
                var id = idProperty.GetValue(entity);
                if (id != null) return id.ToString() ?? "unknown";
            }
            
            // Try name properties
            var nameProperty = type.GetProperty("Name") ?? type.GetProperty("FirstName") ?? type.GetProperty("SerialNumber");
            if (nameProperty != null)
            {
                var name = nameProperty.GetValue(entity);
                if (name != null) return name.ToString() ?? "unknown";
            }
            
            return $"{type.Name}@{entity.GetHashCode()}";
        }

        private DuplicateConfidenceLevel DetermineConfidenceLevel(double averageSimilarity)
        {
            return averageSimilarity switch
            {
                >= 0.95 => DuplicateConfidenceLevel.VeryHigh,
                >= 0.85 => DuplicateConfidenceLevel.High,
                >= 0.70 => DuplicateConfidenceLevel.Medium,
                _ => DuplicateConfidenceLevel.Low
            };
        }

        private string DetermineReasonCode(List<DuplicateCandidate<object>> candidates, DuplicateDetectionConfig config)
        {
            // Simplified reason code determination
            var avgSimilarity = candidates.Average(c => c.SimilarityScore);
            
            if (avgSimilarity >= 0.95)
                return "EXACT_MATCH";
            else if (avgSimilarity >= 0.85)
                return "HIGH_SIMILARITY";
            else if (avgSimilarity >= 0.70)
                return "MODERATE_SIMILARITY";
            else
                return "LOW_SIMILARITY";
        }

        private DuplicateStatistics CalculateStatistics<T>(List<T> entities, List<DuplicateGroup<object>> duplicateGroups)
        {
            var duplicateEntities = duplicateGroups.Sum(g => g.Candidates.Count);
            
            return new DuplicateStatistics
            {
                TotalEntities = entities.Count,
                UniqueEntities = entities.Count - duplicateEntities,
                DuplicateEntities = duplicateEntities,
                DuplicateGroups = duplicateGroups.Count,
                DuplicationRate = entities.Count > 0 ? (double)duplicateEntities / entities.Count * 100 : 0,
                GroupsByConfidence = duplicateGroups.GroupBy(g => g.ConfidenceLevel).ToDictionary(g => g.Key, g => g.Count()),
                AverageSimilarityScore = duplicateGroups.Any() ? duplicateGroups.Average(g => g.AverageSimilarity) : 0
            };
        }

        private double CalculateFieldSimilarity(string value1, string value2, FuzzyMatchingAlgorithm algorithm)
        {
            if (string.IsNullOrEmpty(value1) && string.IsNullOrEmpty(value2))
                return 1.0;
            
            if (string.IsNullOrEmpty(value1) || string.IsNullOrEmpty(value2))
                return 0.0;

            // Normalize values
            value1 = value1.Trim().ToLowerInvariant();
            value2 = value2.Trim().ToLowerInvariant();

            if (value1 == value2)
                return 1.0;

            return algorithm switch
            {
                FuzzyMatchingAlgorithm.Levenshtein => CalculateLevenshteinSimilarity(value1, value2),
                FuzzyMatchingAlgorithm.JaroWinkler => CalculateJaroWinklerSimilarity(value1, value2),
                _ => CalculateLevenshteinSimilarity(value1, value2)
            };
        }

        private double CalculateLevenshteinSimilarity(string s1, string s2)
        {
            var distance = CalculateLevenshteinDistance(s1, s2);
            var maxLength = Math.Max(s1.Length, s2.Length);
            return maxLength == 0 ? 1.0 : 1.0 - (double)distance / maxLength;
        }

        private int CalculateLevenshteinDistance(string s1, string s2)
        {
            var matrix = new int[s1.Length + 1, s2.Length + 1];

            for (int i = 0; i <= s1.Length; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= s2.Length; j++)
                matrix[0, j] = j;

            for (int i = 1; i <= s1.Length; i++)
            {
                for (int j = 1; j <= s2.Length; j++)
                {
                    var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[s1.Length, s2.Length];
        }

        private double CalculateJaroWinklerSimilarity(string s1, string s2)
        {
            // Simplified Jaro-Winkler implementation
            var jaro = CalculateJaroSimilarity(s1, s2);
            
            if (jaro < 0.7) return jaro;

            var prefix = 0;
            for (int i = 0; i < Math.Min(s1.Length, s2.Length) && i < 4; i++)
            {
                if (s1[i] == s2[i])
                    prefix++;
                else
                    break;
            }

            return jaro + (0.1 * prefix * (1 - jaro));
        }

        private double CalculateJaroSimilarity(string s1, string s2)
        {
            if (s1.Length == 0 && s2.Length == 0) return 1.0;
            if (s1.Length == 0 || s2.Length == 0) return 0.0;

            var matchWindow = Math.Max(s1.Length, s2.Length) / 2 - 1;
            if (matchWindow < 0) matchWindow = 0;

            var s1Matches = new bool[s1.Length];
            var s2Matches = new bool[s2.Length];

            var matches = 0;
            var transpositions = 0;

            // Find matches
            for (int i = 0; i < s1.Length; i++)
            {
                var start = Math.Max(0, i - matchWindow);
                var end = Math.Min(i + matchWindow + 1, s2.Length);

                for (int j = start; j < end; j++)
                {
                    if (s2Matches[j] || s1[i] != s2[j]) continue;
                    s1Matches[i] = s2Matches[j] = true;
                    matches++;
                    break;
                }
            }

            if (matches == 0) return 0.0;

            // Find transpositions
            var k = 0;
            for (int i = 0; i < s1.Length; i++)
            {
                if (!s1Matches[i]) continue;
                while (!s2Matches[k]) k++;
                if (s1[i] != s2[k]) transpositions++;
                k++;
            }

            return (matches / (double)s1.Length + matches / (double)s2.Length + 
                   (matches - transpositions / 2.0) / matches) / 3.0;
        }

        private Dictionary<string, string[]> InitializeNicknameDatabase()
        {
            return new Dictionary<string, string[]>
            {
                ["Robert"] = new[] { "Bob", "Rob", "Bobby", "Robbie" },
                ["William"] = new[] { "Bill", "Will", "Billy", "Willie" },
                ["Richard"] = new[] { "Rick", "Dick", "Rich", "Richie" },
                ["Michael"] = new[] { "Mike", "Mick", "Mickey" },
                ["James"] = new[] { "Jim", "Jimmy", "Jamie" },
                ["John"] = new[] { "Jack", "Johnny", "Jon" },
                ["David"] = new[] { "Dave", "Davey" },
                ["Christopher"] = new[] { "Chris", "Christie" },
                ["Matthew"] = new[] { "Matt", "Matty" },
                ["Anthony"] = new[] { "Tony", "Ant" },
                ["Elizabeth"] = new[] { "Liz", "Beth", "Betty", "Lizzie" },
                ["Jennifer"] = new[] { "Jen", "Jenny", "Jenn" },
                ["Patricia"] = new[] { "Pat", "Patty", "Tricia" },
                ["Linda"] = new[] { "Lin", "Lindy" },
                ["Barbara"] = new[] { "Barb", "Babs", "Bobbie" },
                ["Susan"] = new[] { "Sue", "Susie", "Suzy" },
                ["Jessica"] = new[] { "Jess", "Jessie" },
                ["Sarah"] = new[] { "Sara" },
                ["Karen"] = new[] { "Kari" },
                ["Nancy"] = new[] { "Nan" }
            };
        }

        private async Task<ResolvedDuplicateGroup> ResolveGroupAsync(DuplicateGroup<object> group, DuplicateResolutionStrategy strategy, CancellationToken cancellationToken)
        {
            var resolvedGroup = new ResolvedDuplicateGroup
            {
                GroupId = group.GroupId
            };

            switch (strategy)
            {
                case DuplicateResolutionStrategy.KeepFirst:
                    resolvedGroup.ResolvedEntity = group.Candidates.First().Entity;
                    resolvedGroup.DiscardedEntities = group.Candidates.Skip(1).Select(c => c.Entity).ToList();
                    resolvedGroup.Action = ResolutionAction.Kept;
                    resolvedGroup.Reason = "Kept first occurrence";
                    break;

                case DuplicateResolutionStrategy.KeepLast:
                    resolvedGroup.ResolvedEntity = group.Candidates.Last().Entity;
                    resolvedGroup.DiscardedEntities = group.Candidates.Take(group.Candidates.Count - 1).Select(c => c.Entity).ToList();
                    resolvedGroup.Action = ResolutionAction.Kept;
                    resolvedGroup.Reason = "Kept last occurrence";
                    break;

                case DuplicateResolutionStrategy.KeepMostComplete:
                    var mostComplete = group.Candidates.OrderByDescending(c => CalculateCompleteness(c.Entity)).First();
                    resolvedGroup.ResolvedEntity = mostComplete.Entity;
                    resolvedGroup.DiscardedEntities = group.Candidates.Where(c => c != mostComplete).Select(c => c.Entity).ToList();
                    resolvedGroup.Action = ResolutionAction.Kept;
                    resolvedGroup.Reason = "Kept most complete record";
                    break;

                case DuplicateResolutionStrategy.MarkForReview:
                    resolvedGroup.Action = ResolutionAction.MarkedForReview;
                    resolvedGroup.Reason = "Marked for manual review";
                    break;

                case DuplicateResolutionStrategy.Skip:
                    resolvedGroup.Action = ResolutionAction.Skipped;
                    resolvedGroup.Reason = "Skipped as requested";
                    break;

                default:
                    resolvedGroup.Action = ResolutionAction.Error;
                    resolvedGroup.Reason = $"Unsupported resolution strategy: {strategy}";
                    break;
            }

            return resolvedGroup;
        }

        private double CalculateCompleteness(object entity)
        {
            if (entity == null) return 0.0;

            var properties = entity.GetType().GetProperties();
            var totalFields = properties.Length;
            var completedFields = 0;

            foreach (var property in properties)
            {
                var value = property.GetValue(entity);
                if (value != null && !string.IsNullOrWhiteSpace(value.ToString()))
                {
                    completedFields++;
                }
            }

            return totalFields > 0 ? (double)completedFields / totalFields : 0.0;
        }

        private ResolutionStatistics CalculateResolutionStatistics(DuplicateResolutionResult result)
        {
            return new ResolutionStatistics
            {
                TotalGroups = result.ResolvedGroups.Count + result.Errors.Count,
                ResolvedGroups = result.ResolvedGroups.Count,
                ErrorGroups = result.Errors.Count,
                ActionCounts = result.ResolvedGroups.GroupBy(g => g.Action).ToDictionary(g => g.Key, g => g.Count()),
                EntitiesKept = result.ResolvedGroups.Count(g => g.ResolvedEntity != null),
                EntitiesDiscarded = result.ResolvedGroups.Sum(g => g.DiscardedEntities.Count),
                EntitiesMerged = result.ResolvedGroups.Count(g => g.Action == ResolutionAction.Merged)
            };
        }
    }
}
