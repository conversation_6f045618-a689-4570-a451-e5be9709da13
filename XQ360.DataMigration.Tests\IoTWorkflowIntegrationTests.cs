using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Integration tests for the complete IoT workflow functionality
    /// Tests integration between IoTDeviceCreationService, TempTableApiOrchestrationService,
    /// and XQ360ApiClient for end-to-end IoT device creation and vehicle linking
    /// </summary>
    public class IoTWorkflowIntegrationTests : IDisposable
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly Mock<XQ360ApiClient> _mockApiClient;
        private readonly BulkSeederConfiguration _testConfig;
        private readonly MigrationConfiguration _testMigrationConfig;

        public IoTWorkflowIntegrationTests()
        {
            _mockApiClient = new Mock<XQ360ApiClient>();

            _testConfig = new BulkSeederConfiguration
            {
                DefaultBatchSize = 50,
                MaxRetryAttempts = 3,
                RetryDelaySeconds = 1
            };

            _testMigrationConfig = new MigrationConfiguration
            {
                DatabaseConnection = "Server=test;Database=test;Integrated Security=true;",
                ApiBaseUrl = "https://test-api.example.com",
                ApiUsername = "testuser",
                ApiPassword = "testpass"
            };

            _serviceProvider = CreateServiceProvider();
        }

        #region Service Integration Tests

        [Fact]
        public void ServiceProvider_ShouldResolveAllIoTServices()
        {
            // Act & Assert
            Assert.NotNull(_serviceProvider.GetRequiredService<IIoTDeviceCreationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>());
            Assert.NotNull(_serviceProvider.GetRequiredService<XQ360ApiClient>());
        }

        [Fact]
        public async Task IoTDeviceCreationService_ShouldIntegrateWithApiClient()
        {
            // Arrange
            var iotService = _serviceProvider.GetRequiredService<IIoTDeviceCreationService>();
            var requests = CreateTestIoTDeviceRequests(3);

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulModuleCreation();

            // Act
            var result = await iotService.CreateIoTDeviceBatchAsync(requests, batchSize: 10);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.TotalRequests);

            // Verify API client was called
            _mockApiClient.Verify(x => x.TestAuthenticationAsync(), Times.AtLeastOnce);
        }

        [Fact]
        public async Task EnhancedApiOrchestrationService_ShouldIntegrateWithIoTDeviceService()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var moduleRequests = CreateTestIoTDeviceRequests(2);
            var vehicleRequests = CreateTestVehicleRequests(2);

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulModuleCreation();
            SetupSuccessfulSync();

            // Act
            var result = await orchestrationService.CreateCompleteIoTWorkflowAsync(
                moduleRequests, vehicleRequests, batchSize: 10);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.TotalModuleRequests);
            Assert.Equal(2, result.TotalVehicleRequests);

            // Verify all phases were executed
            Assert.NotNull(result.ModuleCreationResult);
            Assert.True(result.VehicleCreationSuccess);
            Assert.NotNull(result.SyncResult);
        }

        #endregion

        #region End-to-End Workflow Tests

        [Fact]
        public async Task CompleteIoTWorkflow_WithValidData_ShouldExecuteAllPhases()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var moduleRequests = CreateTestIoTDeviceRequests(5);
            var vehicleRequests = CreateTestVehicleRequests(5);

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulModuleCreation();
            SetupSuccessfulSync();

            // Act
            var result = await orchestrationService.CreateCompleteIoTWorkflowAsync(
                moduleRequests, vehicleRequests, batchSize: 3);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(5, result.TotalModuleRequests);
            Assert.Equal(5, result.TotalVehicleRequests);

            // Verify Phase 1: Module Creation
            Assert.NotNull(result.ModuleCreationResult);
            Assert.True(result.ModuleCreationResult.Success);

            // Verify Phase 2: Vehicle Creation
            Assert.True(result.VehicleCreationSuccess);

            // Verify Phase 3: IoT Sync
            Assert.NotNull(result.SyncResult);
            Assert.True(result.SyncResult.Success);

            // Verify API calls were made in correct order
            _mockApiClient.Verify(x => x.TestAuthenticationAsync(), Times.AtLeastOnce);
            _mockApiClient.Verify(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()), Times.AtLeastOnce);
            _mockApiClient.Verify(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()), Times.AtLeastOnce);
        }

        [Fact]
        public async Task CompleteIoTWorkflow_WithApiFailure_ShouldHandleGracefully()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var moduleRequests = CreateTestIoTDeviceRequests(2);
            var vehicleRequests = CreateTestVehicleRequests(2);

            // Setup authentication failure
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(false);
            _mockApiClient.Setup(x => x.AuthenticateAsync()).ReturnsAsync(false);

            // Act
            var result = await orchestrationService.CreateCompleteIoTWorkflowAsync(
                moduleRequests, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("Phase 1 failed", result.Summary);

            // Verify subsequent phases were not executed
            _mockApiClient.Verify(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task CompleteIoTWorkflow_WithPartialFailures_ShouldContinueProcessing()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var moduleRequests = CreateTestIoTDeviceRequests(3);
            var vehicleRequests = CreateTestVehicleRequests(3);

            SetupSuccessfulApiAuthentication();
            SetupPartialModuleCreationFailures();
            SetupSuccessfulSync();

            // Act
            var result = await orchestrationService.CreateCompleteIoTWorkflowAsync(
                moduleRequests, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            // Should continue to sync phase even with partial module creation failures
            Assert.NotNull(result.ModuleCreationResult);
            Assert.NotNull(result.SyncResult);
        }

        [Fact]
        public async Task CompleteIoTWorkflow_WithLargeDataSet_ShouldProcessInBatches()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var moduleRequests = CreateTestIoTDeviceRequests(100);
            var vehicleRequests = CreateTestVehicleRequests(100);

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulModuleCreation();
            SetupSuccessfulSync();

            // Act
            var result = await orchestrationService.CreateCompleteIoTWorkflowAsync(
                moduleRequests, vehicleRequests, batchSize: 10);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(100, result.TotalModuleRequests);
            Assert.Equal(100, result.TotalVehicleRequests);

            // Verify multiple API calls were made (due to batching)
            _mockApiClient.Verify(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()), Times.AtLeastOnce);
        }

        #endregion

        #region Performance and Reliability Tests

        [Fact]
        public async Task CompleteIoTWorkflow_WithConcurrentRequests_ShouldHandleCorrectly()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var tasks = new List<Task<CompleteIoTWorkflowResult>>();

            SetupSuccessfulApiAuthentication();
            SetupSuccessfulModuleCreation();
            SetupSuccessfulSync();

            // Act - Execute multiple workflows concurrently
            for (int i = 0; i < 3; i++)
            {
                var moduleRequests = CreateTestIoTDeviceRequests(2, startIndex: i * 10);
                var vehicleRequests = CreateTestVehicleRequests(2, startIndex: i * 10);

                tasks.Add(orchestrationService.CreateCompleteIoTWorkflowAsync(
                    moduleRequests, vehicleRequests, batchSize: 5));
            }

            var results = await Task.WhenAll(tasks);

            // Assert
            Assert.All(results, result =>
            {
                Assert.NotNull(result);
                Assert.True(result.Success);
                Assert.Equal(2, result.TotalModuleRequests);
                Assert.Equal(2, result.TotalVehicleRequests);
            });
        }

        [Fact]
        public async Task CompleteIoTWorkflow_WithTimeout_ShouldHandleGracefully()
        {
            // Arrange
            var orchestrationService = _serviceProvider.GetRequiredService<IEnhancedApiOrchestrationService>();
            var moduleRequests = CreateTestIoTDeviceRequests(2);
            var vehicleRequests = CreateTestVehicleRequests(2);
            var cancellationTokenSource = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));

            SetupSuccessfulApiAuthentication();
            SetupSlowModuleCreation(); // Simulate slow API

            // Act & Assert
            await Assert.ThrowsAnyAsync<OperationCanceledException>(() =>
                orchestrationService.CreateCompleteIoTWorkflowAsync(
                    moduleRequests, vehicleRequests, cancellationToken: cancellationTokenSource.Token));
        }

        #endregion

        #region Helper Methods

        private ServiceProvider CreateServiceProvider()
        {
            var services = new ServiceCollection();

            // Register logging
            services.AddLogging(builder => builder.AddConsole());

            // Register configuration
            services.AddSingleton(Options.Create(_testConfig));
            services.AddSingleton(Options.Create(_testMigrationConfig));

            // Register mocked API client
            services.AddSingleton(_mockApiClient.Object);

            // Register environment service
            var mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();
            mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(_testMigrationConfig);
            services.AddSingleton(mockEnvironmentService.Object);

            // Register temp staging service
            var mockTempStagingService = new Mock<ITempStagingService>();
            services.AddSingleton(mockTempStagingService.Object);

            // Register base API orchestration service
            var mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            services.AddSingleton(mockApiOrchestrationService.Object);

            // Register IoT services
            services.AddScoped<IIoTDeviceCreationService, IoTDeviceCreationService>();
            services.AddScoped<IEnhancedApiOrchestrationService, TempTableApiOrchestrationService>();

            return services.BuildServiceProvider();
        }

        private List<IoTDeviceCreateRequest> CreateTestIoTDeviceRequests(int count, int startIndex = 0)
        {
            var requests = new List<IoTDeviceCreateRequest>();
            for (int i = 1; i <= count; i++)
            {
                var index = startIndex + i;
                requests.Add(new IoTDeviceCreateRequest
                {
                    IoTDeviceID = $"TEST_IOT_{index:D3}",
                    Dealer = "Test Dealer",
                    CCID = 1000 + index,
                    RANumber = 2000 + index,
                    TechNumber = 3000 + index
                });
            }
            return requests;
        }

        private List<VehicleCreateRequest> CreateTestVehicleRequests(int count, int startIndex = 0)
        {
            var requests = new List<VehicleCreateRequest>();
            for (int i = 1; i <= count; i++)
            {
                var index = startIndex + i;
                requests.Add(new VehicleCreateRequest
                {
                    SerialNo = $"TEST_VEH_{index:D3}",
                    HireNo = $"HIRE_{index:D3}",
                    IdleTimer = 300,
                    OnHire = true,
                    ImpactLockout = false,
                    TimeoutEnabled = true,
                    IsCanbus = i % 2 == 0,
                    ModelId = Guid.NewGuid(),
                    SiteId = Guid.NewGuid(),
                    DepartmentId = Guid.NewGuid(),
                    CustomerId = Guid.NewGuid(),
                    ModuleIoTDevice = $"TEST_IOT_{index:D3}",
                    ChecklistType = ChecklistType.TimeBased
                });
            }
            return requests;
        }

        private void SetupSuccessfulApiAuthentication()
        {
            _mockApiClient.Setup(x => x.TestAuthenticationAsync()).ReturnsAsync(true);
        }

        private void SetupSuccessfulModuleCreation()
        {
            var apiResponse = new IoTDeviceApiResponse
            {
                InternalObjectId = 1,
                PrimaryKey = Guid.NewGuid().ToString(),
                ObjectsDataSet = new object()
            };

            _mockApiClient.Setup(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()))
                .ReturnsAsync(new ApiResult<IoTDeviceApiResponse> { Success = true, Data = apiResponse });
        }

        private void SetupSuccessfulSync()
        {
            _mockApiClient.Setup(x => x.PostFormAsync<object>("iothubmanager/syncvehiclesettings", It.IsAny<Dictionary<string, string>>()))
                .ReturnsAsync(new ApiResult<object> { Success = true, Data = new object() });
        }

        private void SetupPartialModuleCreationFailures()
        {
            var callCount = 0;
            _mockApiClient.Setup(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()))
                .Returns(() =>
                {
                    callCount++;
                    if (callCount % 2 == 0) // Every second call fails
                    {
                        return Task.FromResult(new ApiResult<IoTDeviceApiResponse> { Success = false, ErrorMessage = "Module creation failed" });
                    }

                    var apiResponse = new IoTDeviceApiResponse
                    {
                        InternalObjectId = 1,
                        PrimaryKey = Guid.NewGuid().ToString(),
                        ObjectsDataSet = new object()
                    };
                    return Task.FromResult(new ApiResult<IoTDeviceApiResponse> { Success = true, Data = apiResponse });
                });
        }

        private void SetupSlowModuleCreation()
        {
            _mockApiClient.Setup(x => x.PostFormAsync<IoTDeviceApiResponse>("module", It.IsAny<Dictionary<string, string>>()))
                .Returns(async () =>
                {
                    await Task.Delay(1000); // Simulate slow API
                    var apiResponse = new IoTDeviceApiResponse
                    {
                        InternalObjectId = 1,
                        PrimaryKey = Guid.NewGuid().ToString(),
                        ObjectsDataSet = new object()
                    };
                    return new ApiResult<IoTDeviceApiResponse> { Success = true, Data = apiResponse };
                });
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }

        #endregion
    }
}
