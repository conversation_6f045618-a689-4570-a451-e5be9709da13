using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.DataSeeder.Tests.Mocks;

namespace XQ360.DataMigration.DataSeeder.Tests
{
    /// <summary>
    /// Comprehensive unit tests for SqlDataGenerationService
    /// Tests data generation methods, staging operations, and data processing functionality
    /// Uses mocked database connections - does not require actual database infrastructure
    /// </summary>
    [Trait("Category", TestCategories.Unit)]
    public class SqlDataGenerationServiceTests : IDisposable
    {
        private readonly Mock<ILogger<SqlDataGenerationService>> _mockLogger;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockOptions;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly Mock<IDatabaseConnectionFactory> _mockConnectionFactory;
        private readonly BulkSeederConfiguration _testConfig;

        public SqlDataGenerationServiceTests()
        {
            _mockLogger = new Mock<ILogger<SqlDataGenerationService>>();
            _mockOptions = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();
            _mockConnectionFactory = new Mock<IDatabaseConnectionFactory>();

            _testConfig = TestConfigurationHelper.GetBulkSeederConfiguration();
            _mockOptions.Setup(x => x.Value).Returns(_testConfig);

            // Setup environment service
            var testEnvironment = TestConfigurationHelper.GetTestEnvironment();
            var testMigrationConfig = TestConfigurationHelper.GetTestConfiguration();
            _mockEnvironmentService.Setup(x => x.CurrentEnvironment).Returns(testEnvironment);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(testMigrationConfig);

            // Setup mock database connection factory
            _mockConnectionFactory.Setup(x => x.CreateConnection())
                .Returns(() => new MockDatabaseConnection());
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act
            var service = CreateSqlDataGenerationService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SqlDataGenerationService(
                null!,
                _mockOptions.Object,
                _mockEnvironmentService.Object,
                _mockConnectionFactory.Object));
        }

        [Fact]
        public void Constructor_WithNullOptions_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SqlDataGenerationService(
                _mockLogger.Object,
                null!,
                _mockEnvironmentService.Object,
                _mockConnectionFactory.Object));
        }

        [Fact]
        public void Constructor_WithNullEnvironmentService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SqlDataGenerationService(
                _mockLogger.Object,
                _mockOptions.Object,
                null!,
                _mockConnectionFactory.Object));
        }

        [Fact]
        public void Constructor_WithNullConnectionFactory_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new SqlDataGenerationService(
                _mockLogger.Object,
                _mockOptions.Object,
                _mockEnvironmentService.Object,
                null!));
        }

        #endregion

        #region GenerateDriverDataAsync Tests

        [Fact]
        public async Task GenerateDriverDataAsync_WithValidParameters_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 100;

            // Act
            var result = await service.GenerateDriverDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success); // Should succeed with mocked database connection
            Assert.True(result.GeneratedRows > 0); // Mock should return some generated rows
        }

        [Fact]
        public async Task GenerateDriverDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 0;

            // Act
            var result = await service.GenerateDriverDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(0, result.GeneratedRows);
            Assert.Contains("No driver records to generate", result.Summary);
        }

        [Fact]
        public async Task GenerateDriverDataAsync_WithNegativeCount_ShouldThrowArgumentException()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = -1;

            // Act
            var result = await service.GenerateDriverDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("negative", string.Join(" ", result.Errors).ToLower());
        }

        [Fact]
        public async Task GenerateDriverDataAsync_WithCancellationToken_ShouldRespectCancellation()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 1000;
            var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.Cancel(); // Cancel immediately

            // Act & Assert
            // Service should throw OperationCanceledException when cancellation token is already cancelled
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                service.GenerateDriverDataAsync(sessionId, count, cancellationTokenSource.Token));
        }

        #endregion

        #region GenerateVehicleDataAsync Tests

        [Fact]
        public async Task GenerateVehicleDataAsync_WithValidParameters_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 50;

            // Act
            var result = await service.GenerateVehicleDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success); // Should succeed with mocked database connection
            Assert.True(result.GeneratedRows > 0); // Mock should return some generated rows
        }

        [Fact]
        public async Task GenerateVehicleDataAsync_WithZeroCount_ShouldReturnSuccessWithZeroRows()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 0;

            // Act
            var result = await service.GenerateVehicleDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(0, result.GeneratedRows);
            Assert.Contains("No vehicle records to generate", result.Summary);
        }

        [Fact]
        public async Task GenerateVehicleDataAsync_WithNegativeCount_ShouldThrowArgumentException()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = -5;

            // Act
            var result = await service.GenerateVehicleDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Contains("negative", string.Join(" ", result.Errors).ToLower());
        }

        #endregion

        #region ValidateStagedDataAsync Tests

        [Fact]
        public async Task ValidateStagedDataAsync_WithValidSessionId_ShouldReturnValidationResult()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();

            // Act
            var result = await service.ValidateStagedDataAsync(sessionId);

            // Assert
            Assert.NotNull(result);
            // May succeed or fail depending on database availability
        }

        [Fact]
        public async Task ValidateStagedDataAsync_WithEmptyGuid_ShouldReturnValidationResult()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.Empty;

            // Act
            var result = await service.ValidateStagedDataAsync(sessionId);

            // Assert
            Assert.NotNull(result);
            // The service doesn't validate sessionId parameter - it attempts validation
        }

        #endregion

        #region ProcessStagedDataAsync Tests

        [Fact]
        public async Task ProcessStagedDataAsync_WithValidParameters_ShouldReturnProcessingResult()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var dryRun = true;

            // Act
            var result = await service.ProcessStagedDataAsync(sessionId, dryRun);

            // Assert
            Assert.NotNull(result);
            // May succeed or fail depending on database availability
        }

        [Fact]
        public async Task ProcessStagedDataAsync_WithEmptyGuid_ShouldReturnProcessingResult()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.Empty;

            // Act
            var result = await service.ProcessStagedDataAsync(sessionId, false);

            // Assert
            Assert.NotNull(result);
            // The service doesn't validate sessionId parameter - it attempts processing
        }

        [Fact]
        public async Task ProcessStagedDataAsync_WithCancellationToken_ShouldRespectCancellation()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.Cancel();

            // Act & Assert
            // Service should throw OperationCanceledException when cancellation token is already cancelled
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                service.ProcessStagedDataAsync(sessionId, false, cancellationTokenSource.Token));
        }

        #endregion

        #region Edge Cases and Error Handling Tests

        [Fact]
        public async Task GenerateDriverDataAsync_WithLargeCount_ShouldHandleCorrectly()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 100000; // Large count to test performance considerations

            // Act
            var result = await service.GenerateDriverDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            // Should handle large counts gracefully with mocked database connection
            Assert.True(result.Success);
        }

        [Fact]
        public async Task GenerateVehicleDataAsync_WithLargeCount_ShouldHandleCorrectly()
        {
            // Arrange
            var service = CreateSqlDataGenerationService();
            var sessionId = Guid.NewGuid();
            var count = 50000;

            // Act
            var result = await service.GenerateVehicleDataAsync(sessionId, count);

            // Assert
            Assert.NotNull(result);
            // Should handle large counts gracefully with mocked database connection
            Assert.True(result.Success);
        }

        #endregion

        #region Helper Methods

        private SqlDataGenerationService CreateSqlDataGenerationService()
        {
            return new SqlDataGenerationService(
                _mockLogger.Object,
                _mockOptions.Object,
                _mockEnvironmentService.Object,
                _mockConnectionFactory.Object);
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
