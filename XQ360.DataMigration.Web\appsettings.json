{"Migration": {"Environments": {"US": {"Name": "United States Production", "Description": "US Production Environment", "DatabaseConnection": "Server=us-fleetxqdb.database.windows.net,1433;Database=FleetXQ.US.Production;User Id=us-fleetxqdb;Password=YOUR_US_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://us-api.xq360.com/", "ApiUsername": "us-migration-user", "ApiPassword": "YOUR_US_API_PASSWORD"}, "UK": {"Name": "United Kingdom Production", "Description": "UK Production Environment", "DatabaseConnection": "Server=uk-fleetxqdb.database.windows.net,1433;Database=FleetXQ.UK.Production;User Id=uk-fleetxqdb;Password=YOUR_UK_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://uk-api.xq360.com/", "ApiUsername": "uk-migration-user", "ApiPassword": "YOUR_UK_API_PASSWORD"}, "AU": {"Name": "Australia Production", "Description": "AU Production Environment", "DatabaseConnection": "Server=au-fleetxqdb.database.windows.net,1433;Database=FleetXQ.AU.Production;User Id=au-fleetxqdb;Password=YOUR_AU_PASSWORD;Connection Timeout=30;Encrypt=true;TrustServerCertificate=false", "ApiBaseUrl": "https://au-api.xq360.com/", "ApiUsername": "au-migration-user", "ApiPassword": "YOUR_AU_API_PASSWORD"}, "Pilot": {"Name": "Pilot Testing Environment", "Description": "Pilot testing and validation environment", "DatabaseConnection": "Server=fleetxqdb.database.windows.net,1433;Database=fleetXQ.Application-8735218d-3aeb-4563-bccb-8cdfcdf1188f;User Id=fleetxqdb;Password=*"wUvWCEKUxaHZK)6w*";TrustServerCertificate=true;", "ApiBaseUrl": "https://godev.collectiveintelligence.com.au/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/", "ApiUsername": "Admin", "ApiPassword": "Admin"}, "Development": {"Name": "Development Environment", "Description": "Development and testing environment", "DatabaseConnection": "Server=DESKTOP-LJEH13I**SQLEXPRESS;Database=dev07281;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True", "ApiBaseUrl": "https://localhost:53052/", "ApiUsername": "Admin", "ApiPassword": "Admin"}}, "BatchSize": 100, "MaxRetryAttempts": 3, "BackupEnabled": true, "ValidateBeforeMigration": true, "ContinueOnError": false}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "./Logs/developer-migration-{Date}.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}]}, "Reporting": {"GenerateUserReports": true, "UserReportDirectory": "./Reports", "IncludeDetailedRecords": true, "MaxRecordsPerErrorType": 50}, "Authentication": {"BypassEnabled": true}, "BulkSeeder": {"DefaultDriversCount": 10000, "DefaultVehiclesCount": 5000, "DefaultBatchSize": 1000, "MaxBatchSize": 50000, "BulkCopyTimeout": 300, "CommandTimeout": 120, "NotifyAfter": 1000, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": false, "CleanupStagingData": true, "UseTempTables": true, "TempTableMode": "SessionScoped", "TempTableBatchSize": 5000, "TempTableIndexes": true, "LogTempTableOperations": true}, "Observability": {"Logging": {"Enabled": true, "EnableConsoleLogging": false, "EnableFileLogging": false, "EnableStructuredLogging": false, "EnablePerformanceLogging": false, "EnableErrorLogging": false, "EnableDebugLogging": false}, "Monitoring": {"Enabled": false, "EnablePerformanceMonitoring": false, "EnableResourceMonitoring": false, "EnableDatabaseMonitoring": false, "EnableOperationMonitoring": false, "CollectionIntervalSeconds": 5}, "Metrics": {"Enabled": false, "EnableThroughputMetrics": false, "EnableDurationMetrics": false, "EnableErrorRateMetrics": false, "EnableSuccessRateMetrics": false, "MaxHistorySize": 10000}, "HealthChecks": {"Enabled": false, "EnableDatabaseHealthChecks": false, "EnableSystemHealthChecks": false, "EnableServiceHealthChecks": false, "EnableNetworkHealthChecks": false, "CheckIntervalSeconds": 30}, "Tracing": {"Enabled": false, "EnableDistributedTracing": false, "EnableOperationTracing": false, "EnableDatabaseTracing": false, "SamplingRate": 0.1}, "Alerting": {"Enabled": true, "EnablePerformanceAlerts": false, "EnableErrorAlerts": true, "EnableResourceAlerts": false, "EnableAutoResolution": false, "EnableEscalation": false, "EnableDeduplication": false}, "Audit": {"Enabled": false, "EnableOperationAuditing": false, "EnableDataChangeAuditing": false, "EnableSecurityAuditing": false, "EnableAutomaticCleanup": false, "MaskSensitiveData": false, "DefaultRetentionPeriod": "90.00:00:00"}, "Reporting": {"Enabled": false, "EnableMigrationReports": false, "EnablePerformanceReports": false, "EnableErrorReports": false, "EnableDetailedRecordReports": false, "GenerateUserReports": false, "UserReportDirectory": "./Reports", "IncludeDetailedRecords": false, "MaxRecordsPerErrorType": 50}}}