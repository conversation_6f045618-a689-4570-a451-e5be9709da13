namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public interface IPerformanceMonitoringService
    {
        Task<PerformanceMetrics> GetCurrentMetricsAsync(CancellationToken cancellationToken = default);
        Task RecordOperationAsync(OperationMetrics operation, CancellationToken cancellationToken = default);
        Task<ThroughputReport> GetThroughputReportAsync(TimeSpan period, CancellationToken cancellationToken = default);
        Task<List<PerformanceAlert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);
        Task StartMonitoringSessionAsync(Guid sessionId, string operationType, CancellationToken cancellationToken = default);
        Task EndMonitoringSessionAsync(Guid sessionId, CancellationToken cancellationToken = default);
        Task<ResourceUtilizationReport> GetResourceUtilizationAsync(CancellationToken cancellationToken = default);
        Task<ErrorRateReport> GetErrorRateReportAsync(TimeSpan period, CancellationToken cancellationToken = default);
    }

    public class PerformanceMetrics
    {
        public DateTime Timestamp { get; set; }
        public double CpuUsagePercent { get; set; }
        public long MemoryUsageMB { get; set; }
        public double DiskUsagePercent { get; set; }
        public int ActiveConnections { get; set; }
        public int CacheHitRatio { get; set; }
        public double AverageResponseTimeMs { get; set; }
        public int RequestsPerSecond { get; set; }
        public int ErrorRate { get; set; }
        public Dictionary<string, object> CustomMetrics { get; set; } = new();
    }

    public class OperationMetrics
    {
        public Guid OperationId { get; set; }
        public Guid SessionId { get; set; }
        public string OperationType { get; set; } = string.Empty;
        public string OperationName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
        public bool Success { get; set; }
        public int RecordsProcessed { get; set; }
        public double ThroughputPerSecond => Duration.TotalSeconds > 0 ? RecordsProcessed / Duration.TotalSeconds : 0;
        public long MemoryUsedMB { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class ThroughputReport
    {
        public TimeSpan Period { get; set; }
        public double AverageThroughput { get; set; }
        public double PeakThroughput { get; set; }
        public double MinThroughput { get; set; }
        public List<ThroughputDataPoint> DataPoints { get; set; } = new();
        public Dictionary<string, double> ThroughputByOperationType { get; set; } = new();
    }

    public class ThroughputDataPoint
    {
        public DateTime Timestamp { get; set; }
        public double Throughput { get; set; }
        public string OperationType { get; set; } = string.Empty;
    }

    public class PerformanceAlert
    {
        public Guid AlertId { get; set; }
        public AlertType Type { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
    }

    public enum AlertType
    {
        HighCpuUsage,
        HighMemoryUsage,
        HighErrorRate,
        LowThroughput,
        DatabaseConnectionIssue,
        SlowResponse,
        ResourceExhaustion
    }

    public enum AlertSeverity
    {
        Info,
        Warning,
        Critical
    }

    public class ResourceUtilizationReport
    {
        public DateTime Timestamp { get; set; }
        public CpuUtilization Cpu { get; set; } = new();
        public MemoryUtilization Memory { get; set; } = new();
        public DiskUtilization Disk { get; set; } = new();
        public NetworkUtilization Network { get; set; } = new();
        public DatabaseUtilization Database { get; set; } = new();
    }

    public class CpuUtilization
    {
        public double CurrentPercent { get; set; }
        public double AveragePercent { get; set; }
        public double PeakPercent { get; set; }
        public int CoreCount { get; set; }
    }

    public class MemoryUtilization
    {
        public long TotalMB { get; set; }
        public long UsedMB { get; set; }
        public long AvailableMB { get; set; }
        public double UsagePercent => TotalMB > 0 ? (double)UsedMB / TotalMB * 100 : 0;
        public long GcCollections { get; set; }
        public long GcMemoryMB { get; set; }
    }

    public class DiskUtilization
    {
        public long TotalGB { get; set; }
        public long UsedGB { get; set; }
        public long AvailableGB { get; set; }
        public double UsagePercent => TotalGB > 0 ? (double)UsedGB / TotalGB * 100 : 0;
        public double ReadMBps { get; set; }
        public double WriteMBps { get; set; }
    }

    public class NetworkUtilization
    {
        public double InboundMBps { get; set; }
        public double OutboundMBps { get; set; }
        public int ActiveConnections { get; set; }
        public int ConnectionsPerSecond { get; set; }
    }

    public class DatabaseUtilization
    {
        public int ActiveConnections { get; set; }
        public int MaxConnections { get; set; }
        public double ConnectionPoolUsagePercent => MaxConnections > 0 ? (double)ActiveConnections / MaxConnections * 100 : 0;
        public double AverageQueryTimeMs { get; set; }
        public int QueriesPerSecond { get; set; }
        public long LockWaits { get; set; }
        public long Deadlocks { get; set; }
    }

    public class ErrorRateReport
    {
        public TimeSpan Period { get; set; }
        public double OverallErrorRate { get; set; }
        public Dictionary<string, double> ErrorRateByType { get; set; } = new();
        public List<ErrorTrend> ErrorTrends { get; set; } = new();
        public List<string> MostCommonErrors { get; set; } = new();
    }

    public class ErrorTrend
    {
        public DateTime Timestamp { get; set; }
        public double ErrorRate { get; set; }
        public int ErrorCount { get; set; }
        public int TotalOperations { get; set; }
    }
}
