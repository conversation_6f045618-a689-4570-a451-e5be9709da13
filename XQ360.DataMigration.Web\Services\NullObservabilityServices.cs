using XQ360.DataMigration.Web.Services.Monitoring;

namespace XQ360.DataMigration.Web.Services
{
    /// <summary>
    /// Null implementation of IPerformanceMonitoringService for when monitoring is disabled
    /// </summary>
    public class NullPerformanceMonitoringService : IPerformanceMonitoringService
    {
        public Task<PerformanceMetrics> GetCurrentMetricsAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new PerformanceMetrics());

        public Task RecordOperationAsync(OperationMetrics operation, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task<ThroughputReport> GetThroughputReportAsync(TimeSpan period, CancellationToken cancellationToken = default) =>
            Task.FromResult(new ThroughputReport { Period = period });

        public Task<List<PerformanceAlert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new List<PerformanceAlert>());

        public Task StartMonitoringSessionAsync(Guid sessionId, string operationType, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task EndMonitoringSessionAsync(Guid sessionId, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task<ResourceUtilizationReport> GetResourceUtilizationAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new ResourceUtilizationReport());

        public Task<ErrorRateReport> GetErrorRateReportAsync(TimeSpan period, CancellationToken cancellationToken = default) =>
            Task.FromResult(new ErrorRateReport { Period = period });
    }

    /// <summary>
    /// Null implementation of IAuditTrailService for when audit is disabled
    /// </summary>
    public class NullAuditTrailService : IAuditTrailService
    {
        public Task LogOperationAsync(AuditEntry entry, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task<List<AuditEntry>> GetAuditTrailAsync(AuditFilter filter, CancellationToken cancellationToken = default) =>
            Task.FromResult(new List<AuditEntry>());

        public Task<AuditSummary> GetAuditSummaryAsync(Guid sessionId, CancellationToken cancellationToken = default) =>
            Task.FromResult(new AuditSummary { SessionId = sessionId });

        public Task CleanupOldAuditEntriesAsync(TimeSpan retentionPeriod, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task<List<AuditEntry>> SearchAuditEntriesAsync(AuditSearchCriteria criteria, CancellationToken cancellationToken = default) =>
            Task.FromResult(new List<AuditEntry>());

        public Task ExportAuditTrailAsync(AuditFilter filter, string exportPath, AuditExportFormat format, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;
    }

    /// <summary>
    /// Null implementation of IHealthCheckService for when health checks are disabled
    /// </summary>
    public class NullHealthCheckService : IHealthCheckService
    {
        public Task<HealthCheckResult> GetSystemHealthAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new HealthCheckResult { Status = OverallHealthStatus.Healthy });

        public Task<DatabaseHealthResult> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new DatabaseHealthResult { Status = HealthStatus.Healthy });

        public Task<ApiHealthResult> CheckApiHealthAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new ApiHealthResult { Status = HealthStatus.Healthy });

        public Task<ResourceHealthResult> CheckResourceHealthAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new ResourceHealthResult { Status = HealthStatus.Healthy });

        public Task<ServiceHealthResult> CheckServiceHealthAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new ServiceHealthResult { Status = HealthStatus.Healthy });

        public Task<List<DiagnosticResult>> RunDiagnosticsAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new List<DiagnosticResult>());

        public Task<PerformanceReport> GeneratePerformanceReportAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new PerformanceReport());

        public Task<SystemConfiguration> GetSystemConfigurationAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new SystemConfiguration());
    }

    /// <summary>
    /// Null implementation of IAlertingService for when alerting is disabled
    /// </summary>
    public class NullAlertingService : IAlertingService
    {
        public Task CreateAlertAsync(Alert alert, CancellationToken cancellationToken = default) => Task.CompletedTask;

        public Task<List<Alert>> GetActiveAlertsAsync(AlertFilter? filter = null, CancellationToken cancellationToken = default) =>
            Task.FromResult(new List<Alert>());

        public Task<Alert?> GetAlertAsync(Guid alertId, CancellationToken cancellationToken = default) =>
            Task.FromResult<Alert?>(null);

        public Task ResolveAlertAsync(Guid alertId, string resolvedBy, string resolution, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task AcknowledgeAlertAsync(Guid alertId, string acknowledgedBy, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task EscalateAlertAsync(Guid alertId, AlertSeverity newSeverity, string escalatedBy, string reason, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task<AlertStatistics> GetAlertStatisticsAsync(TimeSpan period, CancellationToken cancellationToken = default) =>
            Task.FromResult(new AlertStatistics());

        public Task SendNotificationAsync(Notification notification, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task<List<NotificationChannel>> GetNotificationChannelsAsync(CancellationToken cancellationToken = default) =>
            Task.FromResult(new List<NotificationChannel>());

        public Task ConfigureNotificationRulesAsync(List<NotificationRule> rules, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;

        public Task TestNotificationChannelAsync(string channelId, CancellationToken cancellationToken = default) =>
            Task.CompletedTask;
    }
}
