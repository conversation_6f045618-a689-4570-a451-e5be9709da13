using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Web.Services.TransactionManagement
{
    /// <summary>
    /// Default implementation of granular rollback service with basic placeholder implementations
    /// </summary>
    public class DefaultGranularRollbackService : GranularRollbackService
    {
        public DefaultGranularRollbackService(
            ILogger<GranularRollbackService> logger,
            IOptions<MigrationConfiguration> config,
            IDistributedTransactionService transactionService)
            : base(logger, config, transactionService)
        {
        }

        protected override async Task PerformRollbackRecoveryAsync(RecoveryRequest request, RecoveryResult result, CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            throw new NotImplementedException("Rollback recovery strategy is not yet implemented");
        }

        protected override async Task PerformRepairRecoveryAsync(RecoveryRequest request, RecoveryResult result, CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            throw new NotImplementedException("Repair recovery strategy is not yet implemented");
        }

        protected override async Task PerformRecreateRecoveryAsync(RecoveryRequest request, RecoveryResult result, CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            throw new NotImplementedException("Recreate recovery strategy is not yet implemented");
        }

        protected override async Task PrepareManualRecoveryAsync(RecoveryRequest request, RecoveryResult result, CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            throw new NotImplementedException("Manual recovery preparation is not yet implemented");
        }
    }
}
