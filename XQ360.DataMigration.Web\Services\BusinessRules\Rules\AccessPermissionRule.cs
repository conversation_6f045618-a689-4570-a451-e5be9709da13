using Microsoft.Data.SqlClient;

namespace XQ360.DataMigration.Web.Services.BusinessRules.Rules
{
    /// <summary>
    /// Business rule for validating access permissions
    /// </summary>
    public class AccessPermissionRule : BusinessRuleBase
    {
        private readonly ILogger<AccessPermissionRule> _logger;

        public AccessPermissionRule(ILogger<AccessPermissionRule> logger)
        {
            _logger = logger;
        }

        public override string Id => "ACCESS_PERMISSION";
        public override string Name => "Access Permission Validation";
        public override string Description => "Validates access permission assignments including hierarchy, conflicts, and authorization levels";
        public override string Category => "Access Control";
        public override string[] ApplicableEntityTypes => new[] { "AccessPermissionRequest", "SiteVehicleNormalCardAccess", "DepartmentVehicleNormalCardAccess", "ModelVehicleNormalCardAccess", "PerVehicleNormalCardAccess" };

        public override async Task<ValidationResult> ValidateAsync(object entity, ValidationContext context, CancellationToken cancellationToken = default)
        {
            var issues = new List<ValidationIssue>();

            try
            {
                var request = ExtractAccessPermissionRequest(entity);
                if (request == null)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        "Invalid access permission request format",
                        "Entity",
                        entity?.GetType().Name,
                        "Ensure entity contains required access permission fields"));
                    return CreateFailureResult(issues.ToArray());
                }

                using var connection = new SqlConnection(context.ConnectionString);
                await connection.OpenAsync(cancellationToken);

                // Rule 1: Validate card exists and is active
                var cardValidation = await ValidateCardAsync(request.CardId, connection, cancellationToken);
                if (!cardValidation.IsValid)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Card validation failed: {cardValidation.Reason}",
                        "CardId",
                        request.CardId,
                        "Ensure card exists and is active"));
                }

                // Rule 2: Validate permission exists
                var permissionValidation = await ValidatePermissionAsync(request.PermissionId, connection, cancellationToken);
                if (!permissionValidation.IsValid)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Error,
                        $"Permission validation failed: {permissionValidation.Reason}",
                        "PermissionId",
                        request.PermissionId,
                        "Ensure permission ID is valid"));
                }

                // Rule 3: Validate access level hierarchy
                var hierarchyValidation = await ValidateAccessHierarchyAsync(request, connection, cancellationToken);
                if (!hierarchyValidation.IsValid)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Access hierarchy issue: {hierarchyValidation.Reason}",
                        "AccessLevel",
                        request.AccessLevel,
                        "Review access level assignments for consistency"));
                }

                // Rule 4: Check for conflicting permissions
                var conflictCheck = await CheckPermissionConflictsAsync(request, connection, cancellationToken);
                if (conflictCheck.HasConflicts)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        $"Potential permission conflicts detected: {conflictCheck.ConflictDescription}",
                        "AccessLevel",
                        request.AccessLevel,
                        "Review existing permissions to avoid conflicts"));
                }

                // Rule 5: Validate entity-specific requirements
                switch (request.AccessLevel.ToLower())
                {
                    case "site":
                        var siteValidation = await ValidateSiteAccessAsync(request, connection, cancellationToken);
                        if (!siteValidation.IsValid)
                        {
                            issues.Add(CreateIssue(ValidationSeverity.Error,
                                $"Site access validation failed: {siteValidation.Reason}",
                                "SiteId",
                                request.SiteId,
                                "Ensure site exists and is accessible"));
                        }
                        break;

                    case "department":
                        var deptValidation = await ValidateDepartmentAccessAsync(request, connection, cancellationToken);
                        if (!deptValidation.IsValid)
                        {
                            issues.Add(CreateIssue(ValidationSeverity.Error,
                                $"Department access validation failed: {deptValidation.Reason}",
                                "DepartmentId",
                                request.DepartmentId,
                                "Ensure department exists and is accessible"));
                        }
                        break;

                    case "model":
                        var modelValidation = await ValidateModelAccessAsync(request, connection, cancellationToken);
                        if (!modelValidation.IsValid)
                        {
                            issues.Add(CreateIssue(ValidationSeverity.Error,
                                $"Model access validation failed: {modelValidation.Reason}",
                                "ModelId",
                                request.ModelId,
                                "Ensure model exists and is accessible"));
                        }
                        break;

                    case "vehicle":
                        var vehicleValidation = await ValidateVehicleAccessAsync(request, connection, cancellationToken);
                        if (!vehicleValidation.IsValid)
                        {
                            issues.Add(CreateIssue(ValidationSeverity.Error,
                                $"Vehicle access validation failed: {vehicleValidation.Reason}",
                                "VehicleId",
                                request.VehicleId,
                                "Ensure vehicle exists and is accessible"));
                        }
                        break;
                }

                // Rule 6: Check for duplicate permissions
                var duplicateCheck = await CheckDuplicatePermissionsAsync(request, connection, cancellationToken);
                if (duplicateCheck.HasDuplicates)
                {
                    issues.Add(CreateIssue(ValidationSeverity.Warning,
                        "Duplicate permission already exists",
                        "AccessLevel",
                        request.AccessLevel,
                        "Permission may already be granted - verify if update is needed"));
                }

                return issues.Any(i => i.Severity == ValidationSeverity.Error)
                    ? CreateFailureResult(issues.ToArray())
                    : new ValidationResult { IsValid = true, Issues = issues };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating access permission");

                issues.Add(CreateIssue(ValidationSeverity.Error,
                    $"Validation error: {ex.Message}",
                    "System",
                    null,
                    "Contact system administrator"));

                return CreateFailureResult(issues.ToArray());
            }
        }

        private AccessPermissionRequest? ExtractAccessPermissionRequest(object entity)
        {
            if (entity is AccessPermissionRequest request)
                return request;

            // Try to extract from other entity types
            var cardId = GetPropertyValue(entity, "CardId");
            var siteId = GetPropertyValue(entity, "SiteId");
            var departmentId = GetPropertyValue(entity, "DepartmentId");
            var modelId = GetPropertyValue(entity, "ModelId");
            var vehicleId = GetPropertyValue(entity, "VehicleId");
            var permissionId = GetPropertyValue(entity, "PermissionId");

            if (cardId is Guid cardGuid && permissionId is Guid permGuid)
            {
                return new AccessPermissionRequest
                {
                    CardId = cardGuid,
                    SiteId = siteId as Guid?,
                    DepartmentId = departmentId as Guid?,
                    ModelId = modelId as Guid?,
                    VehicleId = vehicleId as Guid?,
                    PermissionId = permGuid,
                    AccessLevel = DetermineAccessLevel(siteId, departmentId, modelId, vehicleId)
                };
            }

            return null;
        }

        private string DetermineAccessLevel(object? siteId, object? departmentId, object? modelId, object? vehicleId)
        {
            if (vehicleId != null) return "vehicle";
            if (modelId != null) return "model";
            if (departmentId != null) return "department";
            if (siteId != null) return "site";
            return "unknown";
        }

        private async Task<(bool IsValid, string Reason)> ValidateCardAsync(Guid cardId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT IsActive, Status
                FROM [dbo].[Card]
                WHERE Id = @CardId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@CardId", cardId);

            using var reader = await command.ExecuteReaderAsync(cancellationToken);
            if (await reader.ReadAsync(cancellationToken))
            {
                var isActive = reader.GetBoolean(reader.GetOrdinal("IsActive"));
                var status = reader.IsDBNull(reader.GetOrdinal("Status")) ? null : reader.GetString(reader.GetOrdinal("Status"));

                if (!isActive)
                    return (false, "Card is inactive");

                if (!string.IsNullOrEmpty(status) && status.ToLower() != "active")
                    return (false, $"Card status is {status}");

                return (true, "Valid");
            }

            return (false, "Card not found");
        }

        private async Task<(bool IsValid, string Reason)> ValidatePermissionAsync(Guid permissionId, SqlConnection connection, CancellationToken cancellationToken)
        {
            const string sql = @"
                SELECT COUNT(*)
                FROM [dbo].[Permission]
                WHERE Id = @PermissionId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@PermissionId", permissionId);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Valid") : (false, "Permission not found");
        }

        private async Task<(bool IsValid, string Reason)> ValidateAccessHierarchyAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            // Validate that the access hierarchy makes sense
            // Site -> Department -> Model -> Vehicle
            
            if (request.AccessLevel.ToLower() == "department" && request.DepartmentId.HasValue && request.SiteId.HasValue)
            {
                const string sql = @"
                    SELECT SiteId
                    FROM [dbo].[Department]
                    WHERE Id = @DepartmentId";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@DepartmentId", request.DepartmentId.Value);

                var result = await command.ExecuteScalarAsync(cancellationToken);
                if (result != null && result != DBNull.Value)
                {
                    var actualSiteId = (Guid)result;
                    if (actualSiteId != request.SiteId.Value)
                        return (false, "Department does not belong to specified site");
                }
            }

            return (true, "Hierarchy is valid");
        }

        private async Task<(bool HasConflicts, string ConflictDescription)> CheckPermissionConflictsAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            // Check for conflicting permissions at different levels
            // This is a simplified check - in practice, this would be more sophisticated
            
            return (false, "No conflicts detected");
        }

        private async Task<(bool IsValid, string Reason)> ValidateSiteAccessAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            if (!request.SiteId.HasValue)
                return (false, "Site ID is required for site access");

            const string sql = @"
                SELECT COUNT(*)
                FROM [dbo].[Site]
                WHERE Id = @SiteId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@SiteId", request.SiteId.Value);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Valid") : (false, "Site not found");
        }

        private async Task<(bool IsValid, string Reason)> ValidateDepartmentAccessAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            if (!request.DepartmentId.HasValue)
                return (false, "Department ID is required for department access");

            const string sql = @"
                SELECT COUNT(*)
                FROM [dbo].[Department]
                WHERE Id = @DepartmentId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", request.DepartmentId.Value);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Valid") : (false, "Department not found");
        }

        private async Task<(bool IsValid, string Reason)> ValidateModelAccessAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            if (!request.ModelId.HasValue)
                return (false, "Model ID is required for model access");

            const string sql = @"
                SELECT COUNT(*)
                FROM [dbo].[Model]
                WHERE Id = @ModelId";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@ModelId", request.ModelId.Value);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Valid") : (false, "Model not found");
        }

        private async Task<(bool IsValid, string Reason)> ValidateVehicleAccessAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            if (!request.VehicleId.HasValue)
                return (false, "Vehicle ID is required for vehicle access");

            const string sql = @"
                SELECT COUNT(*)
                FROM [dbo].[Vehicle]
                WHERE Id = @VehicleId AND IsActive = 1";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@VehicleId", request.VehicleId.Value);

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Valid") : (false, "Vehicle not found or inactive");
        }

        private async Task<(bool HasDuplicates, string Description)> CheckDuplicatePermissionsAsync(AccessPermissionRequest request, SqlConnection connection, CancellationToken cancellationToken)
        {
            string sql = request.AccessLevel.ToLower() switch
            {
                "site" => @"
                    SELECT COUNT(*)
                    FROM [dbo].[SiteVehicleNormalCardAccess]
                    WHERE SiteId = @SiteId AND CardId = @CardId AND PermissionId = @PermissionId",
                "department" => @"
                    SELECT COUNT(*)
                    FROM [dbo].[DepartmentVehicleNormalCardAccess]
                    WHERE DepartmentId = @DepartmentId AND CardId = @CardId AND PermissionId = @PermissionId",
                "model" => @"
                    SELECT COUNT(*)
                    FROM [dbo].[ModelVehicleNormalCardAccess]
                    WHERE ModelId = @ModelId AND CardId = @CardId AND PermissionId = @PermissionId",
                "vehicle" => @"
                    SELECT COUNT(*)
                    FROM [dbo].[PerVehicleNormalCardAccess]
                    WHERE VehicleId = @VehicleId AND CardId = @CardId AND PermissionId = @PermissionId",
                _ => null
            };

            if (sql == null)
                return (false, "Unknown access level");

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@CardId", request.CardId);
            command.Parameters.AddWithValue("@PermissionId", request.PermissionId);

            switch (request.AccessLevel.ToLower())
            {
                case "site":
                    command.Parameters.AddWithValue("@SiteId", request.SiteId ?? Guid.Empty);
                    break;
                case "department":
                    command.Parameters.AddWithValue("@DepartmentId", request.DepartmentId ?? Guid.Empty);
                    break;
                case "model":
                    command.Parameters.AddWithValue("@ModelId", request.ModelId ?? Guid.Empty);
                    break;
                case "vehicle":
                    command.Parameters.AddWithValue("@VehicleId", request.VehicleId ?? Guid.Empty);
                    break;
            }

            var count = (int)await command.ExecuteScalarAsync(cancellationToken);
            return count > 0 ? (true, "Duplicate permission exists") : (false, "No duplicates");
        }
    }
}
