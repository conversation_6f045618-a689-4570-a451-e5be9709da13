using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Text.Json;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.Monitoring
{
    public class AlertingService : IAlertingService
    {
        private readonly ILogger<AlertingService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly AlertConfiguration _alertConfig;
        private readonly ConcurrentDictionary<string, NotificationChannel> _channels = new();
        private readonly ConcurrentDictionary<Guid, NotificationRule> _notificationRules = new();
        private readonly ConcurrentQueue<Alert> _recentAlerts = new();
        private readonly Timer _alertProcessingTimer;
        private readonly Timer _escalationTimer;

        public AlertingService(
            ILogger<AlertingService> logger,
            IServiceScopeFactory scopeFactory,
            IOptions<AlertConfiguration> alertConfig)
        {
            _logger = logger;
            _scopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));
            _alertConfig = alertConfig.Value;

            InitializeNotificationChannels();
            InitializeDefaultNotificationRules();
            InitializeAlertTableAsync().ConfigureAwait(false);

            // Start background processing timers
            _alertProcessingTimer = new Timer(ProcessPendingAlerts, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
            _escalationTimer = new Timer(ProcessEscalations, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

            _logger.LogInformation("Alerting service initialized with {ChannelCount} notification channels", _channels.Count);
        }

        /// <summary>
        /// Helper method to get MigrationConfiguration using scoped service
        /// </summary>
        private MigrationConfiguration GetMigrationConfiguration()
        {
            using var scope = _scopeFactory.CreateScope();
            var environmentService = scope.ServiceProvider.GetRequiredService<IEnvironmentConfigurationService>();
            return environmentService.CurrentMigrationConfiguration;
        }

        /// <summary>
        /// Helper method to get database connection string using scoped service
        /// </summary>
        private string GetConnectionString()
        {
            return GetMigrationConfiguration().DatabaseConnection;
        }

        public async Task CreateAlertAsync(Alert alert, CancellationToken cancellationToken = default)
        {
            try
            {
                alert.AlertId = alert.AlertId == Guid.Empty ? Guid.NewGuid() : alert.AlertId;
                alert.CreatedAt = DateTime.UtcNow;

                // Check for deduplication
                if (_alertConfig.EnableDeduplication)
                {
                    var duplicate = await CheckForDuplicateAlertAsync(alert, cancellationToken);
                    if (duplicate != null)
                    {
                        _logger.LogDebug("Duplicate alert detected, updating existing alert {AlertId}", duplicate.AlertId);
                        await UpdateExistingAlertAsync(duplicate, alert, cancellationToken);
                        return;
                    }
                }

                // Apply severity configuration
                ApplySeverityConfiguration(alert);

                // Add initial action
                alert.Actions.Add(new AlertAction
                {
                    ActionId = Guid.NewGuid(),
                    ActionType = AlertActionType.Created,
                    PerformedBy = "System",
                    Description = "Alert created"
                });

                // Store alert in database
                await StoreAlertAsync(alert, cancellationToken);

                // Add to recent alerts for deduplication
                _recentAlerts.Enqueue(alert);
                while (_recentAlerts.Count > 1000)
                {
                    _recentAlerts.TryDequeue(out _);
                }

                // Send notifications based on rules
                await ProcessNotificationRulesAsync(alert, cancellationToken);

                _logger.LogInformation("Alert created: {AlertType} - {Title} (Severity: {Severity})",
                    alert.AlertType, alert.Title, alert.Severity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create alert: {AlertType} - {Title}", alert.AlertType, alert.Title);
                throw;
            }
        }

        public async Task<List<Alert>> GetActiveAlertsAsync(AlertFilter? filter = null, CancellationToken cancellationToken = default)
        {
            try
            {
                using var connection = new SqlConnection(GetConnectionString());
                await connection.OpenAsync(cancellationToken);

                var sql = BuildAlertsQuery(filter);
                using var cmd = new SqlCommand(sql, connection);
                AddAlertFilterParameters(cmd, filter);

                var alerts = new List<Alert>();
                using var reader = await cmd.ExecuteReaderAsync(cancellationToken);

                while (await reader.ReadAsync(cancellationToken))
                {
                    alerts.Add(ReadAlertFromReader(reader));
                }

                _logger.LogDebug("Retrieved {Count} alerts", alerts.Count);
                return alerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve alerts");
                throw;
            }
        }

        public async Task<Alert?> GetAlertAsync(Guid alertId, CancellationToken cancellationToken = default)
        {
            try
            {
                using var connection = new SqlConnection(GetConnectionString());
                await connection.OpenAsync(cancellationToken);

                var sql = "SELECT * FROM Alerts WHERE AlertId = @AlertId";
                using var cmd = new SqlCommand(sql, connection);
                cmd.Parameters.AddWithValue("@AlertId", alertId);

                using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
                if (await reader.ReadAsync(cancellationToken))
                {
                    return ReadAlertFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve alert {AlertId}", alertId);
                throw;
            }
        }

        public async Task ResolveAlertAsync(Guid alertId, string resolvedBy, string resolution, CancellationToken cancellationToken = default)
        {
            try
            {
                var alert = await GetAlertAsync(alertId, cancellationToken);
                if (alert == null)
                {
                    _logger.LogWarning("Cannot resolve alert {AlertId} - alert not found", alertId);
                    return;
                }

                if (alert.Status == AlertStatus.Resolved)
                {
                    _logger.LogWarning("Alert {AlertId} is already resolved", alertId);
                    return;
                }

                alert.Status = AlertStatus.Resolved;
                alert.ResolvedAt = DateTime.UtcNow;
                alert.ResolvedBy = resolvedBy;
                alert.Resolution = resolution;

                alert.Actions.Add(new AlertAction
                {
                    ActionId = Guid.NewGuid(),
                    ActionType = AlertActionType.Resolved,
                    PerformedBy = resolvedBy,
                    Description = $"Alert resolved: {resolution}"
                });

                await UpdateAlertAsync(alert, cancellationToken);

                // Send resolution notification
                await SendResolutionNotificationAsync(alert, cancellationToken);

                _logger.LogInformation("Alert {AlertId} resolved by {ResolvedBy}: {Resolution}",
                    alertId, resolvedBy, resolution);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to resolve alert {AlertId}", alertId);
                throw;
            }
        }

        public async Task AcknowledgeAlertAsync(Guid alertId, string acknowledgedBy, CancellationToken cancellationToken = default)
        {
            try
            {
                var alert = await GetAlertAsync(alertId, cancellationToken);
                if (alert == null)
                {
                    _logger.LogWarning("Cannot acknowledge alert {AlertId} - alert not found", alertId);
                    return;
                }

                if (alert.Status == AlertStatus.Acknowledged)
                {
                    _logger.LogWarning("Alert {AlertId} is already acknowledged", alertId);
                    return;
                }

                alert.Status = AlertStatus.Acknowledged;
                alert.AcknowledgedAt = DateTime.UtcNow;
                alert.AcknowledgedBy = acknowledgedBy;

                alert.Actions.Add(new AlertAction
                {
                    ActionId = Guid.NewGuid(),
                    ActionType = AlertActionType.Acknowledged,
                    PerformedBy = acknowledgedBy,
                    Description = "Alert acknowledged"
                });

                await UpdateAlertAsync(alert, cancellationToken);

                _logger.LogInformation("Alert {AlertId} acknowledged by {AcknowledgedBy}", alertId, acknowledgedBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to acknowledge alert {AlertId}", alertId);
                throw;
            }
        }

        public async Task EscalateAlertAsync(Guid alertId, AlertSeverity newSeverity, string escalatedBy, string reason, CancellationToken cancellationToken = default)
        {
            try
            {
                var alert = await GetAlertAsync(alertId, cancellationToken);
                if (alert == null)
                {
                    _logger.LogWarning("Cannot escalate alert {AlertId} - alert not found", alertId);
                    return;
                }

                if (alert.EscalationLevel >= _alertConfig.MaxEscalationLevel)
                {
                    _logger.LogWarning("Alert {AlertId} has reached maximum escalation level", alertId);
                    return;
                }

                var previousSeverity = alert.Severity;
                alert.Severity = newSeverity;
                alert.EscalationLevel++;
                alert.LastEscalated = DateTime.UtcNow;
                alert.EscalationReason = reason;

                alert.Actions.Add(new AlertAction
                {
                    ActionId = Guid.NewGuid(),
                    ActionType = AlertActionType.Escalated,
                    PerformedBy = escalatedBy,
                    Description = $"Alert escalated from {previousSeverity} to {newSeverity}: {reason}"
                });

                await UpdateAlertAsync(alert, cancellationToken);

                // Send escalation notification
                await SendEscalationNotificationAsync(alert, previousSeverity, cancellationToken);

                _logger.LogInformation("Alert {AlertId} escalated from {PreviousSeverity} to {NewSeverity} by {EscalatedBy}",
                    alertId, previousSeverity, newSeverity, escalatedBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to escalate alert {AlertId}", alertId);
                throw;
            }
        }

        public async Task<AlertStatistics> GetAlertStatisticsAsync(TimeSpan period, CancellationToken cancellationToken = default)
        {
            try
            {
                using var connection = new SqlConnection(GetConnectionString());
                await connection.OpenAsync(cancellationToken);

                var cutoff = DateTime.UtcNow.Subtract(period);
                var statistics = new AlertStatistics
                {
                    GeneratedAt = DateTime.UtcNow,
                    Period = period
                };

                // Get basic counts
                var countSql = @"
                    SELECT 
                        COUNT(*) as TotalAlerts,
                        SUM(CASE WHEN Status = @OpenStatus THEN 1 ELSE 0 END) as OpenAlerts,
                        SUM(CASE WHEN Status = @AcknowledgedStatus THEN 1 ELSE 0 END) as AcknowledgedAlerts,
                        SUM(CASE WHEN Status = @ResolvedStatus THEN 1 ELSE 0 END) as ResolvedAlerts
                    FROM Alerts 
                    WHERE CreatedAt >= @Cutoff";

                using var countCmd = new SqlCommand(countSql, connection);
                countCmd.Parameters.AddWithValue("@Cutoff", cutoff);
                countCmd.Parameters.AddWithValue("@OpenStatus", (int)AlertStatus.Open);
                countCmd.Parameters.AddWithValue("@AcknowledgedStatus", (int)AlertStatus.Acknowledged);
                countCmd.Parameters.AddWithValue("@ResolvedStatus", (int)AlertStatus.Resolved);

                using var countReader = await countCmd.ExecuteReaderAsync(cancellationToken);
                if (await countReader.ReadAsync(cancellationToken))
                {
                    statistics.TotalAlerts = countReader.GetInt32(countReader.GetOrdinal("TotalAlerts"));
                    statistics.OpenAlerts = countReader.GetInt32(countReader.GetOrdinal("OpenAlerts"));
                    statistics.AcknowledgedAlerts = countReader.GetInt32(countReader.GetOrdinal("AcknowledgedAlerts"));
                    statistics.ResolvedAlerts = countReader.GetInt32(countReader.GetOrdinal("ResolvedAlerts"));
                }

                await countReader.CloseAsync();

                // Get severity breakdown
                var severitySql = @"
                    SELECT Severity, COUNT(*) as Count 
                    FROM Alerts 
                    WHERE CreatedAt >= @Cutoff 
                    GROUP BY Severity";

                using var severityCmd = new SqlCommand(severitySql, connection);
                severityCmd.Parameters.AddWithValue("@Cutoff", cutoff);

                using var severityReader = await severityCmd.ExecuteReaderAsync(cancellationToken);
                while (await severityReader.ReadAsync(cancellationToken))
                {
                    var severity = (AlertSeverity)severityReader.GetInt32(severityReader.GetOrdinal("Severity"));
                    var count = severityReader.GetInt32(severityReader.GetOrdinal("Count"));
                    statistics.AlertsBySeverity[severity] = count;
                }

                _logger.LogDebug("Generated alert statistics for period {Period}: {TotalAlerts} total alerts",
                    period, statistics.TotalAlerts);

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate alert statistics");
                throw;
            }
        }

        public async Task SendNotificationAsync(Notification notification, CancellationToken cancellationToken = default)
        {
            try
            {
                notification.NotificationId = notification.NotificationId == Guid.Empty ? Guid.NewGuid() : notification.NotificationId;
                notification.CreatedAt = DateTime.UtcNow;

                foreach (var channelId in notification.Channels)
                {
                    if (_channels.TryGetValue(channelId, out var channel) && channel.IsEnabled)
                    {
                        foreach (var recipient in notification.Recipients)
                        {
                            await SendToChannelAsync(notification, channel, recipient, cancellationToken);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Notification channel {ChannelId} not found or disabled", channelId);
                    }
                }

                _logger.LogInformation("Notification {NotificationId} sent to {ChannelCount} channels, {RecipientCount} recipients",
                    notification.NotificationId, notification.Channels.Count, notification.Recipients.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification {NotificationId}", notification.NotificationId);
                throw;
            }
        }

        public async Task<List<NotificationChannel>> GetNotificationChannelsAsync(CancellationToken cancellationToken = default)
        {
            return await Task.FromResult(_channels.Values.ToList());
        }

        public async Task ConfigureNotificationRulesAsync(List<NotificationRule> rules, CancellationToken cancellationToken = default)
        {
            try
            {
                _notificationRules.Clear();
                foreach (var rule in rules)
                {
                    _notificationRules.TryAdd(rule.RuleId, rule);
                }

                _logger.LogInformation("Configured {RuleCount} notification rules", rules.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to configure notification rules");
                throw;
            }
        }

        public async Task TestNotificationChannelAsync(string channelId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (!_channels.TryGetValue(channelId, out var channel))
                {
                    throw new ArgumentException($"Channel {channelId} not found");
                }

                var testNotification = new Notification
                {
                    Type = NotificationType.Information,
                    Priority = NotificationPriority.Low,
                    Title = "Test Notification",
                    Message = $"This is a test notification for channel {channel.Name}",
                    Recipients = new List<string> { "<EMAIL>" },
                    Channels = new List<string> { channelId }
                };

                await SendToChannelAsync(testNotification, channel, "<EMAIL>", cancellationToken);

                _logger.LogInformation("Test notification sent to channel {ChannelId}", channelId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to test notification channel {ChannelId}", channelId);
                throw;
            }
        }

        private async Task InitializeAlertTableAsync()
        {
            try
            {
                using var connection = new SqlConnection(GetConnectionString());
                await connection.OpenAsync();

                var createTableSql = @"
                    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Alerts')
                    BEGIN
                        CREATE TABLE Alerts (
                            AlertId UNIQUEIDENTIFIER PRIMARY KEY,
                            AlertType NVARCHAR(255) NOT NULL,
                            Severity INT NOT NULL,
                            Status INT NOT NULL,
                            Title NVARCHAR(500) NOT NULL,
                            Description NVARCHAR(MAX) NOT NULL,
                            Source NVARCHAR(255) NOT NULL,
                            Component NVARCHAR(255) NOT NULL,
                            CreatedAt DATETIME2 NOT NULL,
                            AcknowledgedAt DATETIME2,
                            ResolvedAt DATETIME2,
                            AcknowledgedBy NVARCHAR(255),
                            ResolvedBy NVARCHAR(255),
                            Resolution NVARCHAR(MAX),
                            Metadata NVARCHAR(MAX),
                            Actions NVARCHAR(MAX),
                            Tags NVARCHAR(MAX),
                            EscalationLevel INT NOT NULL DEFAULT 0,
                            LastEscalated DATETIME2,
                            EscalationReason NVARCHAR(MAX),
                            SuppressNotifications BIT NOT NULL DEFAULT 0
                        );

                        CREATE INDEX IX_Alerts_CreatedAt ON Alerts(CreatedAt);
                        CREATE INDEX IX_Alerts_Status ON Alerts(Status);
                        CREATE INDEX IX_Alerts_Severity ON Alerts(Severity);
                        CREATE INDEX IX_Alerts_Component ON Alerts(Component);
                        CREATE INDEX IX_Alerts_Source ON Alerts(Source);
                    END";

                using var cmd = new SqlCommand(createTableSql, connection);
                await cmd.ExecuteNonQueryAsync();

                _logger.LogDebug("Alerts table initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize alerts table");
                throw;
            }
        }

        private void InitializeNotificationChannels()
        {
            // Email channel
            _channels.TryAdd("email", new NotificationChannel
            {
                ChannelId = "email",
                Name = "Email",
                Type = NotificationChannelType.Email,
                IsEnabled = true,
                SupportedNotificationTypes = new List<string> { "Alert", "Information", "Warning" },
                HealthStatus = NotificationChannelHealthStatus.Healthy
            });

            // SignalR channel for real-time notifications
            _channels.TryAdd("signalr", new NotificationChannel
            {
                ChannelId = "signalr",
                Name = "SignalR Hub",
                Type = NotificationChannelType.SignalR,
                IsEnabled = true,
                SupportedNotificationTypes = new List<string> { "Alert", "Information", "Warning" },
                HealthStatus = NotificationChannelHealthStatus.Healthy
            });

            // Database channel for persistent notifications
            _channels.TryAdd("database", new NotificationChannel
            {
                ChannelId = "database",
                Name = "Database Storage",
                Type = NotificationChannelType.Database,
                IsEnabled = true,
                SupportedNotificationTypes = new List<string> { "Alert", "Information", "Warning" },
                HealthStatus = NotificationChannelHealthStatus.Healthy
            });

            _logger.LogDebug("Initialized {ChannelCount} notification channels", _channels.Count);
        }

        private void InitializeDefaultNotificationRules()
        {
            // Critical alerts rule
            var criticalRule = new NotificationRule
            {
                RuleId = Guid.NewGuid(),
                Name = "Critical Alerts",
                IsEnabled = true,
                Priority = 1,
                Condition = new NotificationRuleCondition
                {
                    AlertSeverities = new List<AlertSeverity> { AlertSeverity.Critical }
                },
                Channels = new List<string> { "email", "signalr", "database" },
                Recipients = new List<string> { "<EMAIL>" },
                NotificationPriority = NotificationPriority.Urgent,
                CreatedBy = "System"
            };

            // Warning alerts rule
            var warningRule = new NotificationRule
            {
                RuleId = Guid.NewGuid(),
                Name = "Warning Alerts",
                IsEnabled = true,
                Priority = 2,
                Condition = new NotificationRuleCondition
                {
                    AlertSeverities = new List<AlertSeverity> { AlertSeverity.Warning }
                },
                Channels = new List<string> { "signalr", "database" },
                Recipients = new List<string> { "<EMAIL>" },
                NotificationPriority = NotificationPriority.Normal,
                Throttle = TimeSpan.FromMinutes(15),
                CreatedBy = "System"
            };

            _notificationRules.TryAdd(criticalRule.RuleId, criticalRule);
            _notificationRules.TryAdd(warningRule.RuleId, warningRule);

            _logger.LogDebug("Initialized {RuleCount} default notification rules", _notificationRules.Count);
        }

        private async Task<Alert?> CheckForDuplicateAlertAsync(Alert alert, CancellationToken cancellationToken)
        {
            var cutoff = DateTime.UtcNow.Subtract(_alertConfig.DeduplicationWindow);
            return _recentAlerts
                .Where(a => a.CreatedAt >= cutoff)
                .FirstOrDefault(a => IsDuplicateAlert(a, alert));
        }

        private bool IsDuplicateAlert(Alert existing, Alert newAlert)
        {
            foreach (var field in _alertConfig.DeduplicationFields)
            {
                var existingValue = GetAlertFieldValue(existing, field);
                var newValue = GetAlertFieldValue(newAlert, field);
                if (!string.Equals(existingValue, newValue, StringComparison.OrdinalIgnoreCase))
                {
                    return false;
                }
            }
            return true;
        }

        private string GetAlertFieldValue(Alert alert, string fieldName)
        {
            return fieldName.ToLower() switch
            {
                "alerttype" => alert.AlertType,
                "component" => alert.Component,
                "source" => alert.Source,
                "title" => alert.Title,
                _ => string.Empty
            };
        }

        private void ApplySeverityConfiguration(Alert alert)
        {
            if (_alertConfig.SeverityConfigurations.TryGetValue(alert.Severity, out var config))
            {
                if (config.DefaultRecipients.Any() && !alert.Metadata.ContainsKey("Recipients"))
                {
                    alert.Metadata["Recipients"] = config.DefaultRecipients;
                }

                if (config.DefaultChannels.Any() && !alert.Metadata.ContainsKey("Channels"))
                {
                    alert.Metadata["Channels"] = config.DefaultChannels;
                }
            }
        }

        private async Task ProcessNotificationRulesAsync(Alert alert, CancellationToken cancellationToken)
        {
            if (alert.SuppressNotifications)
            {
                _logger.LogDebug("Notifications suppressed for alert {AlertId}", alert.AlertId);
                return;
            }

            var applicableRules = _notificationRules.Values
                .Where(rule => rule.IsEnabled && RuleMatches(rule, alert))
                .OrderBy(rule => rule.Priority)
                .ToList();

            foreach (var rule in applicableRules)
            {
                try
                {
                    var notification = CreateNotificationFromRule(alert, rule);
                    await SendNotificationAsync(notification, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process notification rule {RuleName} for alert {AlertId}",
                        rule.Name, alert.AlertId);
                }
            }
        }

        private bool RuleMatches(NotificationRule rule, Alert alert)
        {
            var condition = rule.Condition;

            if (condition.AlertSeverities?.Any() == true && !condition.AlertSeverities.Contains(alert.Severity))
                return false;

            if (condition.AlertTypes?.Any() == true && !condition.AlertTypes.Contains(alert.AlertType))
                return false;

            if (condition.Sources?.Any() == true && !condition.Sources.Contains(alert.Source))
                return false;

            if (condition.Components?.Any() == true && !condition.Components.Contains(alert.Component))
                return false;

            if (condition.Tags?.Any() == true && !condition.Tags.Intersect(alert.Tags).Any())
                return false;

            return true;
        }

        private Notification CreateNotificationFromRule(Alert alert, NotificationRule rule)
        {
            var notification = new Notification
            {
                Type = NotificationType.Alert,
                Priority = rule.NotificationPriority,
                Title = $"Alert: {alert.Title}",
                Message = alert.Description,
                Recipients = rule.Recipients,
                Channels = rule.Channels,
                RelatedAlertId = alert.AlertId,
                Template = rule.MessageTemplate
            };

            // Add alert metadata to notification
            notification.Metadata["AlertId"] = alert.AlertId;
            notification.Metadata["AlertType"] = alert.AlertType;
            notification.Metadata["Severity"] = alert.Severity.ToString();
            notification.Metadata["Source"] = alert.Source;
            notification.Metadata["Component"] = alert.Component;

            return notification;
        }

        private async Task SendToChannelAsync(Notification notification, NotificationChannel channel, string recipient, CancellationToken cancellationToken)
        {
            var delivery = new NotificationDelivery
            {
                DeliveryId = Guid.NewGuid(),
                Channel = channel.ChannelId,
                Recipient = recipient,
                Status = NotificationDeliveryStatus.Pending,
                AttemptedAt = DateTime.UtcNow,
                AttemptNumber = 1
            };

            try
            {
                switch (channel.Type)
                {
                    case NotificationChannelType.Email:
                        await SendEmailNotificationAsync(notification, recipient);
                        break;
                    case NotificationChannelType.SignalR:
                        await SendSignalRNotificationAsync(notification, recipient);
                        break;
                    case NotificationChannelType.Database:
                        await SendDatabaseNotificationAsync(notification, recipient);
                        break;
                    default:
                        _logger.LogWarning("Unsupported notification channel type: {ChannelType}", channel.Type);
                        delivery.Status = NotificationDeliveryStatus.Failed;
                        delivery.ErrorMessage = $"Unsupported channel type: {channel.Type}";
                        break;
                }

                if (delivery.Status == NotificationDeliveryStatus.Pending)
                {
                    delivery.Status = NotificationDeliveryStatus.Sent;
                    delivery.DeliveredAt = DateTime.UtcNow;
                }

                notification.Deliveries.Add(delivery);
            }
            catch (Exception ex)
            {
                delivery.Status = NotificationDeliveryStatus.Failed;
                delivery.ErrorMessage = ex.Message;
                notification.Deliveries.Add(delivery);
                _logger.LogError(ex, "Failed to send notification to {Channel} for {Recipient}", channel.ChannelId, recipient);
            }
        }

        private async Task SendEmailNotificationAsync(Notification notification, string recipient)
        {
            // Email implementation would go here
            // For now, just log
            _logger.LogInformation("Email notification sent to {Recipient}: {Title}", recipient, notification.Title);
        }

        private async Task SendSignalRNotificationAsync(Notification notification, string recipient)
        {
            // SignalR implementation would go here
            // For now, just log
            _logger.LogInformation("SignalR notification sent to {Recipient}: {Title}", recipient, notification.Title);
        }

        private async Task SendDatabaseNotificationAsync(Notification notification, string recipient)
        {
            // Database storage implementation would go here
            // For now, just log
            _logger.LogInformation("Database notification stored for {Recipient}: {Title}", recipient, notification.Title);
        }

        private async Task StoreAlertAsync(Alert alert, CancellationToken cancellationToken)
        {
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            var sql = @"
                INSERT INTO Alerts (
                    AlertId, AlertType, Severity, Status, Title, Description, Source, Component,
                    CreatedAt, AcknowledgedAt, ResolvedAt, AcknowledgedBy, ResolvedBy, Resolution,
                    Metadata, Actions, Tags, EscalationLevel, LastEscalated, EscalationReason, SuppressNotifications
                ) VALUES (
                    @AlertId, @AlertType, @Severity, @Status, @Title, @Description, @Source, @Component,
                    @CreatedAt, @AcknowledgedAt, @ResolvedAt, @AcknowledgedBy, @ResolvedBy, @Resolution,
                    @Metadata, @Actions, @Tags, @EscalationLevel, @LastEscalated, @EscalationReason, @SuppressNotifications
                )";

            using var cmd = new SqlCommand(sql, connection);
            AddAlertParameters(cmd, alert);

            await cmd.ExecuteNonQueryAsync(cancellationToken);
        }

        private async Task UpdateAlertAsync(Alert alert, CancellationToken cancellationToken)
        {
            using var connection = new SqlConnection(GetConnectionString());
            await connection.OpenAsync(cancellationToken);

            var sql = @"
                UPDATE Alerts SET
                    Status = @Status,
                    AcknowledgedAt = @AcknowledgedAt,
                    ResolvedAt = @ResolvedAt,
                    AcknowledgedBy = @AcknowledgedBy,
                    ResolvedBy = @ResolvedBy,
                    Resolution = @Resolution,
                    Actions = @Actions,
                    EscalationLevel = @EscalationLevel,
                    LastEscalated = @LastEscalated,
                    EscalationReason = @EscalationReason,
                    Severity = @Severity
                WHERE AlertId = @AlertId";

            using var cmd = new SqlCommand(sql, connection);
            AddAlertParameters(cmd, alert);

            await cmd.ExecuteNonQueryAsync(cancellationToken);
        }

        private async Task UpdateExistingAlertAsync(Alert existing, Alert newAlert, CancellationToken cancellationToken)
        {
            // Update the existing alert with new information
            existing.Actions.Add(new AlertAction
            {
                ActionId = Guid.NewGuid(),
                ActionType = AlertActionType.CommentAdded,
                PerformedBy = "System",
                Description = $"Duplicate alert detected. Original: {newAlert.Description}"
            });

            await UpdateAlertAsync(existing, cancellationToken);
        }

        private async Task SendResolutionNotificationAsync(Alert alert, CancellationToken cancellationToken)
        {
            var notification = new Notification
            {
                Type = NotificationType.Success,
                Priority = NotificationPriority.Normal,
                Title = $"Alert Resolved: {alert.Title}",
                Message = $"Alert {alert.AlertId} has been resolved: {alert.Resolution}",
                Recipients = new List<string> { "<EMAIL>" },
                Channels = new List<string> { "signalr", "database" },
                RelatedAlertId = alert.AlertId
            };

            await SendNotificationAsync(notification, cancellationToken);
        }

        private async Task SendEscalationNotificationAsync(Alert alert, AlertSeverity previousSeverity, CancellationToken cancellationToken)
        {
            var notification = new Notification
            {
                Type = NotificationType.Escalation,
                Priority = NotificationPriority.High,
                Title = $"Alert Escalated: {alert.Title}",
                Message = $"Alert {alert.AlertId} has been escalated from {previousSeverity} to {alert.Severity}: {alert.EscalationReason}",
                Recipients = new List<string> { "<EMAIL>" },
                Channels = new List<string> { "email", "signalr", "database" },
                RelatedAlertId = alert.AlertId
            };

            await SendNotificationAsync(notification, cancellationToken);
        }

        private void ProcessPendingAlerts(object? state)
        {
            // Background processing of pending alerts would go here
            // This could handle auto-resolution, escalation checks, etc.
        }

        private void ProcessEscalations(object? state)
        {
            // Background processing of escalations would go here
            // This could handle automatic escalation based on time thresholds
        }

        private string BuildAlertsQuery(AlertFilter? filter)
        {
            var sql = @"
                SELECT AlertId, AlertType, Severity, Status, Title, Description, Source, Component,
                       CreatedAt, AcknowledgedAt, ResolvedAt, AcknowledgedBy, ResolvedBy, Resolution,
                       Metadata, Actions, Tags, EscalationLevel, LastEscalated, EscalationReason, SuppressNotifications
                FROM Alerts
                WHERE 1=1";

            if (filter != null)
            {
                if (filter.Severities?.Any() == true)
                    sql += " AND Severity IN (" + string.Join(",", filter.Severities.Select((_, i) => $"@Severity{i}")) + ")";
                if (filter.Statuses?.Any() == true)
                    sql += " AND Status IN (" + string.Join(",", filter.Statuses.Select((_, i) => $"@Status{i}")) + ")";
                if (filter.AlertTypes?.Any() == true)
                    sql += " AND AlertType IN (" + string.Join(",", filter.AlertTypes.Select((_, i) => $"@AlertType{i}")) + ")";
                if (filter.Sources?.Any() == true)
                    sql += " AND Source IN (" + string.Join(",", filter.Sources.Select((_, i) => $"@Source{i}")) + ")";
                if (filter.Components?.Any() == true)
                    sql += " AND Component IN (" + string.Join(",", filter.Components.Select((_, i) => $"@Component{i}")) + ")";
                if (filter.CreatedAfter.HasValue)
                    sql += " AND CreatedAt >= @CreatedAfter";
                if (filter.CreatedBefore.HasValue)
                    sql += " AND CreatedAt <= @CreatedBefore";

                sql += $" ORDER BY {filter.OrderBy} {(filter.OrderDescending ? "DESC" : "ASC")}";
                sql += " OFFSET @Skip ROWS FETCH NEXT @Take ROWS ONLY";
            }
            else
            {
                sql += " ORDER BY CreatedAt DESC";
            }

            return sql;
        }

        private void AddAlertFilterParameters(SqlCommand cmd, AlertFilter? filter)
        {
            if (filter == null) return;

            if (filter.Severities?.Any() == true)
            {
                for (int i = 0; i < filter.Severities.Count; i++)
                {
                    cmd.Parameters.AddWithValue($"@Severity{i}", (int)filter.Severities[i]);
                }
            }

            if (filter.Statuses?.Any() == true)
            {
                for (int i = 0; i < filter.Statuses.Count; i++)
                {
                    cmd.Parameters.AddWithValue($"@Status{i}", (int)filter.Statuses[i]);
                }
            }

            if (filter.AlertTypes?.Any() == true)
            {
                for (int i = 0; i < filter.AlertTypes.Count; i++)
                {
                    cmd.Parameters.AddWithValue($"@AlertType{i}", filter.AlertTypes[i]);
                }
            }

            if (filter.Sources?.Any() == true)
            {
                for (int i = 0; i < filter.Sources.Count; i++)
                {
                    cmd.Parameters.AddWithValue($"@Source{i}", filter.Sources[i]);
                }
            }

            if (filter.Components?.Any() == true)
            {
                for (int i = 0; i < filter.Components.Count; i++)
                {
                    cmd.Parameters.AddWithValue($"@Component{i}", filter.Components[i]);
                }
            }

            if (filter.CreatedAfter.HasValue)
                cmd.Parameters.AddWithValue("@CreatedAfter", filter.CreatedAfter.Value);
            if (filter.CreatedBefore.HasValue)
                cmd.Parameters.AddWithValue("@CreatedBefore", filter.CreatedBefore.Value);

            cmd.Parameters.AddWithValue("@Skip", filter.Skip);
            cmd.Parameters.AddWithValue("@Take", filter.Take);
        }

        private void AddAlertParameters(SqlCommand cmd, Alert alert)
        {
            cmd.Parameters.AddWithValue("@AlertId", alert.AlertId);
            cmd.Parameters.AddWithValue("@AlertType", alert.AlertType);
            cmd.Parameters.AddWithValue("@Severity", (int)alert.Severity);
            cmd.Parameters.AddWithValue("@Status", (int)alert.Status);
            cmd.Parameters.AddWithValue("@Title", alert.Title);
            cmd.Parameters.AddWithValue("@Description", alert.Description);
            cmd.Parameters.AddWithValue("@Source", alert.Source);
            cmd.Parameters.AddWithValue("@Component", alert.Component);
            cmd.Parameters.AddWithValue("@CreatedAt", alert.CreatedAt);
            cmd.Parameters.AddWithValue("@AcknowledgedAt", (object?)alert.AcknowledgedAt ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@ResolvedAt", (object?)alert.ResolvedAt ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@AcknowledgedBy", (object?)alert.AcknowledgedBy ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@ResolvedBy", (object?)alert.ResolvedBy ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Resolution", (object?)alert.Resolution ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@Metadata", JsonSerializer.Serialize(alert.Metadata));
            cmd.Parameters.AddWithValue("@Actions", JsonSerializer.Serialize(alert.Actions));
            cmd.Parameters.AddWithValue("@Tags", JsonSerializer.Serialize(alert.Tags));
            cmd.Parameters.AddWithValue("@EscalationLevel", alert.EscalationLevel);
            cmd.Parameters.AddWithValue("@LastEscalated", (object?)alert.LastEscalated ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@EscalationReason", (object?)alert.EscalationReason ?? DBNull.Value);
            cmd.Parameters.AddWithValue("@SuppressNotifications", alert.SuppressNotifications);
        }

        private Alert ReadAlertFromReader(SqlDataReader reader)
        {
            return new Alert
            {
                AlertId = reader.GetGuid(reader.GetOrdinal("AlertId")),
                AlertType = reader.GetString(reader.GetOrdinal("AlertType")),
                Severity = (AlertSeverity)reader.GetInt32(reader.GetOrdinal("Severity")),
                Status = (AlertStatus)reader.GetInt32(reader.GetOrdinal("Status")),
                Title = reader.GetString(reader.GetOrdinal("Title")),
                Description = reader.GetString(reader.GetOrdinal("Description")),
                Source = reader.GetString(reader.GetOrdinal("Source")),
                Component = reader.GetString(reader.GetOrdinal("Component")),
                CreatedAt = reader.GetDateTime(reader.GetOrdinal("CreatedAt")),
                AcknowledgedAt = reader.IsDBNull(reader.GetOrdinal("AcknowledgedAt")) ? null : reader.GetDateTime(reader.GetOrdinal("AcknowledgedAt")),
                ResolvedAt = reader.IsDBNull(reader.GetOrdinal("ResolvedAt")) ? null : reader.GetDateTime(reader.GetOrdinal("ResolvedAt")),
                AcknowledgedBy = reader.IsDBNull(reader.GetOrdinal("AcknowledgedBy")) ? null : reader.GetString(reader.GetOrdinal("AcknowledgedBy")),
                ResolvedBy = reader.IsDBNull(reader.GetOrdinal("ResolvedBy")) ? null : reader.GetString(reader.GetOrdinal("ResolvedBy")),
                Resolution = reader.IsDBNull(reader.GetOrdinal("Resolution")) ? null : reader.GetString(reader.GetOrdinal("Resolution")),
                Metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(reader.GetString(reader.GetOrdinal("Metadata"))) ?? new(),
                Actions = JsonSerializer.Deserialize<List<AlertAction>>(reader.GetString(reader.GetOrdinal("Actions"))) ?? new(),
                Tags = JsonSerializer.Deserialize<List<string>>(reader.GetString(reader.GetOrdinal("Tags"))) ?? new(),
                EscalationLevel = reader.GetInt32(reader.GetOrdinal("EscalationLevel")),
                LastEscalated = reader.IsDBNull(reader.GetOrdinal("LastEscalated")) ? null : reader.GetDateTime(reader.GetOrdinal("LastEscalated")),
                EscalationReason = reader.IsDBNull(reader.GetOrdinal("EscalationReason")) ? null : reader.GetString(reader.GetOrdinal("EscalationReason")),
                SuppressNotifications = reader.GetBoolean(reader.GetOrdinal("SuppressNotifications"))
            };
        }

        public void Dispose()
        {
            _alertProcessingTimer?.Dispose();
            _escalationTimer?.Dispose();
        }
    }
}
