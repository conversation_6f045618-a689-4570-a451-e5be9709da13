using Microsoft.Data.SqlClient;
using System.Data;
using System.Data.Common;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder
{
    /// <summary>
    /// Default implementation of database connection factory using SQL Server
    /// </summary>
    public class DatabaseConnectionFactory : IDatabaseConnectionFactory
    {
        private readonly IEnvironmentConfigurationService _environmentService;

        public DatabaseConnectionFactory(IEnvironmentConfigurationService environmentService)
        {
            _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        }

        /// <summary>
        /// Creates a new SQL Server connection using the current environment configuration
        /// </summary>
        public DbConnection CreateConnection()
        {
            var connectionString = _environmentService.CurrentMigrationConfiguration.DatabaseConnection;
            return new SqlConnection(connectionString);
        }

        /// <summary>
        /// Creates a new SQL Server connection using the specified connection string
        /// </summary>
        public DbConnection CreateConnection(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                throw new ArgumentException("Connection string cannot be null or empty", nameof(connectionString));
            }

            return new SqlConnection(connectionString);
        }
    }
}
