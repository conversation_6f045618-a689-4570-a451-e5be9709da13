using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using XQ360.DataMigration.Models;

namespace XQ360.DataMigration.Web.Services.TransactionManagement
{
    /// <summary>
    /// Implementation of distributed transaction service
    /// </summary>
    public class DistributedTransactionService : IDistributedTransactionService
    {
        private readonly ILogger<DistributedTransactionService> _logger;
        private readonly MigrationConfiguration _config;
        private readonly Dictionary<Guid, IDistributedTransaction> _activeTransactions;

        public DistributedTransactionService(
            ILogger<DistributedTransactionService> logger,
            IOptions<MigrationConfiguration> config)
        {
            _logger = logger;
            _config = config.Value;
            _activeTransactions = new Dictionary<Guid, IDistributedTransaction>();
        }

        public async Task<IDistributedTransaction> BeginTransactionAsync(DistributedTransactionOptions options, CancellationToken cancellationToken = default)
        {
            var transactionId = Guid.NewGuid();
            _logger.LogInformation("Beginning distributed transaction {TransactionId}", transactionId);

            try
            {
                var connectionString = !string.IsNullOrEmpty(options.ConnectionString) 
                    ? options.ConnectionString 
                    : _config.DatabaseConnection;

                var connection = new SqlConnection(connectionString);
                await connection.OpenAsync(cancellationToken);

                var sqlTransaction = connection.BeginTransaction(MapIsolationLevel(options.IsolationLevel));

                var transaction = new DistributedTransaction(
                    transactionId,
                    connection,
                    sqlTransaction,
                    options,
                    _logger);

                _activeTransactions[transactionId] = transaction;

                _logger.LogDebug("Distributed transaction {TransactionId} started successfully", transactionId);
                return transaction;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to begin distributed transaction {TransactionId}", transactionId);
                throw;
            }
        }

        public async Task<DistributedTransactionResult> ExecuteInTransactionAsync<T>(
            Func<IDistributedTransaction, CancellationToken, Task<T>> operation,
            DistributedTransactionOptions options,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new DistributedTransactionResult();

            using var transaction = await BeginTransactionAsync(options, cancellationToken);
            result.TransactionId = transaction.TransactionId;

            try
            {
                _logger.LogInformation("Executing operation in distributed transaction {TransactionId}", transaction.TransactionId);

                var operationResult = await operation(transaction, cancellationToken);

                if (options.ValidateConsistency)
                {
                    var consistencyResult = await ValidateConsistencyAsync(transaction.TransactionId, cancellationToken);
                    if (!consistencyResult.IsConsistent)
                    {
                        _logger.LogWarning("Consistency validation failed for transaction {TransactionId}", transaction.TransactionId);
                        result.Warnings.AddRange(consistencyResult.Issues.Select(i => i.Description));
                    }
                }

                await transaction.CommitAsync(cancellationToken);
                
                result.Success = true;
                result.FinalStatus = TransactionStatus.Committed;
                result.Metadata["OperationResult"] = operationResult;

                _logger.LogInformation("Distributed transaction {TransactionId} completed successfully", transaction.TransactionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in distributed transaction {TransactionId}", transaction.TransactionId);
                
                try
                {
                    await transaction.RollbackAsync(cancellationToken);
                    result.FinalStatus = TransactionStatus.RolledBack;
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "Failed to rollback transaction {TransactionId}", transaction.TransactionId);
                    result.FinalStatus = TransactionStatus.Failed;
                }

                result.Success = false;
                result.Errors.Add(ex.Message);
            }
            finally
            {
                result.Duration = stopwatch.Elapsed;
                _activeTransactions.Remove(transaction.TransactionId);
            }

            return result;
        }

        public async Task<DistributedTransactionResult> CoordinateOperationsAsync(
            IEnumerable<ITransactionOperation> operations,
            DistributedTransactionOptions options,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new DistributedTransactionResult();
            var operationList = operations.OrderBy(o => o.Priority).ToList();

            using var transaction = await BeginTransactionAsync(options, cancellationToken);
            result.TransactionId = transaction.TransactionId;

            try
            {
                _logger.LogInformation("Coordinating {OperationCount} operations in transaction {TransactionId}", 
                    operationList.Count, transaction.TransactionId);

                var savepoints = new List<ISavepoint>();

                foreach (var operation in operationList)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        // Create savepoint if enabled
                        ISavepoint? savepoint = null;
                        if (options.EnableSavepoints)
                        {
                            savepoint = await transaction.CreateSavepointAsync($"sp_{operation.OperationId}", cancellationToken);
                            savepoints.Add(savepoint);
                        }

                        // Validate operation
                        var validation = await operation.ValidateAsync(transaction, cancellationToken);
                        if (!validation.IsValid)
                        {
                            result.Errors.AddRange(validation.Errors);
                            result.Warnings.AddRange(validation.Warnings);
                            
                            if (validation.Errors.Any())
                            {
                                throw new InvalidOperationException($"Operation {operation.OperationId} validation failed: {string.Join(", ", validation.Errors)}");
                            }
                        }

                        // Execute operation
                        var operationResult = await operation.ExecuteAsync(transaction, cancellationToken);
                        result.OperationResults.Add(operationResult);

                        if (!operationResult.Success)
                        {
                            throw new InvalidOperationException($"Operation {operation.OperationId} failed: {operationResult.ErrorMessage}");
                        }

                        // Add compensating action if enabled
                        if (options.EnableCompensatingActions)
                        {
                            var compensatingAction = operation.CreateCompensatingAction();
                            await transaction.AddCompensatingActionAsync(compensatingAction, cancellationToken);
                        }

                        _logger.LogDebug("Operation {OperationId} completed successfully in {Duration}ms", 
                            operation.OperationId, operationResult.Duration.TotalMilliseconds);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Operation {OperationId} failed in transaction {TransactionId}", 
                            operation.OperationId, transaction.TransactionId);

                        // Try to rollback to savepoint if available
                        var lastSavepoint = savepoints.LastOrDefault();
                        if (lastSavepoint != null && options.EnableSavepoints)
                        {
                            try
                            {
                                await transaction.RollbackToSavepointAsync(lastSavepoint, cancellationToken);
                                _logger.LogInformation("Rolled back to savepoint {SavepointName}", lastSavepoint.Name);
                            }
                            catch (Exception savepointEx)
                            {
                                _logger.LogError(savepointEx, "Failed to rollback to savepoint {SavepointName}", lastSavepoint.Name);
                            }
                        }

                        throw;
                    }
                }

                // Validate consistency before commit
                if (options.ValidateConsistency)
                {
                    var consistencyResult = await ValidateConsistencyAsync(transaction.TransactionId, cancellationToken);
                    if (!consistencyResult.IsConsistent)
                    {
                        result.Warnings.AddRange(consistencyResult.Issues.Select(i => i.Description));
                        _logger.LogWarning("Consistency issues detected but proceeding with commit");
                    }
                }

                // Commit transaction
                await transaction.CommitAsync(cancellationToken);
                
                result.Success = true;
                result.FinalStatus = TransactionStatus.Committed;

                _logger.LogInformation("Coordinated transaction {TransactionId} completed successfully with {OperationCount} operations", 
                    transaction.TransactionId, operationList.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Coordinated transaction {TransactionId} failed", transaction.TransactionId);
                
                try
                {
                    await transaction.RollbackAsync(cancellationToken);
                    result.FinalStatus = TransactionStatus.RolledBack;
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "Failed to rollback coordinated transaction {TransactionId}", transaction.TransactionId);
                    result.FinalStatus = TransactionStatus.Failed;
                }

                result.Success = false;
                result.Errors.Add(ex.Message);
            }
            finally
            {
                result.Duration = stopwatch.Elapsed;
                _activeTransactions.Remove(transaction.TransactionId);
            }

            return result;
        }

        public async Task<ICompensatingTransaction> CreateCompensatingTransactionAsync(
            IEnumerable<ICompensatingAction> actions,
            CancellationToken cancellationToken = default)
        {
            var compensatingTransactionId = Guid.NewGuid();
            var originalTransactionId = Guid.NewGuid(); // This would typically be passed in

            _logger.LogInformation("Creating compensating transaction {CompensatingTransactionId} for original transaction {OriginalTransactionId}", 
                compensatingTransactionId, originalTransactionId);

            return new CompensatingTransaction(
                compensatingTransactionId,
                originalTransactionId,
                actions.ToList(),
                _logger);
        }

        public async Task<ConsistencyValidationResult> ValidateConsistencyAsync(
            Guid transactionId,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogDebug("Validating consistency for transaction {TransactionId}", transactionId);

            try
            {
                var result = new ConsistencyValidationResult
                {
                    ValidatedAt = DateTime.UtcNow
                };

                // Get the active transaction
                if (!_activeTransactions.TryGetValue(transactionId, out var transaction))
                {
                    result.IsConsistent = false;
                    result.Issues.Add(new ConsistencyIssue
                    {
                        IssueId = "TRANSACTION_NOT_FOUND",
                        Type = ConsistencyIssueType.StateInconsistency,
                        Description = $"Transaction {transactionId} not found in active transactions",
                        AffectedSystem = "TransactionManager"
                    });
                    return result;
                }

                // Validate SQL Server consistency
                var sqlConsistency = await ValidateSqlConsistencyAsync(transaction, cancellationToken);
                result.SystemStatuses["SqlServer"] = sqlConsistency.Status;
                result.Issues.AddRange(sqlConsistency.Issues);

                // Validate API consistency (placeholder - would integrate with actual API validation)
                var apiConsistency = await ValidateApiConsistencyAsync(transaction, cancellationToken);
                result.SystemStatuses["XQ360API"] = apiConsistency.Status;
                result.Issues.AddRange(apiConsistency.Issues);

                // Overall consistency determination
                result.IsConsistent = result.Issues.All(i => i.Type != ConsistencyIssueType.DataMismatch && 
                                                             i.Type != ConsistencyIssueType.StateInconsistency);

                result.ValidationDuration = stopwatch.Elapsed;

                _logger.LogDebug("Consistency validation completed for transaction {TransactionId}: {IsConsistent} ({Duration}ms)", 
                    transactionId, result.IsConsistent, result.ValidationDuration.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating consistency for transaction {TransactionId}", transactionId);
                
                return new ConsistencyValidationResult
                {
                    IsConsistent = false,
                    ValidatedAt = DateTime.UtcNow,
                    ValidationDuration = stopwatch.Elapsed,
                    Issues = new List<ConsistencyIssue>
                    {
                        new ConsistencyIssue
                        {
                            IssueId = "VALIDATION_ERROR",
                            Type = ConsistencyIssueType.StateInconsistency,
                            Description = $"Consistency validation failed: {ex.Message}",
                            AffectedSystem = "ValidationService"
                        }
                    }
                };
            }
        }

        private System.Data.IsolationLevel MapIsolationLevel(IsolationLevel isolationLevel)
        {
            return isolationLevel switch
            {
                IsolationLevel.ReadUncommitted => System.Data.IsolationLevel.ReadUncommitted,
                IsolationLevel.ReadCommitted => System.Data.IsolationLevel.ReadCommitted,
                IsolationLevel.RepeatableRead => System.Data.IsolationLevel.RepeatableRead,
                IsolationLevel.Serializable => System.Data.IsolationLevel.Serializable,
                IsolationLevel.Snapshot => System.Data.IsolationLevel.Snapshot,
                _ => System.Data.IsolationLevel.ReadCommitted
            };
        }

        private async Task<(ConsistencyStatus Status, List<ConsistencyIssue> Issues)> ValidateSqlConsistencyAsync(
            IDistributedTransaction transaction,
            CancellationToken cancellationToken)
        {
            var issues = new List<ConsistencyIssue>();
            
            try
            {
                // Validate SQL transaction state
                if (transaction.SqlTransaction.Connection?.State != System.Data.ConnectionState.Open)
                {
                    issues.Add(new ConsistencyIssue
                    {
                        IssueId = "SQL_CONNECTION_CLOSED",
                        Type = ConsistencyIssueType.StateInconsistency,
                        Description = "SQL connection is not open",
                        AffectedSystem = "SqlServer"
                    });
                }

                // Additional SQL consistency checks would go here
                // For example: checking for deadlocks, constraint violations, etc.

                return (issues.Any() ? ConsistencyStatus.Inconsistent : ConsistencyStatus.Consistent, issues);
            }
            catch (Exception ex)
            {
                issues.Add(new ConsistencyIssue
                {
                    IssueId = "SQL_VALIDATION_ERROR",
                    Type = ConsistencyIssueType.StateInconsistency,
                    Description = $"SQL consistency validation failed: {ex.Message}",
                    AffectedSystem = "SqlServer"
                });
                
                return (ConsistencyStatus.Unknown, issues);
            }
        }

        private async Task<(ConsistencyStatus Status, List<ConsistencyIssue> Issues)> ValidateApiConsistencyAsync(
            IDistributedTransaction transaction,
            CancellationToken cancellationToken)
        {
            var issues = new List<ConsistencyIssue>();
            
            try
            {
                // Placeholder for API consistency validation
                // In a real implementation, this would check:
                // - API endpoint availability
                // - Data synchronization status
                // - Pending operations queue
                
                return (ConsistencyStatus.Consistent, issues);
            }
            catch (Exception ex)
            {
                issues.Add(new ConsistencyIssue
                {
                    IssueId = "API_VALIDATION_ERROR",
                    Type = ConsistencyIssueType.StateInconsistency,
                    Description = $"API consistency validation failed: {ex.Message}",
                    AffectedSystem = "XQ360API"
                });
                
                return (ConsistencyStatus.Unknown, issues);
            }
        }
    }
}
