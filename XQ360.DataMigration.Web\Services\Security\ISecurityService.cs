namespace XQ360.DataMigration.Web.Services.Security
{
    public interface ISecurityService
    {
        Task<string> EncryptConfigurationValueAsync(string value, CancellationToken cancellationToken = default);
        Task<string> DecryptConfigurationValueAsync(string encryptedValue, CancellationToken cancellationToken = default);
        Task<bool> ValidateApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);
        Task<UserPermissions> GetUserPermissionsAsync(string userId, CancellationToken cancellationToken = default);
        Task<bool> HasPermissionAsync(string userId, string permission, CancellationToken cancellationToken = default);
        Task<SecurityReport> GenerateSecurityReportAsync(CancellationToken cancellationToken = default);
        Task LogSecurityEventAsync(SecurityEvent securityEvent, CancellationToken cancellationToken = default);
    }

    public class UserPermissions
    {
        public string UserId { get; set; } = string.Empty;
        public List<string> Roles { get; set; } = new();
        public List<string> Permissions { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class SecurityReport
    {
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public List<SecurityRecommendation> Recommendations { get; set; } = new();
        public SecurityConfiguration Configuration { get; set; } = new();
        public List<SecurityEvent> RecentEvents { get; set; } = new();
    }

    public class SecurityRecommendation
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public SecuritySeverity Severity { get; set; }
        public string Action { get; set; } = string.Empty;
    }

    public class SecurityConfiguration
    {
        public bool EncryptionEnabled { get; set; }
        public bool AuditingEnabled { get; set; }
        public bool AccessControlEnabled { get; set; }
        public List<string> SecurityProtocols { get; set; } = new();
    }

    public class SecurityEvent
    {
        public Guid EventId { get; set; }
        public SecurityEventType EventType { get; set; }
        public SecuritySeverity Severity { get; set; }
        public string UserId { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    public enum SecurityEventType
    {
        AccessGranted,
        AccessDenied,
        Authentication,
        Authorization,
        ConfigurationChange,
        SecurityViolation
    }

    public enum SecuritySeverity
    {
        Low,
        Medium,
        High,
        Critical
    }
}
