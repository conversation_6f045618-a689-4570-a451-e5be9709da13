using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Web.Models;

namespace XQ360.DataMigration.Tests
{
    /// <summary>
    /// Unit tests for TempTableApiOrchestrationService IoT workflow functionality
    /// Tests the complete 3-phase IoT workflow: device creation → vehicle creation → device synchronization
    /// </summary>
    public class TempTableApiOrchestrationServiceTests : IDisposable
    {
        private readonly Mock<ILogger<TempTableApiOrchestrationService>> _mockLogger;
        private readonly Mock<ITempStagingService> _mockTempStagingService;
        private readonly Mock<IApiOrchestrationService> _mockApiOrchestrationService;
        private readonly Mock<IIoTDeviceCreationService> _mockIoTDeviceCreationService;
        private readonly Mock<IOptions<BulkSeederConfiguration>> _mockConfig;
        private readonly Mock<IEnvironmentConfigurationService> _mockEnvironmentService;
        private readonly BulkSeederConfiguration _testConfig;
        private readonly MigrationConfiguration _testMigrationConfig;

        public TempTableApiOrchestrationServiceTests()
        {
            _mockLogger = new Mock<ILogger<TempTableApiOrchestrationService>>();
            _mockTempStagingService = new Mock<ITempStagingService>();
            _mockApiOrchestrationService = new Mock<IApiOrchestrationService>();
            _mockIoTDeviceCreationService = new Mock<IIoTDeviceCreationService>();
            _mockConfig = new Mock<IOptions<BulkSeederConfiguration>>();
            _mockEnvironmentService = new Mock<IEnvironmentConfigurationService>();

            _testConfig = new BulkSeederConfiguration
            {
                DefaultBatchSize = 50,
                MaxRetryAttempts = 3,
                RetryDelaySeconds = 1
            };

            _testMigrationConfig = new MigrationConfiguration
            {
                DatabaseConnection = "Server=test;Database=test;Integrated Security=true;",
                ApiBaseUrl = "https://test-api.example.com",
                ApiUsername = "testuser",
                ApiPassword = "testpass"
            };

            _mockConfig.Setup(x => x.Value).Returns(_testConfig);
            _mockEnvironmentService.Setup(x => x.CurrentMigrationConfiguration).Returns(_testMigrationConfig);
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act
            var service = CreateTempTableApiOrchestrationService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new TempTableApiOrchestrationService(
                    null!,
                    _mockTempStagingService.Object,
                    _mockApiOrchestrationService.Object,
                    _mockIoTDeviceCreationService.Object,
                    _mockConfig.Object,
                    _mockEnvironmentService.Object));
        }

        [Fact]
        public void Constructor_WithNullIoTDeviceCreationService_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new TempTableApiOrchestrationService(
                    _mockLogger.Object,
                    _mockTempStagingService.Object,
                    _mockApiOrchestrationService.Object,
                    null!,
                    _mockConfig.Object,
                    _mockEnvironmentService.Object));
        }

        #endregion

        #region CreateCompleteIoTWorkflowAsync Tests

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WithValidRequests_ShouldReturnSuccessResult()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = CreateTestModuleRequests(5);
            var vehicleRequests = CreateTestVehicleRequests(5);

            SetupSuccessfulIoTWorkflow();

            // Act
            var result = await service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests, batchSize: 10);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(5, result.TotalModuleRequests);
            Assert.Equal(5, result.TotalVehicleRequests);
            Assert.False(result.DryRun);
            Assert.NotNull(result.ModuleCreationResult);
            Assert.True(result.VehicleCreationSuccess);
            Assert.NotNull(result.SyncResult);
            Assert.Contains("IoT workflow completed", result.Summary);
        }

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WithDryRun_ShouldReturnSuccessWithoutApiCalls()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = CreateTestModuleRequests(3);
            var vehicleRequests = CreateTestVehicleRequests(3);

            // Act
            var result = await service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests, batchSize: 10, dryRun: true);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(3, result.TotalModuleRequests);
            Assert.Equal(3, result.TotalVehicleRequests);
            Assert.True(result.DryRun);
            Assert.True(result.VehicleCreationSuccess);
            Assert.Contains("Dry run completed", result.Summary);

            // Verify no actual API calls were made
            _mockIoTDeviceCreationService.Verify(x => x.CreateIoTDeviceBatchAsync(
                It.IsAny<IEnumerable<IoTDeviceCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()), Times.Never);

            _mockIoTDeviceCreationService.Verify(x => x.SyncVehicleIoTSettingsAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WhenPhase1Fails_ShouldStopAndReturnFailure()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = CreateTestModuleRequests(3);
            var vehicleRequests = CreateTestVehicleRequests(3);

            SetupFailedModuleCreation();

            // Act
            var result = await service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.Equal(3, result.TotalModuleRequests);
            Assert.Equal(3, result.TotalVehicleRequests);
            Assert.NotNull(result.ModuleCreationResult);
            Assert.False(result.ModuleCreationResult.Success);
            Assert.Contains("Phase 1 failed", result.Summary);

            // Verify subsequent phases were not executed
            _mockIoTDeviceCreationService.Verify(x => x.SyncVehicleIoTSettingsAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WithEmptyRequests_ShouldReturnSuccessWithZeroCounts()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = new List<IoTDeviceCreateRequest>();
            var vehicleRequests = new List<VehicleCreateRequest>();

            // Act
            var result = await service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.Equal(0, result.TotalModuleRequests);
            Assert.Equal(0, result.TotalVehicleRequests);
            Assert.True(result.VehicleCreationSuccess);
        }

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WithCancellation_ShouldStopProcessing()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = CreateTestModuleRequests(5);
            var vehicleRequests = CreateTestVehicleRequests(5);
            var cancellationTokenSource = new CancellationTokenSource();

            // Setup to cancel during module creation
            _mockIoTDeviceCreationService.Setup(x => x.CreateIoTDeviceBatchAsync(
                It.IsAny<IEnumerable<IoTDeviceCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .Returns(async (IEnumerable<IoTDeviceCreateRequest> requests, int batchSize, CancellationToken ct) =>
                {
                    cancellationTokenSource.Cancel();
                    ct.ThrowIfCancellationRequested();
                    return new IoTDeviceCreationResult { Success = true };
                });

            // Act & Assert
            await Assert.ThrowsAsync<OperationCanceledException>(() =>
                service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests, cancellationToken: cancellationTokenSource.Token));
        }

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WithBatchProcessing_ShouldRespectBatchSize()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = CreateTestModuleRequests(10);
            var vehicleRequests = CreateTestVehicleRequests(10);

            SetupSuccessfulIoTWorkflow();

            // Act
            var result = await service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests, batchSize: 3);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);

            // Verify batch size was passed to IoT device creation
            _mockIoTDeviceCreationService.Verify(x => x.CreateIoTDeviceBatchAsync(
                It.IsAny<IEnumerable<IoTDeviceCreateRequest>>(),
                3, // Should use the specified batch size
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CreateCompleteIoTWorkflowAsync_WhenSyncFails_ShouldReturnPartialFailure()
        {
            // Arrange
            var service = CreateTempTableApiOrchestrationService();
            var moduleRequests = CreateTestModuleRequests(3);
            var vehicleRequests = CreateTestVehicleRequests(3);

            SetupSuccessfulModuleCreation();
            SetupFailedSync();

            // Act
            var result = await service.CreateCompleteIoTWorkflowAsync(moduleRequests, vehicleRequests);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success); // Overall failure due to sync failure
            Assert.NotNull(result.ModuleCreationResult);
            Assert.True(result.ModuleCreationResult.Success); // Phase 1 succeeded
            Assert.True(result.VehicleCreationSuccess); // Phase 2 succeeded
            Assert.NotNull(result.SyncResult);
            Assert.False(result.SyncResult.Success); // Phase 3 failed
        }

        #endregion

        #region Helper Methods

        private TempTableApiOrchestrationService CreateTempTableApiOrchestrationService()
        {
            return new TempTableApiOrchestrationService(
                _mockLogger.Object,
                _mockTempStagingService.Object,
                _mockApiOrchestrationService.Object,
                _mockIoTDeviceCreationService.Object,
                _mockConfig.Object,
                _mockEnvironmentService.Object);
        }

        private List<IoTDeviceCreateRequest> CreateTestModuleRequests(int count)
        {
            var requests = new List<IoTDeviceCreateRequest>();
            for (int i = 1; i <= count; i++)
            {
                requests.Add(new IoTDeviceCreateRequest
                {
                    IoTDeviceID = $"TEST_IOT_{i:D3}",
                    Dealer = "Test Dealer",
                    CCID = 1000 + i,
                    RANumber = 2000 + i,
                    TechNumber = 3000 + i
                });
            }
            return requests;
        }

        private List<VehicleCreateRequest> CreateTestVehicleRequests(int count)
        {
            var requests = new List<VehicleCreateRequest>();
            for (int i = 1; i <= count; i++)
            {
                requests.Add(new VehicleCreateRequest
                {
                    SerialNo = $"TEST_VEH_{i:D3}",
                    HireNo = $"HIRE_{i:D3}",
                    IdleTimer = 300,
                    OnHire = true,
                    ImpactLockout = false,
                    TimeoutEnabled = true,
                    IsCanbus = i % 2 == 0,
                    ModelId = Guid.NewGuid(),
                    SiteId = Guid.NewGuid(),
                    DepartmentId = Guid.NewGuid(),
                    CustomerId = Guid.NewGuid(),
                    ModuleIoTDevice = $"TEST_IOT_{i:D3}",
                    ChecklistType = ChecklistType.TimeBased
                });
            }
            return requests;
        }

        private void SetupSuccessfulIoTWorkflow()
        {
            SetupSuccessfulModuleCreation();
            SetupSuccessfulSync();
        }

        private void SetupSuccessfulModuleCreation()
        {
            var moduleCreationResult = new IoTDeviceCreationResult
            {
                Success = true,
                TotalRequests = 5,
                SuccessfulRequests = 5,
                FailedRequests = 0,
                Results = new Dictionary<string, IoTDeviceApiResult>
                {
                    { "TEST_IOT_001_Test Dealer", new IoTDeviceApiResult { Success = true, IoTDeviceID = "TEST_IOT_001" } },
                    { "TEST_IOT_002_Test Dealer", new IoTDeviceApiResult { Success = true, IoTDeviceID = "TEST_IOT_002" } },
                    { "TEST_IOT_003_Test Dealer", new IoTDeviceApiResult { Success = true, IoTDeviceID = "TEST_IOT_003" } },
                    { "TEST_IOT_004_Test Dealer", new IoTDeviceApiResult { Success = true, IoTDeviceID = "TEST_IOT_004" } },
                    { "TEST_IOT_005_Test Dealer", new IoTDeviceApiResult { Success = true, IoTDeviceID = "TEST_IOT_005" } }
                }
            };

            _mockIoTDeviceCreationService.Setup(x => x.CreateIoTDeviceBatchAsync(
                It.IsAny<IEnumerable<IoTDeviceCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(moduleCreationResult);
        }

        private void SetupFailedModuleCreation()
        {
            var moduleCreationResult = new IoTDeviceCreationResult
            {
                Success = false,
                TotalRequests = 3,
                SuccessfulRequests = 0,
                FailedRequests = 3,
                Errors = new List<string> { "Module creation failed" }
            };

            _mockIoTDeviceCreationService.Setup(x => x.CreateIoTDeviceBatchAsync(
                It.IsAny<IEnumerable<IoTDeviceCreateRequest>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(moduleCreationResult);
        }

        private void SetupSuccessfulSync()
        {
            var syncResult = new IoTDeviceSyncResult
            {
                Success = true,
                TotalDevices = 5,
                SuccessfulSyncs = 5,
                FailedSyncs = 0,
                SyncedDeviceIds = new List<string> { "TEST_IOT_001", "TEST_IOT_002", "TEST_IOT_003", "TEST_IOT_004", "TEST_IOT_005" }
            };

            _mockIoTDeviceCreationService.Setup(x => x.SyncVehicleIoTSettingsAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(syncResult);
        }

        private void SetupFailedSync()
        {
            var syncResult = new IoTDeviceSyncResult
            {
                Success = false,
                TotalDevices = 3,
                SuccessfulSyncs = 0,
                FailedSyncs = 3,
                Errors = new List<string> { "Sync operation failed" }
            };

            _mockIoTDeviceCreationService.Setup(x => x.SyncVehicleIoTSettingsAsync(
                It.IsAny<IEnumerable<string>>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(syncResult);
        }

        public void Dispose()
        {
            // Cleanup any resources if needed
        }

        #endregion
    }
}
