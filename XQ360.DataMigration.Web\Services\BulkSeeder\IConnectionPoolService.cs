using Microsoft.Data.SqlClient;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Connection Pool Optimization Service for Phase 1.2.3
/// Dedicated connection pools for staging vs production operations
/// Performance target: Min 10, Max 100 connections, 30-second idle timeout
/// </summary>
public interface IConnectionPoolService : IDisposable
{
    /// <summary>
    /// Gets an optimized connection for staging operations
    /// Uses dedicated staging connection pool with high concurrency support
    /// </summary>
    Task<SqlConnection> GetStagingConnectionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an optimized connection for production operations
    /// Uses dedicated production connection pool with controlled concurrency
    /// </summary>
    Task<SqlConnection> GetProductionConnectionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Returns a connection to the appropriate pool for reuse
    /// Implements proper connection lifecycle management
    /// </summary>
    Task ReturnConnectionAsync(SqlConnection connection, ConnectionPoolType poolType);

    /// <summary>
    /// Gets current pool statistics and health metrics
    /// Provides insights into connection usage and performance
    /// </summary>
    Task<ConnectionPoolMetrics> GetPoolMetricsAsync();

    /// <summary>
    /// Optimizes pool performance by adjusting pool sizes and cleanup
    /// Maintains target performance under varying load conditions
    /// </summary>
    Task OptimizePoolsAsync();

    /// <summary>
    /// Validates all connections in pools and removes stale connections
    /// Ensures connection health and prevents timeout issues
    /// </summary>
    Task ValidatePoolHealthAsync();

    /// <summary>
    /// Clears all connections for a specific session (cleanup support)
    /// Used for session-based resource cleanup and isolation
    /// </summary>
    Task ClearSessionConnectionsAsync(Guid sessionId);

    /// <summary>
    /// Creates a connection with optimized settings for bulk operations
    /// Applies performance-tuned connection string parameters
    /// </summary>
    Task<SqlConnection> CreateOptimizedConnectionAsync(
        ConnectionPoolType poolType,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Type of connection pool for operation-specific optimization
/// </summary>
public enum ConnectionPoolType
{
    /// <summary>
    /// Pool optimized for staging operations (high concurrency, bulk operations)
    /// </summary>
    Staging,
    
    /// <summary>
    /// Pool optimized for production operations (controlled concurrency, ACID compliance)
    /// </summary>
    Production,
    
    /// <summary>
    /// Pool optimized for read operations (connection sharing, query optimization)
    /// </summary>
    ReadOnly
}

/// <summary>
/// Comprehensive metrics for connection pool monitoring
/// </summary>
public class ConnectionPoolMetrics
{
    public DateTime MetricsTimestamp { get; set; } = DateTime.UtcNow;
    
    // Staging Pool Metrics
    public ConnectionPoolStats StagingPool { get; set; } = new();
    
    // Production Pool Metrics
    public ConnectionPoolStats ProductionPool { get; set; } = new();
    
    // Read-Only Pool Metrics
    public ConnectionPoolStats ReadOnlyPool { get; set; } = new();
    
    // Overall Performance
    public int TotalActiveConnections { get; set; }
    public int TotalIdleConnections { get; set; }
    public int TotalConnectionsCreated { get; set; }
    public int TotalConnectionsDestroyed { get; set; }
    public TimeSpan AverageConnectionCreationTime { get; set; }
    public TimeSpan AverageConnectionAcquisitionTime { get; set; }
    
    // Health Indicators
    public decimal PoolUtilizationPercentage { get; set; }
    public int ConnectionTimeouts { get; set; }
    public int ConnectionLeaks { get; set; }
    public List<string> HealthWarnings { get; set; } = new();
    public List<string> OptimizationRecommendations { get; set; } = new();
}

/// <summary>
/// Statistics for individual connection pool
/// </summary>
public class ConnectionPoolStats
{
    public ConnectionPoolType PoolType { get; set; }
    public string PoolName { get; set; } = string.Empty;
    
    // Configuration
    public int MinConnections { get; set; }
    public int MaxConnections { get; set; }
    public int IdleTimeoutSeconds { get; set; }
    public int LifetimeSeconds { get; set; }
    
    // Current State
    public int ActiveConnections { get; set; }
    public int IdleConnections { get; set; }
    public int TotalConnections { get; set; }
    public int PendingRequests { get; set; }
    
    // Performance Metrics
    public long TotalConnectionsCreated { get; set; }
    public long TotalConnectionsDestroyed { get; set; }
    public long TotalConnectionsReused { get; set; }
    public long TotalRequestsServed { get; set; }
    public TimeSpan AverageAcquisitionTime { get; set; }
    public TimeSpan MaxAcquisitionTime { get; set; }
    
    // Health Metrics
    public int ConnectionTimeouts { get; set; }
    public int ConnectionFailures { get; set; }
    public DateTime LastOptimization { get; set; }
    public decimal UtilizationPercentage => MaxConnections > 0 ? (decimal)TotalConnections / MaxConnections * 100 : 0;
    
    // Recommendations
    public List<string> PerformanceWarnings { get; set; } = new();
    public List<string> ScalingRecommendations { get; set; } = new();
}

/// <summary>
/// Configuration for connection pool optimization
/// </summary>
public class ConnectionPoolConfiguration
{
    public ConnectionPoolType PoolType { get; set; }
    public int MinConnections { get; set; }
    public int MaxConnections { get; set; }
    public int IdleTimeoutSeconds { get; set; }
    public int LifetimeSeconds { get; set; }
    public string ConnectionString { get; set; } = string.Empty;
    public Dictionary<string, object> OptimizationParameters { get; set; } = new();
}

/// <summary>
/// Result of connection pool health validation
/// </summary>
public class PoolHealthResult
{
    public bool IsHealthy { get; set; }
    public List<string> HealthIssues { get; set; } = new();
    public List<string> StaleConnections { get; set; } = new();
    public List<string> RecoveryActions { get; set; } = new();
    public DateTime ValidationTimestamp { get; set; } = DateTime.UtcNow;
    public TimeSpan ValidationDuration { get; set; }
}
